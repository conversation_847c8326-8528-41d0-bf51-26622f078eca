// FILE: '~/src/Pages/campaign/_components/assetsManager.tsx'

import { Button } from '@repo/shared/components/ui/button'
import { Card, CardContent } from '@repo/shared/components/ui/card'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@repo/shared/components/ui/dialog'
import { Label } from '@repo/shared/components/ui/label'
import { Slider } from '@repo/shared/components/ui/slider'
import { toast } from '@repo/shared/components/ui/use-toast'
import { api } from '@repo/shared/lib/hooks/useApi'
import { useOrganizationAssets } from '@repo/shared/lib/hooks/useCampaignAssets'
import { useCurrentOrganization } from '@repo/shared/lib/hooks/useCurrentOrganization'
import { OrganizationAssetDto } from '@repo/shared/lib/types/organizationAsset'
import { getErrorMessage } from '@repo/shared/lib/utils'
import Rive from '@rive-app/react-canvas'
import axios, { CancelTokenSource } from 'axios'
import { AnimatePresence, motion } from 'framer-motion'
import { Loader2, Trash2, UploadCloud, ZoomIn, CopyIcon } from 'lucide-react'
import React, { useCallback, useEffect, useMemo, useState, memo } from 'react'
import { useDropzone } from 'react-dropzone'
import Cropper, { Area, Point } from 'react-easy-crop'
import { Switch } from '../ui/switch'
import { Tooltip, TooltipContent, TooltipTrigger } from '@repo/shared/components/ui/tooltip'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'

// Define a unified type for items (either uploading or uploaded assets)
type AssetItem = (OrganizationAssetDto & { isUploading: false }) | (UploadQueueItem & { isUploading: true })

// UploadQueueItem defines the structure of each upload item
interface UploadQueueItem {
    file: File
    progress: number
    status: 'pending' | 'uploading' | 'completed' | 'error'
    cancel: () => void
    cancelTokenSource: CancelTokenSource
    id: string // Unique identifier, typically the file name
}

// AssetManagerProps defines the props expected by AssetManagerDialog
export interface AssetManagerProps {
    organizationId: string
    children: React.ReactNode
}

// StorageProgressBar displays the storage usage as a progress bar
const StorageProgressBar: React.FC<{
    usedStorage: number
    availableStorage: number
}> = ({ usedStorage, availableStorage }) => {
    const percentage = (usedStorage / availableStorage) * 100

    return (
        <div className="w-full bg-secondary rounded-full h-4 dark:bg-gray-700 overflow-hidden">
            <motion.div className="h-full bg-primary" initial={{ width: 0 }} animate={{ width: `${percentage}%` }} transition={{ duration: 0.5, ease: 'easeOut' }} />
        </div>
    )
}

// ItemComponent handles rendering both uploading and uploaded items
const ItemComponent: React.FC<{
    item: AssetItem
    onCancel?: (id: string) => void
    onDelete?: (id: string) => void
    onCopyUrl?: (url: string) => void
}> = memo(({ item, onCancel, onDelete, onCopyUrl }) => {
    if (item.isUploading) {
        return (
            <motion.div key={item.id} initial={{ opacity: 0, scale: 9 }} animate={{ opacity: 1, scale: 1 }} exit={{ opacity: 0, scale: 0.9 }} transition={{ duration: 0.3 }}>
                <Card className="overflow-hidden group relative">
                    <CardContent className="p-4">
                        <div className="flex">
                            <div className="flex flex-col justify-center">
                                <div className="aspect-square bg-background rounded-lg flex items-center justify-center mb-2 relative w-[100px] h-[100px]">
                                    <Loader2 className="h-8 w-8 animate-spin" />
                                </div>
                            </div>
                            <div className="ps-4 text-wrap flex flex-col justify-center flex-1">
                                <p className="text-sm font-medium">{item.file.name}</p>
                                <p className="text-xs text-muted-foreground">Uploading: {item.progress}%</p>
                                <div className="w-full bg-secondary h-1 mt-2 rounded-full">
                                    <motion.div
                                        className="bg-primary h-full rounded-full"
                                        style={{ width: `${item.progress}%` }}
                                        initial={{ width: 0 }}
                                        animate={{ width: `${item.progress}%` }}
                                        transition={{ duration: 0.3, ease: 'easeOut' }}
                                    />
                                </div>
                            </div>
                            <motion.button
                                className="absolute top-2 right-2 p-1.5 text-destructive rounded-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10"
                                onClick={(e) => {
                                    e.stopPropagation()
                                    onCancel && onCancel(item.id)
                                }}
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                <Trash2 className="h-4 w-4" />
                            </motion.button>
                        </div>
                    </CardContent>
                </Card>
            </motion.div>
        )
    } else {
        return (
            <motion.div key={item.id} initial={{ opacity: 0, scale: 0.9 }} animate={{ opacity: 1, scale: 1 }} exit={{ opacity: 0, scale: 0.9 }} transition={{ duration: 0.3 }}>
                <Card className="overflow-hidden group relative">
                    {/* Action buttons positioned relative to the Card */}
                    <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
                        {/* Copy URL button */}
                        <motion.button
                            className="p-1.5 text-blue-600 rounded-sm bg-white/10 hover:bg-white/20"
                            onClick={(e) => {
                                e.stopPropagation()
                                onCopyUrl && onCopyUrl((item as OrganizationAssetDto).fileUrl)
                            }}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                            title="Copy asset URL"
                        >
                            <CopyIcon className="h-4 w-4" />
                        </motion.button>
                        {/* Delete button */}
                        <motion.button
                            className="p-1.5 text-destructive rounded-sm bg-white/10 hover:bg-white/20"
                            onClick={(e) => {
                                e.stopPropagation()
                                onDelete && onDelete(item.id)
                            }}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                            title="Delete asset"
                        >
                            <Trash2 className="h-4 w-4" />
                        </motion.button>
                    </div>
                    <CardContent className="p-4">
                        <div className="flex">
                            <div className="flex flex-col justify-center">
                                <div className="aspect-square bg-background rounded-lg flex items-center justify-center mb-2 relative w-[100px] h-[100px]">
                                    <AssetThumbnail asset={item} />
                                </div>
                            </div>
                            <div className="ps-4 text-wrap flex flex-col justify-center flex-1">
                                <p className="text-sm font-medium">{(item as OrganizationAssetDto).fileName}</p>
                                <p className="text-xs text-gray-500">{formatFileSize((item as OrganizationAssetDto).fileSize)}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </motion.div>
        )
    }
})

// AssetManagerDialog manages both uploading and displaying assets
const AssetManagerDialog: React.FC<AssetManagerProps> = ({ organizationId, children }) => {
    const [items, setItems] = useState<AssetItem[]>([])
    const { assetsData, isLoadingAssets, assetsError, refetchAssets, deleteAsset } = useOrganizationAssets(organizationId)

    // Initialize items with existing assets when assetsData changes
    useEffect(() => {
        if (assetsData?.assets) {
            const uploadedAssets: AssetItem[] = assetsData.assets.map((asset) => ({
                ...asset,
                isUploading: false,
            }))
            setItems(uploadedAssets)
        }
    }, [assetsData])

    const formatSize = (bytes: number): string => {
        if (bytes <= 0) return '0 B'
        const k = 1024
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const onDrop = useCallback(
        async (acceptedFiles: File[]) => {
            if (acceptedFiles.length === 0) return
            // Create UploadQueueItems and add to items
            const newUploads: UploadQueueItem[] = acceptedFiles.map((file) => {
                const cancelTokenSource = axios.CancelToken.source()
                return {
                    id: file.name, // Using file name as ID; ensure uniqueness or adjust as needed
                    file,
                    progress: 0,
                    status: 'uploading',
                    cancel: () => cancelTokenSource.cancel('Upload cancelled by user'),
                    cancelTokenSource,
                    isUploading: true,
                }
            })
            setItems((prev) => [...newUploads.map((u) => u as AssetItem), ...prev])

            for (const item of newUploads) {
                const formData = new FormData()
                formData.append('file', item.file)

                try {
                    await api.post(`organizations/${organizationId}/assets`, formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                        onUploadProgress: (progressEvent) => {
                            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                            setItems((prevItems) => prevItems.map((p) => (p.isUploading && p.id === item.id ? { ...p, progress: percentCompleted } : p)))
                        },
                        cancelToken: item.cancelTokenSource.token,
                    })

                    toast({
                        title: 'Asset uploaded',
                        description: `${item.file.name} has been successfully uploaded.`,
                    })

                    // Refetch assets to get the latest list
                    await refetchAssets()

                    // Find the uploaded asset in assetsData
                    const uploadedAsset = assetsData?.assets.find((a) => a.fileName === item.file.name)
                    if (uploadedAsset) {
                        // Replace the uploading item with the uploaded asset
                        setItems((prevItems) => prevItems.map((p) => (p.isUploading && p.id === item.id ? { ...uploadedAsset, isUploading: false } : p)))
                    }
                } catch (error: any) {
                    if (!axios.isCancel(error)) {
                        toast({
                            title: 'Upload failed',
                            description: `Failed to upload ${item.file.name}: ${getErrorMessage(error)}`,
                            variant: 'destructive',
                        })

                        // Optionally, update the item's status to 'error' or remove it
                        setItems((prevItems) => prevItems.filter((p) => !(p.isUploading && p.id === item.id)))
                    }
                }
            }
        },
        [organizationId, refetchAssets, assetsData]
    )

    const cancelUpload = useCallback(
        (fileName: string) => {
            const itemToCancel = items.find((item) => item.isUploading && item.id === fileName)
            if (itemToCancel && itemToCancel.isUploading) {
                itemToCancel.cancel()
                setItems((prev) => prev.filter((item) => item.id !== fileName))
                toast({
                    title: 'Upload cancelled',
                    description: `Upload of ${fileName} has been cancelled.`,
                })
            }
        },
        [items]
    )

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        multiple: true,
    })

    const handleDelete = useCallback(
        async (assetId: string) => {
            // Optimistically remove the asset from the list
            const assetToDelete = items.find((item) => !item.isUploading && item.id === assetId)
            setItems((prev) => prev.filter((item) => !(!item.isUploading && item.id === assetId)))

            try {
                await deleteAsset.mutateAsync(assetId)
                toast({
                    title: 'Asset deleted',
                    description: 'The asset has been successfully deleted.',
                })
                refetchAssets()
            } catch (error: any) {
                // If deletion fails, add the asset back to the list
                if (assetToDelete) {
                    setItems((prev) => [...prev, assetToDelete])
                }
                toast({
                    title: 'Delete failed',
                    description: `Failed to delete asset: ${getErrorMessage(error)}`,
                    variant: 'destructive',
                })
            }
        },
        [deleteAsset, refetchAssets, items]
    )

    const handleCopyUrl = useCallback(async (url: string) => {
        try {
            await navigator.clipboard.writeText(url)
            toast({
                title: 'URL copied',
                description: 'Asset URL has been copied to clipboard.',
            })
        } catch (error) {
            toast({
                title: 'Copy failed',
                description: 'Failed to copy URL to clipboard.',
                variant: 'destructive',
            })
        }
    }, [])

    if (isLoadingAssets) return <Loader2 className="h-8 w-8 animate-spin" />
    if (assetsError) return <div>Error loading assets: {assetsError.message}</div>

    return (
        <Dialog>
            <DialogTrigger>{children}</DialogTrigger>
            <DialogContent className="sm:max-w-[900px] max-h-[100vh] overflow-y-auto">
                <div>
                    {assetsData && (
                        <div className="mb-6 p-4 bg-background rounded-lg shadow-inner">
                            <div className="flex justify-between items-center mb-2">
                                <span className="text-sm">
                                    {formatSize(assetsData.usedStorage)} / {formatSize(assetsData.availableStorage)}
                                </span>
                                <span className="text-sm font-medium">{formatSize(assetsData.availableStorage - assetsData.usedStorage)} available</span>
                            </div>
                            <StorageProgressBar usedStorage={assetsData.usedStorage} availableStorage={assetsData.availableStorage} />
                        </div>
                    )}

                    <div className="overflow-y-auto pe-4 mt-3">
                        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-4 mt-6 max-h-[500px]">
                            <AnimatePresence>
                                {items.map((item, i) => (
                                    <ItemComponent 
                                        key={i} 
                                        item={item} 
                                        onCancel={item.isUploading ? cancelUpload : undefined} 
                                        onDelete={!item.isUploading ? handleDelete : undefined}
                                        onCopyUrl={!item.isUploading ? handleCopyUrl : undefined}
                                    />
                                ))}
                            </AnimatePresence>
                        </div>
                    </div>

                    <div
                        {...getRootProps()}
                        className={`border-2 border-dashed bg-background rounded-lg p-8 text-center cursor-pointer transition-colors mt-4 ${
                            isDragActive ? 'border-primary bg-primary/10' : 'border-accent hover:border-primary'
                        }`}
                    >
                        <input {...getInputProps()} />
                        <UploadCloud className="mx-auto h-12 w-12 text-gray-400" />
                        <p className="mt-2 text-sm text-gray-600">Drag 'n' drop some files here, or click to select files</p>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}

interface AssetPickerProps {
    onSelect: (assetId: string | null) => void
    currentAssetId?: string
    assetUrl?: AssetUrl
    extensions?: string[]
    exactSize?: { width: number; height: number }
}

// Add this new component for image cropping
const ImageCropDialog = ({
    isOpen,
    onClose,
    imageUrl,
    targetWidth,
    targetHeight,
    onCropComplete,
    organizationId,
}: {
    isOpen: boolean
    onClose: () => void
    imageUrl: string
    targetWidth: number
    targetHeight: number
    onCropComplete: (newAssetId: string) => void
    organizationId: string
}) => {
    const [crop, setCrop] = useState<Point>({ x: 50, y: 50 })
    const [zoom, setZoom] = useState(1)
    const [maxZoom, setMaxZoom] = useState(6)
    const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null)
    const [imageSize, setImageSize] = useState<{ width: number; height: number } | null>(null)
    const [isProcessing, setIsProcessing] = useState(false)

    // Calculate optimal initial zoom when image loads
    useEffect(() => {
        const img = new Image()
        img.crossOrigin = 'anonymous' // Add CORS here too
        img.src = imageUrl
        img.onload = () => {
            setImageSize({ width: img.width, height: img.height })
        }
    }, [imageUrl])

    // Calculate crop area dimensions with maximum bounds
    const cropSize = useMemo(() => {
        const maxWidth = 580
        const maxHeight = 380
        const ratio = targetWidth / targetHeight
        let width, height

        if (ratio >= 1) {
            width = maxWidth
            height = width / ratio
            if (height > maxHeight) {
                height = maxHeight
                width = height * ratio
            }
        } else {
            height = maxHeight
            width = height * ratio
            if (width > maxWidth) {
                width = maxWidth
                height = width / ratio
            }
        }

        return { width: Math.round(width), height: Math.round(height) }
    }, [targetWidth, targetHeight])

    // Calculate and set optimal zoom when image size is available
    useEffect(() => {
        if (!imageSize) return

        // Calculate zoom required to fit the image to the crop area
        const cropRatio = cropSize.width / cropSize.height
        const imageRatio = imageSize.width / imageSize.height

        let optimalZoom
        if (cropRatio > imageRatio) {
            // Fit to width
            optimalZoom = cropSize.width / imageSize.width
        } else {
            // Fit to height
            optimalZoom = cropSize.height / imageSize.height
        }

        // If optimal zoom is greater than default max zoom (3),
        // set the max zoom to optimal zoom + 10%
        const newMaxZoom = optimalZoom > 6 ? optimalZoom * 1.1 : 6
        setMaxZoom(newMaxZoom)
        setZoom(Math.max(1, optimalZoom))
    }, [imageSize, cropSize])

    const onCropChange = (location: Point) => {
        setCrop(location)
    }

    const onZoomChange = (newZoom: number) => {
        setZoom(newZoom)
    }

    const onCropAreaComplete = (croppedArea: Area, croppedAreaPixels: Area) => {
        setCroppedAreaPixels(croppedAreaPixels)
    }

    const createImage = (url: string): Promise<HTMLImageElement> =>
        new Promise((resolve, reject) => {
            const image = new Image()
            image.crossOrigin = 'anonymous' // Add this line to enable CORS
            image.addEventListener('load', () => resolve(image))
            image.addEventListener('error', (error) => reject(error))
            image.src = url
        })

    const getCroppedImg = async (imageSrc: string, pixelCrop: Area, zoom: number, rotation = 0): Promise<Blob> => {
        const image = await createImage(imageSrc)
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')

        if (!ctx) {
            throw new Error('No 2d context')
        }

        canvas.width = targetWidth
        canvas.height = targetHeight

        // ctx.fillStyle = 'white'
        // ctx.fillRect(0, 0, canvas.width, canvas.height)

        // Draw the cropped image
        ctx.drawImage(image, pixelCrop.x, pixelCrop.y, pixelCrop.width, pixelCrop.height, 0, 0, targetWidth, targetHeight)

        return new Promise((resolve) => {
            canvas.toBlob((blob) => {
                if (!blob) throw new Error('Canvas is empty')
                resolve(blob)
            }, 'image/webp')
        })
    }

    const handleCrop = async () => {
        if (!croppedAreaPixels) return

        try {
            setIsProcessing(true)

            // Get the cropped image as a blob
            const croppedBlob = await getCroppedImg(imageUrl, croppedAreaPixels, zoom)

            // Create a File object from the blob
            const fileName = `cropped_${Date.now()}.${'webp'}` // or .avif
            const croppedFile = new File([croppedBlob], fileName, {
                type: 'image/webp',
            })

            // Upload the cropped image
            const formData = new FormData()
            formData.append('file', croppedFile)

            const response = await api.post(`organizations/${organizationId}/assets`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            })

            // Get the uploaded asset ID from the response
            const uploadedAsset = response.data

            // Pass the new asset ID back
            onCropComplete(uploadedAsset.id)
            onClose()

            toast({
                title: 'Image cropped and uploaded',
                description: 'The cropped image has been uploaded successfully.',
            })
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to crop and upload image: ' + getErrorMessage(error),
                variant: 'destructive',
            })
        } finally {
            setIsProcessing(false)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[700px] h-fit flex flex-col gap-4">
                <DialogHeader>
                    <DialogTitle>
                        Crop Image to {targetWidth}x{targetHeight}
                    </DialogTitle>
                </DialogHeader>
                <div className="relative h-[400px] w-full overflow-hidden bg-background rounded-md">
                    <Cropper
                        image={imageUrl}
                        crop={crop}
                        zoom={zoom}
                        aspect={targetWidth / targetHeight}
                        onCropChange={onCropChange}
                        onZoomChange={onZoomChange}
                        onCropComplete={onCropAreaComplete}
                        cropSize={cropSize}
                        restrictPosition={false}
                        // Removed objectFit property to allow natural zooming behavior
                        classes={{
                            containerClassName: '!relative !h-[400px] !w-full',
                        }}
                        minZoom={0.1} // Allow zooming out further
                    />
                </div>
                <div className="flex items-center gap-4 py-2">
                    <div className="flex items-center gap-2">
                        <ZoomIn className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <Slider value={[zoom]} min={0.5} max={maxZoom} step={0.01} onValueChange={(value) => setZoom(value[0])} className="flex-1" />
                    <div className="w-12 text-sm text-muted-foreground">{Math.round(zoom * 100)}%</div>
                </div>

                <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={onClose} disabled={isProcessing}>
                        Cancel
                    </Button>
                    <Button onClick={handleCrop} disabled={isProcessing}>
                        {isProcessing ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Processing...
                            </>
                        ) : (
                            'Crop and Upload'
                        )}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}

// Modify the AssetPicker component
export const AssetPicker: React.FC<AssetPickerProps> = ({ onSelect, assetUrl, extensions = [], exactSize }) => {
    const { currentOrganization } = useCurrentOrganization()
    const [isOpen, setIsOpen] = useState(false)
    const [showCropDialog, setShowCropDialog] = useState(false)
    const [selectedAsset, setSelectedAsset] = useState<OrganizationAssetDto | null>(null)
    const { assetsData, isLoadingAssets, assetsError, refetchAssets } = useOrganizationAssets(currentOrganization?.id || '')

    const removeAsset = () => {
        onSelect(null)
    }

    const handleAssetSelect = async (asset: OrganizationAssetDto) => {
        if (!exactSize || !asset.contentType.startsWith('image/')) {
            onSelect(asset.id)
            setIsOpen(false)
            return
        }

        // For images when exact size is required
        const img = new Image()
        img.crossOrigin = 'anonymous' // Add CORS support
        img.src = asset.fileUrl
        img.onload = () => {
            if (img.width === exactSize.width && img.height === exactSize.height) {
                onSelect(asset.id)
                setIsOpen(false)
            } else {
                setSelectedAsset(asset)
                setShowCropDialog(true)
                setIsOpen(false)
            }
        }
    }

    const handleCropComplete = (newAssetId: string) => {
        refetchAssets()

        onSelect(newAssetId)
        setShowCropDialog(false)
        setSelectedAsset(null)
    }

    return (
        <div className="space-y-1.5 mt-1">
            <div className="group relative overflow-hidden rounded-md border bg-background transition-colors hover:bg-accent/50 ">
                <div className="flex aspect-video items-center justify-center ">
                    {assetUrl ? (
                        <div className="relative h-full w-full ">
                            <AssetUrlThumbnail asset={assetUrl} className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105" />
                            <div className="absolute inset-0 bg-black/60 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
                                <div className="flex h-full items-center justify-center gap-2">
                                    <Button onClick={() => setIsOpen(true)} variant="secondary" className="bg-white/10 hover:bg-white/20 text-white hover:text-white">
                                        Change Asset
                                    </Button>
                                    <Button onClick={removeAsset} variant="secondary" size="icon" className="bg-white/10 hover:bg-destructive text-white hover:text-white transition-colors">
                                        <Trash2 className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <Button onClick={() => setIsOpen(true)} variant="ghost" className="flex h-full w-full flex-col items-center justify-center gap-2 rounded-none hover:bg-accent/50">
                            <UploadCloud className="h-8 w-8 text-muted-foreground" />
                            <span className="text-xs font-medium text-muted-foreground">Select an asset</span>
                        </Button>
                    )}
                </div>
            </div>

            {/* Rest of the dialogs remain unchanged */}
            {showCropDialog && selectedAsset && exactSize && (
                <ImageCropDialog
                    isOpen={showCropDialog}
                    onClose={() => {
                        setShowCropDialog(false)
                        setSelectedAsset(null)
                    }}
                    imageUrl={selectedAsset.fileUrl}
                    targetWidth={exactSize.width}
                    targetHeight={exactSize.height}
                    onCropComplete={handleCropComplete}
                    organizationId={currentOrganization?.id || ''}
                />
            )}

            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogTrigger asChild>
                    <></>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[675px] z-[10000]">
                    <DialogHeader>
                        <DialogTitle>Select an Asset</DialogTitle>
                    </DialogHeader>
                    <div className="max-h-[500px] overflow-y-auto scroll-sm pe-4">
                        {isLoadingAssets && <div>Loading assets...</div>}
                        {assetsError && <div>Error loading assets: {assetsError.message}</div>}
                        {assetsData && (
                            <div className="grid grid-cols-3 gap-4">
                                {assetsData.assets
                                    .filter((a) => extensions.length === 0 || extensions.some((e) => a.fileName.toLowerCase().endsWith('.' + e.toLowerCase())))
                                    .map((asset) => (
                                        <Card key={asset.id} className={`cursor-pointer transition-all hover:border-primary/50`} onClick={() => handleAssetSelect(asset)}>
                                            <CardContent className="p-2">
                                                <div className="aspect-square bg-background rounded-lg flex items-center justify-center mb-2">
                                                    <AssetThumbnail asset={asset} />
                                                </div>
                                                <p className="text-sm font-medium truncate">
                                                    {asset.fileName} ({asset.contentType})
                                                </p>
                                                <p className="text-xs text-gray-500">{formatFileSize(asset.fileSize)}</p>
                                            </CardContent>
                                        </Card>
                                    ))}
                            </div>
                        )}
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    )
}

// AssetThumbnail component displays the thumbnail of the asset
const AssetThumbnail: React.FC<{
    asset: OrganizationAssetDto | AssetItem
    className?: string
}> = ({ asset, className = '' }) => {
    if (!asset) return <div className="text-4xl">📄</div>
    if ('contentType' in asset && asset.contentType.startsWith('image/')) {
        return <img src={asset.fileUrl} alt={asset.fileName} className={`w-full h-full object-cover rounded-lg ${className}`} />
    }

    if ('fileName' in asset && asset.fileName.toLowerCase().endsWith('.riv')) {
        return <Rive src={asset.fileUrl} className={`w-full h-full object-cover rounded-lg ${className}`} />
    }
    return <div className="text-4xl">📄</div>
}

const AssetUrlThumbnail: React.FC<{
    asset: AssetUrl
    className?: string
}> = ({ asset, className = '' }) => {
    const { currentOrganization } = useCurrentOrganization()
    const { assetsData, isLoadingAssets, assetsError, refetchAssets, deleteAsset } = useOrganizationAssets(currentOrganization?.id)

    const organizationAsset = assetsData?.assets.find((a) => a.id === asset?.assetId)

    if (organizationAsset?.contentType.startsWith('image/')) {
        return <img src={organizationAsset.fileUrl} className={`w-full h-full  rounded-lg ${className}`} style={{ objectFit: 'contain' }} />
    }

    if (organizationAsset?.fileName.endsWith('riv')) {
        return <Rive src={organizationAsset.fileUrl} className={`w-full h-full  rounded-lg ${className}`} style={{ objectFit: 'contain' }} />
    }

    // Check for common image formats in absoluteUrl
    const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.avif', '.svg', '.bmp']
    if (asset?.absoluteUrl && imageExtensions.some((ext) => asset.absoluteUrl?.toLowerCase().endsWith(ext))) {
        return <img src={asset?.absoluteUrl} className={`w-full h-full  rounded-lg ${className}`} style={{ objectFit: 'contain' }} />
    }

    if (asset?.absoluteUrl && asset?.absoluteUrl?.endsWith('.riv')) {
        return <Rive src={asset?.absoluteUrl} className={`w-full h-full  rounded-lg ${className}`} style={{ objectFit: 'contain' }} />
    }

    // Extract filename from either organizationAsset or absoluteUrl
    const getFileName = (): string => {
        if (organizationAsset?.fileName) {
            return organizationAsset.fileName
        }

        if (asset?.absoluteUrl) {
            // Extract filename from URL
            const urlParts = asset.absoluteUrl.split('/')
            return urlParts[urlParts.length - 1]
        }

        return 'Unknown'
    }

    // Truncate filename if longer than 10 characters
    const truncateFileName = (filename: string): string => {
        return filename.length > 20 ? filename.substring(0, 20) + '...' : filename
    }

    const displayFileName = truncateFileName(getFileName())

    return (
        <div className="text-4xl w-full h-full flex items-center justify-center text-center" style={{ objectFit: 'contain' }}>
            <div className="flex flex-col">
                <div>📄</div>
                <div className="text-sm">{displayFileName}</div>
            </div>
        </div>
    )
}

const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export default AssetManagerDialog
