import { CounterElementStyle, GameButtonStyle, GameTextSettings, RewardComponentStyle } from '@repo/shared-game-utils/types/uiStyles'
import { GameConfig, gameConfigKey } from '@repo/shared/lib/game/gameConfig'
import { AssetUrl, SoundAssetUrl } from '@repo/shared/lib/types/widgetSettings'


export type BackgroundStyle = {
    asset?: AssetUrl
    fill?: string
    useBackgroundColor?: boolean
    width?: number
    height?: number
    // Grid settings for slots background
    gridSettings?: {
        tileSpacing?: number
        containerPadding?: number
    }
}


export type SpinButtonStyle = GameButtonStyle & {
    alignment?: 'left' | 'center' | 'right'
}

export type GameSoundSwitchStyle = {
    isVisible?: boolean
    offsetY?: number
    offsetX?: number
    onAsset?: AssetUrl
    offAsset?: AssetUrl
    alignment?: 'left' | 'center' | 'right'
    width?: number
    height?: number
}





export type GameEndHandler = {
    useLives?: boolean
    livesCount?: number
}

export type GameRewardsHandler = {
    rewardsEnabled: boolean
    enableCtaButton?: boolean
}

export class ReactGameConfig extends GameConfig {
    @gameConfigKey({ name: 'Lives Handler', configEditorType: 'lives-handler' })
    gameEndHandler?: GameEndHandler

    @gameConfigKey({ name: 'Rewards Handler', configEditorType: 'rewards-handler' })
    gameRewardsHandler?: GameRewardsHandler

    @gameConfigKey({ name: 'Background Music', configEditorType: 'audio' })
    backgroundMusic?: SoundAssetUrl

    @gameConfigKey({ name: 'Spin Sound', configEditorType: 'audio' })
    spinSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Win Sound', configEditorType: 'audio' })
    winSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Game Over Sound', configEditorType: 'audio' })
    gameOverSound?: SoundAssetUrl

    @gameConfigKey({
        name: 'Main Background',
        configEditorType: 'background',
        width: 800,
        height: 600,
    })
    mainBackground: BackgroundStyle

    @gameConfigKey({
        name: 'Slots Background',
        configEditorType: 'background',
    })
    slotsBackground: BackgroundStyle

    @gameConfigKey({
        name: 'Score Style',
        configEditorType: 'scoreboard-numbers-style',
    })
    scoreStyle: CounterElementStyle

    @gameConfigKey({
        name: 'Lives Style',
        configEditorType: 'counter',
    })
    livesStyle: CounterElementStyle

    @gameConfigKey({
        name: 'Game Over Text',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    gameOverText: GameTextSettings

    @gameConfigKey({
        name: 'Out of Lives Try Again Button',
        configEditorType: 'game-button',
    })
    outOfLivesContinueButton: GameButtonStyle

    @gameConfigKey({ name: 'Lose Life Overlay', configEditorType: 'background' })
    loseLifeOverlay?: BackgroundStyle

    @gameConfigKey({ name: 'Lose Life Title', configEditorType: 'text' })
    loseLifeTitle?: GameTextSettings

    @gameConfigKey({ name: 'Continue Button', configEditorType: 'game-button' })
    continueButton?: GameButtonStyle

    @gameConfigKey({ name: 'Game Over Overlay', configEditorType: 'background' })
    gameOverOverlay?: BackgroundStyle

    @gameConfigKey({ name: 'Game Over Title', configEditorType: 'text' })
    gameOverTitle?: GameTextSettings

    @gameConfigKey({ name: 'Game Over Message', configEditorType: 'text' })
    gameOverMessage?: GameTextSettings

    @gameConfigKey({ name: 'Game Over Continue Button', configEditorType: 'game-button' })
    gameOverContinueButton?: GameButtonStyle

    @gameConfigKey({ name: 'Reward Overlay', configEditorType: 'background' })
    rewardOverlay?: BackgroundStyle

    @gameConfigKey({ name: 'Reward Title', configEditorType: 'text' })
    rewardTitle?: GameTextSettings

    @gameConfigKey({ name: 'Reward Claim Button', configEditorType: 'game-button', editorSettings: { toggleableVisibility: true } })
    rewardClaimButton?: GameButtonStyle

    @gameConfigKey({ name: 'Out of Lives Overlay', configEditorType: 'background' })
    outOfLivesOverlay?: BackgroundStyle

    @gameConfigKey({ name: 'Out of Lives Title', configEditorType: 'text' })
    outOfLivesTitle?: GameTextSettings

    @gameConfigKey({ name: 'Claim Reward Overlay', configEditorType: 'background' })
    claimRewardOverlay?: BackgroundStyle

    @gameConfigKey({ name: 'Claim Reward Title', configEditorType: 'text' })
    claimRewardTitle?: GameTextSettings

    @gameConfigKey({
        name: 'Claim Reward Button',
        configEditorType: 'game-button',
        editorSettings: {
            toggleableVisibility: true,
        },
    })
    claimRewardButton?: GameButtonStyle

    @gameConfigKey({ name: 'Try Again Button', configEditorType: 'game-button' })
    tryAgainButton?: GameButtonStyle

    @gameConfigKey({ name: 'Spin Button', configEditorType: 'spin-game-button' })
    spinButton?: SpinButtonStyle

    @gameConfigKey({ name: 'Sound Switch', configEditorType: 'sound-switch', editorSettings: { toggleableVisibility: true } })
    gameSoundSwitch?: GameSoundSwitchStyle

    @gameConfigKey({ name: 'Reward Component', configEditorType: 'reward-component-style' })
    rewardComponent?: RewardComponentStyle

    @gameConfigKey({ name: 'Slot Symbol 1', configEditorType: 'image' })
    slotSymbol1: AssetUrl

    @gameConfigKey({ name: 'Slot Symbol 2', configEditorType: 'image' })
    slotSymbol2: AssetUrl

    @gameConfigKey({ name: 'Slot Symbol 3', configEditorType: 'image' })
    slotSymbol3: AssetUrl

    @gameConfigKey({ name: 'Slot Symbol 4', configEditorType: 'image' })
    slotSymbol4: AssetUrl

    @gameConfigKey({ name: 'Slot Symbol 5', configEditorType: 'image' })
    slotSymbol5: AssetUrl

    @gameConfigKey({ name: 'Slot Symbol 6', configEditorType: 'image' })
    slotSymbol6: AssetUrl

    @gameConfigKey({ name: 'Slot Symbol 7', configEditorType: 'image' })
    slotSymbol7: AssetUrl

    @gameConfigKey({ name: 'Slot Symbol 8', configEditorType: 'image' })
    slotSymbol8: AssetUrl

    @gameConfigKey({ name: 'Slot Symbol 9', configEditorType: 'image' })
    slotSymbol9: AssetUrl
}

export const defaultGameConfig: ReactGameConfig = {
    gameEndHandler: {
        useLives: false,
        livesCount: 3,
    },

    gameRewardsHandler: {
        rewardsEnabled: false
    },

    backgroundMusic: {
        enabled: true,
        absoluteUrl: 'https://example.com/background-music.mp3',
    },

    spinSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/spin-sound.mp3',
    },

    winSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/win-sound.mp3',
    },

    gameOverSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/game-over-sound.mp3',
    },

    mainBackground: {
        fill: '#faf8ef',
        useBackgroundColor: true,
    },

    slotsBackground: {
        fill: '#1f2937', // Default dark gray color (matches the previous bg-gray-800)
        useBackgroundColor: true,
        width: 400,
        height: 400,
        gridSettings: {
            tileSpacing: 4,
            containerPadding: 16,
        },
    },

    scoreStyle: {
        fontFamily_tFontFamily: 'Poppins',
        fontSize: 32,
        fill: '#776e65',
        textColor: '#f9f6f2',
        textAlign: 'center',
        width: 100,
        height: 60,
        marginBottom: 0,
        padding: {
            top: 5,
            right: 5,
            bottom: 5,
            left: 5,
        },
        borderRadius: 4,
    },

    livesStyle: {
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 22,
        fill: '#f5cb5c',
        textColor: '#000000',
        textAlign: 'center',
        width: 100,
        height: 48,
        marginTop: 0,
        marginRight: 0,
        marginBottom: 0,
        marginLeft: 0,
        padding: {
            top: 5,
            right: 5,
            bottom: 5,
            left: 5,
        },
        useBackgroundColor: true,
        borderRadius: 8,
        alignment: 'left',
    },

    gameOverText: {
        text: 'Game Over!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    continueButton: {
        textConfig: {
            text: 'Ok',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
        isVisible: true,
    },

    outOfLivesContinueButton: {
        textConfig: {
            text: 'Ok',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
        isVisible: true,
    },

    loseLifeOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
    },

    loseLifeTitle: {
        text: 'You Lost a Life',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
    },

    gameOverTitle: {
        text: 'Game Over',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverMessage: {
        text: 'Better luck next time!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 22,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverContinueButton: {
        textConfig: {
            text: 'Continue',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
        isVisible: true,
    },

    rewardOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
    },

    rewardTitle: {
        text: 'You Earned a Reward!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    rewardClaimButton: {
        textConfig: {
            text: 'Claim Reward',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
        isVisible: true,
    },

    outOfLivesOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
    },

    outOfLivesTitle: {
        text: 'Out of Lives',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    claimRewardTitle: {
        text: 'Claim Your Reward or Try Again',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    claimRewardOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
    },

    claimRewardButton: {
        textConfig: {
            text: 'Claim Reward',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
        isVisible: true,
    },

    tryAgainButton: {
        textConfig: {
            text: 'Try Again',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
        isVisible: true,
    },

    spinButton: {
        textConfig: {
            text: 'SPIN',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 22,
                fill: '#000000',
                isVisible: true,
            },
        },
        fill: '#f5cb5c',
        width: 240,
        height: 48,
        offsetX: 0,
        offsetY: 0,
        useBackgroundColor: true,
        borderRadius: 8,
        alignment: 'center',
        isVisible: true,
    },

    gameSoundSwitch: {
        onAsset: {
            absoluteUrl: 'https://placehold.co/48x48/f5cb5c/000000?text=ON',
        },
        offAsset: {
            absoluteUrl: 'https://placehold.co/48x48/f5cb5c/000000?text=OFF',
        },
        width: 48,
        height: 48,
        offsetX: 0,
        offsetY: 0,
        alignment: 'right',
        isVisible: true,
    },

    rewardComponent: {
        imageWidth: 150,
        imageHeight: 150,
        titleFontFamily: 'Londrina Solid',
        titleFontSize: 22,
        titleColor: '#776e65',
        titleTextAlign: 'left',
        descriptionFontFamily: 'Poppins',
        descriptionFontSize: 16,
        descriptionColor: '#776e65',
        descriptionTextAlign: 'left',
        padding: 16,
        maxWidth: 400,
        layout: 'horizontal-left',
        imageBorderRadius: 8,
        imageMargin: 16,
        containerBackgroundColor: 'transparent',
        containerBorderRadius: 12,
        containerShadow: false,
        spacing: 16,
        showImage: true,
        showTitle: true,
        showDescription: true,
    },

    // Default slot symbols (placeholder URLs)
    slotSymbol1: {
        absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=1',
    },
    slotSymbol2: {
        absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=2',
    },
    slotSymbol3: {
        absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=3',
    },
    slotSymbol4: {
        absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=4',
    },
    slotSymbol5: {
        absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=5',
    },
    slotSymbol6: {
        absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=6',
    },
    slotSymbol7: {
        absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=7',
    },
    slotSymbol8: {
        absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=8',
    },
    slotSymbol9: {
        absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=9',
    },
}
