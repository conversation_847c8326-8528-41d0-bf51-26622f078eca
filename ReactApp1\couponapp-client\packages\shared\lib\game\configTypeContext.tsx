import { createContext, ReactNode } from "react"

interface ConfigTypeContextValue {
    configType: any
}

export const ConfigTypeContext = createContext<ConfigTypeContextValue | undefined>(undefined)

interface ConfigTypeProviderProps {
    children: ReactNode
    configType: any
}

export function ConfigTypeProvider({ children, configType }: ConfigTypeProviderProps) {
    return (
        <ConfigTypeContext.Provider value={{ configType }}>
            {children}
        </ConfigTypeContext.Provider>
    )
}