import { GameConfig, gameConfigKey } from '@repo/shared/lib/game/gameConfig'
import { AssetUrl, SoundAssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { 
    GameTextSettings, 
    BackgroundStyle, 
    ContainerStyle, 
    CounterElementStyle, 
    GameButtonStyle, 
    GameSoundSwitchStyle, 
    RewardComponentStyle, 
    GameEndHandler, 
    GameTimerHandler, 
    GameRewardsHandler 
} from '../../../shared-game-utils/types/uiStyles'

export type MemoryCardStyle = {
    asset?: AssetUrl
    fill?: string
    useBackgroundColor?: boolean
}

export type MemoryGridStyle = {
    spacing?: number
    padding?: number
    columns?: number
    rows?: number
    cardWidth?: number
    cardHeight?: number
    cardBorderRadius?: number
    borderRadius?: number
    background?: BackgroundStyle
}

export type TimerElementStyle = CounterElementStyle & {
    // Additional timer-specific properties can be added here
}

export class ReactGameConfig extends GameConfig {
    @gameConfigKey({ name: 'Lives Handler', configEditorType: 'lives-handler' })
    gameEndHandler?: GameEndHandler

    @gameConfigKey({ name: 'Timer Handler', configEditorType: 'counter' })
    gameTimerHandler?: GameTimerHandler

    @gameConfigKey({ name: 'Rewards Handler', configEditorType: 'rewards-handler' })
    gameRewardsHandler?: GameRewardsHandler

    @gameConfigKey({ name: 'Background Music', configEditorType: 'audio' })
    backgroundMusic?: SoundAssetUrl

    @gameConfigKey({ name: 'Win Sound', configEditorType: 'audio' })
    winSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Game Over Sound', configEditorType: 'audio' })
    gameOverSound?: SoundAssetUrl

    @gameConfigKey({
        name: 'Main Background',
        configEditorType: 'background',
        width: 800,
        height: 600,
    })
    mainBackground: BackgroundStyle


    @gameConfigKey({
        name: 'Lives Style',
        configEditorType: 'counter',
    })
    livesStyle: CounterElementStyle

    @gameConfigKey({
        name: 'Timer Style',
        configEditorType: 'counter',
    })
    timerStyle: TimerElementStyle

    @gameConfigKey({
        name: 'Game Over Text',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    gameOverText: GameTextSettings

    @gameConfigKey({
        name: 'Out of Lives Try Again Button',
        configEditorType: 'game-button',
        editorSettings: { toggleableVisibility: true },
    })
    outOfLivesContinueButton: GameButtonStyle

    @gameConfigKey({ name: 'Lose Life Overlay', configEditorType: 'container' })
    loseLifeOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Lose Life Title', configEditorType: 'text' })
    loseLifeTitle?: GameTextSettings

    @gameConfigKey({ name: 'Continue Button', configEditorType: 'game-button' })
    continueButton?: GameButtonStyle

    @gameConfigKey({ name: 'Game Over Overlay', configEditorType: 'container' })
    gameOverOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Game Over Title', configEditorType: 'text' })
    gameOverTitle?: GameTextSettings

    @gameConfigKey({ name: 'Game Over Message', configEditorType: 'text' })
    gameOverMessage?: GameTextSettings

    @gameConfigKey({ name: 'Game Over Continue Button', configEditorType: 'game-button' })
    gameOverContinueButton?: GameButtonStyle

    @gameConfigKey({ name: 'Reward Overlay', configEditorType: 'container' })
    rewardOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Reward Title', configEditorType: 'text' })
    rewardTitle?: GameTextSettings

    @gameConfigKey({ name: 'Reward Claim Button', configEditorType: 'game-button', editorSettings: { toggleableVisibility: true } })
    rewardClaimButton?: GameButtonStyle

    @gameConfigKey({ name: 'Out of Lives Overlay', configEditorType: 'container' })
    outOfLivesOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Out of Lives Title', configEditorType: 'text' })
    outOfLivesTitle?: GameTextSettings

    @gameConfigKey({ name: 'Claim Reward Overlay', configEditorType: 'container' })
    claimRewardOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Claim Reward Title', configEditorType: 'text' })
    claimRewardTitle?: GameTextSettings

    @gameConfigKey({ name: 'Claim Reward Button', configEditorType: 'game-button', editorSettings: { toggleableVisibility: true } })
    claimRewardButton?: GameButtonStyle

    @gameConfigKey({ name: 'Try Again Button', configEditorType: 'game-button', editorSettings: { toggleableVisibility: true }  })
    tryAgainButton?: GameButtonStyle

    @gameConfigKey({ name: 'Reward Component', configEditorType: 'reward-component-style' })
    rewardComponent?: RewardComponentStyle

    @gameConfigKey({ name: 'Sound Switch', configEditorType: 'sound-switch', editorSettings: { toggleableVisibility: true } })
    gameSoundSwitch?: GameSoundSwitchStyle

    @gameConfigKey({ name: 'Card Flip Sound', configEditorType: 'audio' })
    cardFlipSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Card Match Sound', configEditorType: 'audio' })
    cardMatchSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Card Mismatch Sound', configEditorType: 'audio' })
    cardMismatchSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Memory Grid', configEditorType: 'memory-grid' })
    memoryGrid: MemoryGridStyle

    @gameConfigKey({ name: 'Memory Card Back', configEditorType: 'memory-card' })
    memoryCardBack: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 1', configEditorType: 'memory-card' })
    memoryCard1: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 2', configEditorType: 'memory-card' })
    memoryCard2: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 3', configEditorType: 'memory-card' })
    memoryCard3: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 4', configEditorType: 'memory-card' })
    memoryCard4: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 5', configEditorType: 'memory-card' })
    memoryCard5: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 6', configEditorType: 'memory-card' })
    memoryCard6: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 7', configEditorType: 'memory-card' })
    memoryCard7: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 8', configEditorType: 'memory-card' })
    memoryCard8: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 9', configEditorType: 'memory-card' })
    memoryCard9: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 10', configEditorType: 'memory-card' })
    memoryCard10: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 11', configEditorType: 'memory-card' })
    memoryCard11: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 12', configEditorType: 'memory-card' })
    memoryCard12: MemoryCardStyle

    @gameConfigKey({ name: 'Memory Card 13', configEditorType: 'memory-card' })
    memoryCard13: MemoryCardStyle
}

export const defaultGameConfig: ReactGameConfig = {
    gameEndHandler: {
        useLives: false,
        livesCount: 3,
    },

    gameTimerHandler: {
        useTimer: false,
        timerDuration: 60,
        loseLifeOnTimeout: true,
    },

    gameRewardsHandler: {
        rewardsEnabled: false,
    },

    backgroundMusic: {
        enabled: true,
        absoluteUrl: 'https://example.com/background-music.mp3',
    },

    winSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/win-sound.mp3',
    },

    gameOverSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/game-over-sound.mp3',
    },

    mainBackground: {
        fill: '#faf8ef',
        useBackgroundColor: true,
    },

    // Score style removed as score functionality is no longer used

    livesStyle: {
        textConfig: {
            text: '',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 22,
                fill: '#000000',
                textAlign: 'center',
                isVisible: true,
            }
        },
        fill: '#f5cb5c',
        width: 100,
        height: 48,
        useBackgroundColor: true,
        borderRadius: 8,
        offsetX: 0,
        offsetY: 0,
        isVisible: true,
    },

    timerStyle: {
        textConfig: {
            text: '',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 22,
                fill: '#000000',
                textAlign: 'center',
                isVisible: true,
            }
        },
        fill: '#4cc9f0',
        width: 100,
        height: 48,
        useBackgroundColor: true,
        borderRadius: 8,
        offsetX: 0,
        offsetY: 0,
        isVisible: true,
    },

    gameOverText: {
        text: 'Game Over!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    continueButton: {
        textConfig: {
            text: 'Ok',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    outOfLivesContinueButton: {
        textConfig: {
            text: 'Ok',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    loseLifeOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    loseLifeTitle: {
        text: 'You Lost a Life',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
       borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    gameOverTitle: {
        text: 'Game Over',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverMessage: {
        text: 'Better luck next time!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 22,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverContinueButton: {
        textConfig: {
            text: 'Continue',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    rewardOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
       borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    rewardTitle: {
        text: 'You Earned a Reward!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    rewardClaimButton: {
        textConfig: {
            text: 'Claim Reward',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    outOfLivesOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
       borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    outOfLivesTitle: {
        text: 'Out of Lives',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    claimRewardTitle: {
        text: 'Claim Your Reward or Try Again',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    claimRewardOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    claimRewardButton: {
        textConfig: {
            text: 'Claim Reward',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    tryAgainButton: {
        textConfig: {
            text: 'Try Again',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    rewardComponent: {
        imageWidth: 150,
        imageHeight: 150,
        titleFontFamily: 'Londrina Solid',
        titleFontSize: 22,
        titleColor: '#776e65',
        titleTextAlign: 'left',
        descriptionFontFamily: 'Poppins',
        descriptionFontSize: 16,
        descriptionColor: '#776e65',
        descriptionTextAlign: 'left',
        padding: 16,
        maxWidth: 400,
        layout: 'horizontal-left',
        imageBorderRadius: 8,
        imageMargin: 16,
        containerBackgroundColor: 'transparent',
        containerBorderRadius: 12,
        containerShadow: false,
        spacing: 16,
        showImage: true,
        showTitle: true,
        showDescription: true,
    },

    gameSoundSwitch: {
        onAsset: {
            absoluteUrl: 'https://placehold.co/48x48/f5cb5c/000000?text=ON',
        },
        offAsset: {
            absoluteUrl: 'https://placehold.co/48x48/f5cb5c/000000?text=OFF',
        },
        width: 48,
        height: 48,
        offsetX: 0,
        offsetY: 0,
        alignment: 'right',
        isVisible: true,
    },

    cardFlipSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/card-flip-sound.mp3',
    },

    cardMatchSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/card-match-sound.mp3',
    },

    cardMismatchSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/card-mismatch-sound.mp3',
    },

    memoryGrid: {
        spacing: 10,
        padding: 20,
        columns: 4,  // Changed from 3 to 4 to ensure an even number of cells (4x4=16)
        rows: 4,     // Changed from 3 to 4 to ensure an even number of cells
        cardWidth: 100,
        cardHeight: 100,
        cardBorderRadius: 8,
        borderRadius: 12,
        background: {
            fill: 'rgba(0, 0, 0, 0.1)',
            useBackgroundColor: true,
        },
    },

    memoryCardBack: {
        fill: '#8f7a66',
        useBackgroundColor: true,
    },

    memoryCard1: {
        fill: '#eee4da',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/eee4da/776e65?text=1',
        },
    },

    memoryCard2: {
        fill: '#ede0c8',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/ede0c8/776e65?text=2',
        },
    },

    memoryCard3: {
        fill: '#f2b179',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/f2b179/776e65?text=3',
        },
    },

    memoryCard4: {
        fill: '#f59563',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/f59563/776e65?text=4',
        },
    },

    memoryCard5: {
        fill: '#f67c5f',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/f67c5f/776e65?text=5',
        },
    },

    memoryCard6: {
        fill: '#edcf72',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/edcf72/776e65?text=6',
        },
    },

    memoryCard7: {
        fill: '#edcc61',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/edcc61/776e65?text=7',
        },
    },

    memoryCard8: {
        fill: '#edc850',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/edc850/776e65?text=8',
        },
    },

    memoryCard9: {
        fill: '#edc53f',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/edc53f/776e65?text=9',
        },
    },

    memoryCard10: {
        fill: '#edc22e',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/edc22e/776e65?text=10',
        },
    },

    memoryCard11: {
        fill: '#3c3a32',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/3c3a32/f9f6f2?text=11',
        },
    },

    memoryCard12: {
        fill: '#a56941',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/a56941/f9f6f2?text=12',
        },
    },

    memoryCard13: {
        fill: '#6baed6',
        useBackgroundColor: true,
        asset: {
            absoluteUrl: 'https://placehold.co/100x100/6baed6/f9f6f2?text=13',
        },
    },
}
