import Phaser from "phaser";
import { ReactGameConfig } from "../types/config";
import { MainScene } from "./scenes/MainScene";
import { AssetUrl } from "@repo/shared/lib/types/widgetSettings";

export class SimpleGame {
  public game: Phaser.Game;
  private scene: MainScene;
  private gameConfig: ReactGameConfig;

  constructor(
    parent: HTMLDivElement,
    config: ReactGameConfig,
    resolveAssetUrl: (id: AssetUrl) => string | undefined
  ) {
    this.gameConfig = config;


    const phaserConfig: Phaser.Types.Core.GameConfig = {
      parent: parent,
      type: Phaser.CANVAS,
      scale: {
        mode: Phaser.Scale.FIT,
        autoCenter: Phaser.Scale.CENTER_BOTH,
        width: 900,
        height: 1600,
      },
      fps: {
        min: 60
      },
     transparent: true,
      physics: {
        default: 'arcade', 
        arcade: {
          gravity: { x: 0, y: 1100 },
          debug: false,
        },
      }, 
      scene: [],
    };

    this.game = new Phaser.Game(phaserConfig);
    
    // Create and add the main scene
    this.scene = new MainScene(this.gameConfig, resolveAssetUrl);
    this.game.scene.add('MainScene', this.scene, true);
  }

  public destroy() {
    if (this.game) {
      this.game.destroy(true);
    }
  }
} 