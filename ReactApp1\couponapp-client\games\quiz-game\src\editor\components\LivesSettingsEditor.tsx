import React from 'react'
import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import { Slider } from '@repo/shared/components/ui/slider'
import { Heart, Info } from 'lucide-react'
import { GameEndHandler } from '@repo/shared-game-utils/types/uiStyles'
interface LivesSettingsEditorProps {
    config: any
    onChange: (config: any) => void
}

export const LivesSettingsEditor: React.FC<LivesSettingsEditorProps> = ({ config, onChange }) => {
    const livesHandler = (config.gameEndHandler || {}) as GameEndHandler

    const useLives = livesHandler.useLives ?? false
    const livesCount = livesHandler.livesCount ?? 3

    const handleLivesToggle = (enabled: boolean) => {
        onChange({
            gameEndHandler: {
                ...livesHandler,
                useLives: enabled,
            },
        })
    }

    const handleLivesCountChange = (value: number[]) => {
        onChange({
            gameEndHandler: {
                ...livesHandler,
                livesCount: value[0],
            },
        })
    }

    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                    <Heart className="h-4 w-4 text-red-500" />
                    <Label htmlFor="lives-system" className="text-sm font-medium">Lives System</Label>
                </div>
                <Switch
                    id="lives-system"
                    checked={useLives}
                    onCheckedChange={handleLivesToggle}
                />
            </div>

            <div className="flex items-start gap-2 text-xs text-muted-foreground">
                <Info className="h-3 w-3 mt-0.5 flex-shrink-0" />
                <span>Limit how many times players can attempt the quiz before needing to restart</span>
            </div>

            {useLives && (
                <div className="space-y-3 pl-6 border-l-2 border-muted">
                    <div className="space-y-2">
                        <div className="flex items-center justify-between">
                            <Label htmlFor="lives-count" className="text-sm">Starting Lives</Label>
                            <span className="text-sm font-medium">
                                {livesCount}
                            </span>
                        </div>
                        <Slider
                            id="lives-count"
                            min={1}
                            max={20}
                            step={1}
                            value={[livesCount]}
                            onValueChange={handleLivesCountChange}
                            className="w-full"
                        />
                        <div className="text-xs text-muted-foreground">
                            Players start with {livesCount} {livesCount === 1 ? 'life' : 'lives'}
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}
