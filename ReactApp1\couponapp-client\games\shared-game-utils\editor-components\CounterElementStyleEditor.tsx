import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { Input } from '@repo/shared/components/ui/input'
import { Slider } from '@repo/shared/components/ui/slider'
import { getConfigKeyDefinition } from '@repo/shared/lib/game/gameConfig'
import { ButtonIconStyleEditor } from './ButtonIconStyleEditor'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { useConfigKeyDefinition } from '../hooks/useConfigType'
import { TextStyleEditor } from './TextStyleEditor'
import { TextStyle } from '../types/uiStyles'
interface CounterElementStyleEditorProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
}

export function CounterElementStyleEditor({ configKey, config, onChange }: CounterElementStyleEditorProps) {

     if(!config) {
        return <div>tf</div>
    }

        const definition = useConfigKeyDefinition(configKey)

    return (
        <div className="space-y-4">
            {definition?.editorSettings?.toggleableVisibility && (
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Visibility</Label>
                    <Switch
                        checked={(config[configKey] as any)?.isVisible !== false}
                        onCheckedChange={(isVisible) =>
                            onChange({
                                [configKey]: { ...config[configKey], isVisible },
                            })
                        }
                    />
                </div>
            )}
            <div>
                <TextStyleEditor
                    style={
                        (config[configKey] as any)?.textConfig?.style || {}
                    }
                    onChange={(style: TextStyle) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                textConfig: {
                                    text: '', // Empty text as we'll use dynamic content
                                    style,
                                },
                            },
                        })
                    }
                    configKey={String(configKey) + '.textConfig.style'}
                />
            </div>

            <ButtonIconStyleEditor
                config={(config[configKey] as any)?.iconStyle || {}}
                onChange={(iconStyle) =>
                    onChange({
                        [configKey]: {
                            ...config[configKey],
                            iconStyle,
                        },
                    })
                }
            />

            <div>
                <Label className="mb-2 block">Background Image</Label>
                <AssetPicker
                    onSelect={(assetId) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                asset: { assetId },
                            },
                        })
                    }
                    assetUrl={(config[configKey] as any)?.asset}
                    extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']}
                />
            </div>
            <div>
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Background Color</Label>
                    <Switch
                        checked={(config[configKey] as any)?.useBackgroundColor !== false}
                        onCheckedChange={(useBackgroundColor) =>
                            onChange({
                                [configKey]: { ...config[configKey], useBackgroundColor },
                            })
                        }
                    />
                </div>
                <div className={`transition-opacity ${(config[configKey] as any)?.useBackgroundColor !== false ? 'opacity-100' : 'opacity-40'}`}>
                    <ColorPicker
                        color={(config[configKey] as any)?.fill || '#8f7a66'}
                        onChange={(fill) =>
                            onChange({
                                [configKey]: {
                                    ...config[configKey],
                                    fill,
                                },
                            })
                        }
                    />
                </div>
            </div>
            <div>
                <Label className="mb-2 block">Dimensions</Label>
                <div className="grid grid-cols-2 gap-2">
                    <div>
                        <Label className="text-xs">Width</Label>
                        <Input
                            type="number"
                            value={(config[configKey] as any)?.width || 120}
                            onChange={(e) =>
                                onChange({
                                    [configKey]: {
                                        ...config[configKey],
                                        width: parseInt(e.target.value, 10),
                                    },
                                })
                            }
                        />
                    </div>
                    <div>
                        <Label className="text-xs">Height</Label>
                        <Input
                            type="number"
                            value={(config[configKey] as any)?.height || 40}
                            onChange={(e) =>
                                onChange({
                                    [configKey]: {
                                        ...config[configKey],
                                        height: parseInt(e.target.value, 10),
                                    },
                                })
                            }
                        />
                    </div>
                </div>
            </div>
            <div>
                <Label className="mb-2 block">Offset X</Label>
                <div className="flex justify-between items-center mb-2">
                    <span className="text-sm">{(config[configKey] as any)?.offsetX || 0}px</span>
                </div>
                <Slider
                    value={[(config[configKey] as any)?.offsetX || 0]}
                    min={-50}
                    max={50}
                    step={1}
                    onValueChange={(values) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                offsetX: values[0],
                            },
                        })
                    }
                />
            </div>
            <div>
                <Label className="mb-2 block">Offset Y</Label>
                <div className="flex justify-between items-center mb-2">
                    <span className="text-sm">{(config[configKey] as any)?.offsetY || 0}px</span>
                </div>
                <Slider
                    value={[(config[configKey] as any)?.offsetY || 0]}
                    min={-50}
                    max={50}
                    step={1}
                    onValueChange={(values) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                offsetY: values[0],
                            },
                        })
                    }
                />
            </div>
            <div>
                <Label className="mb-2 block">Border Radius</Label>
                <div className="flex justify-between items-center mb-2">
                    <span className="text-sm">{(config[configKey] as any)?.borderRadius || 4}px</span>
                </div>
                <Slider
                    value={[(config[configKey] as any)?.borderRadius || 4]}
                    min={0}
                    max={50}
                    step={1}
                    onValueChange={(values) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                borderRadius: values[0],
                            },
                        })
                    }
                />
            </div>
            <div>
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Pixelated</Label>
                    <Switch
                        checked={(config[configKey] as any)?.pixelated === true}
                        onCheckedChange={(pixelated) =>
                            onChange({
                                [configKey]: { ...config[configKey], pixelated },
                            })
                        }
                    />
                </div>
            </div>
            {/* Position selector */}
            <div>
                <Label className="mb-2 block">Position</Label>
                <Select
                    value={(config[configKey] as any)?.position || 'center'}
                    onValueChange={(position) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                position,
                            },
                        })
                    }
                >
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select position" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            {/* Keep margin bottom for backward compatibility */}
            <div>
                <Label className="mb-2 block">Margin Bottom</Label>
                <Input
                    type="number"
                    value={(config[configKey] as any)?.marginBottom || 0}
                    onChange={(e) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                marginBottom: parseInt(e.target.value, 10),
                            },
                        })
                    }
                />
            </div>
        </div>
    )
}
