import { ConfigKeyEditorComponentProps } from '@repo/shared/lib/game/game'
import { Game2048Config, game2048DefaultConfig } from '../types/game2048Config'
import { Tile2048Editor } from './components/Tile2048Editor'
import { EmptyTile2048Editor } from './components/EmptyTile2048Editor'
import { GridBackgroundEditor } from './components/GridBackgroundEditor'
import { ScoreboardNumbersStyleEditor } from './components/ScoreboardNumbersStyleEditor'

import { Button } from '@repo/shared/components/ui/button'
import React, { ReactNode } from 'react'
import { RefreshCw } from 'lucide-react'
import { AudioEditor } from '@repo/shared-game-utils/editor-components/AudioEditor'
import { BackgroundEditor } from '@repo/shared-game-utils/editor-components/BackgroundEditor'
import { CounterElementStyleEditor } from '@repo/shared-game-utils/editor-components/CounterElementStyleEditor'
import { GameButtonEditor } from '@repo/shared-game-utils/editor-components/GameButtonEditor'
import { ImageEditor } from '@repo/shared-game-utils/editor-components/ImageEditor'
import { RewardComponentStyleEditor } from '@repo/shared-game-utils/editor-components/RewardComponentStyleEditor'
import { TextEditor } from '@repo/shared-game-utils/editor-components/TextEditor'
import { useConfigKeyDefinition } from '@repo/shared-game-utils/hooks/useConfigType'
import { Input } from '@repo/shared/components/ui/input'
import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'

type ComponentMapping = Record<string, React.ComponentType<any>>
type TextEditorProps = {
    text: any
    onChange: (text: any) => void
    configKey: string
}

// Number editor component
function NumberEditor({ configKey, config, onChange }: { configKey: string, config: any, onChange: (changes: any) => void }) {
    const definition = useConfigKeyDefinition(configKey)
    
    return (
        <div className="space-y-2">
            <Label>{definition?.name ?? configKey.toString()}</Label>
            <Input
                type="number"
                value={config[configKey] ?? 0}
                onChange={(e) => onChange({ [configKey]: parseInt(e.target.value, 10) })}
                min={0}
            />
        </div>
    )
}

// Lives handler editor component
function LivesHandlerEditor({ configKey, config, onChange }: { configKey: string, config: any, onChange: (changes: any) => void }) {
    const livesHandler = config[configKey] || {}
    const useLives = livesHandler.useLives ?? false
    const livesCount = livesHandler.livesCount ?? 3

    const handleUseLivesChange = (useLives: boolean) => {
        onChange({
            [configKey]: {
                ...livesHandler,
                useLives,
            },
        })
    }

    const handleLivesCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        onChange({
            [configKey]: {
                ...livesHandler,
                livesCount: Number(e.target.value),
            },
        })
    }

    return (
        <div className="space-y-4">
            <div className="flex items-center space-x-2">
                <Label className="flex-grow">Use Lives System</Label>
                <Switch
                    checked={useLives}
                    onCheckedChange={handleUseLivesChange}
                />
            </div>

            {useLives && (
                <div className="space-y-2">
                    <Label htmlFor="lives-count">Number of Lives</Label>
                    <Input
                        id="lives-count"
                        type="number"
                        value={livesCount}
                        onChange={handleLivesCountChange}
                        min={1}
                        max={10}
                    />
                </div>
            )}
        </div>
    )
}

// Rewards handler editor component
function RewardsHandlerEditor({ configKey, config, onChange }: { configKey: string, config: any, onChange: (changes: any) => void }) {
    const rewardsHandler = config[configKey] || {}
    const rewardsEnabled = rewardsHandler.rewardsEnabled ?? false

    const handleRewardsEnabledChange = (rewardsEnabled: boolean) => {
        onChange({
            [configKey]: {
                ...rewardsHandler,
                rewardsEnabled,
            },
        })
    }

    return (
        <div className="space-y-4">
            <div className="flex items-center space-x-2">
                <Label className="flex-grow">Enable Rewards</Label>
                <Switch
                    checked={rewardsEnabled}
                    onCheckedChange={handleRewardsEnabledChange}
                />
            </div>
        </div>
    )
}

export const editorComponentMapping: ComponentMapping = {
    'image': ImageEditor,
    'audio': AudioEditor,
    'image-background': BackgroundEditor,
    '2048-grid-background': GridBackgroundEditor,
    'empty-2048-tile': EmptyTile2048Editor,
    '2048-tile': Tile2048Editor,
    '2048-scoreboard-numbers-style': ScoreboardNumbersStyleEditor,
    'text': TextEditor,
    'game-button': GameButtonEditor,
    'reward-component-style': RewardComponentStyleEditor,
    'number': NumberEditor,
    'lives-handler': LivesHandlerEditor,
    'rewards-handler': RewardsHandlerEditor,
}

export function ConfigKeyEditor({ configKey, config: gameConfig, updateConfig }: ConfigKeyEditorComponentProps) {
    const config = gameConfig ?? {}

    const handleConfigChange = (changes: Partial<Game2048Config>) => {
        updateConfig({ ...config, ...changes })
    }

    const handleResetToDefault = () => {
        // Use game2048DefaultConfig which contains the actual default values
        const defaultValue = game2048DefaultConfig[configKey]
        if (defaultValue !== undefined) {
            handleConfigChange({ [configKey]: defaultValue })
        }
    }

    const definition = useConfigKeyDefinition(configKey)

    if (!definition) {
        return <div>No definition found for: {configKey?.toString()}</div>
    }

    const { configEditorType: type } = definition

    // If the type is not supported, show an error message
    if (!type) {
        return <div>No editor type defined for: {configKey?.toString()}</div>
    }

    return (
        <EditorComponentWrapper onReset={handleResetToDefault}>
            {renderEditorComponent(type, configKey as string, config, handleConfigChange)}
        </EditorComponentWrapper>
    )
}

interface EditorComponentWrapperProps {
    children: ReactNode
    onReset: () => void
    showResetButton?: boolean
}

export function EditorComponentWrapper({
    children,
    onReset,
    showResetButton = true
}: EditorComponentWrapperProps) {
    return (
        <div className="space-y-4">
            {showResetButton && (
                <div className="flex justify-end mb-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={onReset}
                        className="flex items-center gap-1"
                    >
                        <RefreshCw size={14} />
                        Reset
                    </Button>
                </div>
            )}
            {children}
        </div>
    )
}

// Function to render the appropriate editor component
export function renderEditorComponent(
    type: string,
    configKey: string,
    config: any,
    handleConfigChange: (changes: Partial<Game2048Config>) => void
): ReactNode {
    const Component = editorComponentMapping[type]

    if (!Component) {
        return <div>Unsupported config type: {type}</div>
    }

    const props = getComponentProps(type, configKey, config, handleConfigChange)
    return <Component {...props} />
}

// Define a type for the editor component props
export interface EditorComponentProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
}

// Helper function to get the appropriate props for a component based on its type
export function getComponentProps(
    type: string,
    configKey: string,
    config: any,
    handleConfigChange: (changes: Partial<Game2048Config>) => void
): any {
    // Special case for text editor which has a different prop structure
    if (type === 'text') {
        return {
            text: (config[configKey] as any) || (game2048DefaultConfig[configKey as keyof Game2048Config] as any),
            onChange: (text: any) => handleConfigChange({ [configKey]: text }),
            configKey: configKey,
        } as TextEditorProps
    }

    // Special case for components that need a different onChange handler
    if (type === 'reward-component-style') {
        return {
            config: config[configKey] as any,
            onChange: (changes: any) => handleConfigChange({
                [configKey]: {
                    ...config[configKey],
                    ...changes,
                },
            }),
        }
    }

    // Default case for most components
    return {
        configKey: configKey,
        config: config,
        onChange: handleConfigChange,
    } as EditorComponentProps
} 