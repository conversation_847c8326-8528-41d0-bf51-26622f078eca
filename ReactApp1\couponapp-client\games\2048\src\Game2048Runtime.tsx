import { AnimatePresence, motion } from 'framer-motion'
import { Heart, Star, Trophy } from 'lucide-react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSwipeable } from 'react-swipeable'
import { gameEvents } from '@repo/shared/lib/campaign/eventEmitter'
import { GameRuntimeComponentProps } from '@repo/shared/lib/game/game'
import { EditorSelectionBounds } from '@repo/shared/lib/types/editorSelection'
import { selectedEditorItemAtom } from '@repo/shared/lib/atoms/editor-atoms'
import { useAtom } from 'jotai'
import { Game2048Config, game2048DefaultConfig } from './types/game2048Config'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { Button } from '@repo/shared/components/ui/button'
import { TryAgainScreen, GameOverScreen, RewardScreen, OutOfLivesScreen } from './PreviewScene'
import { useGame<PERSON>tom } from '@repo/shared/lib/atoms/atom-extensions'
import { useGameRewards } from '@repo/shared/lib/game/useGameRewards'
import { makeElementsInteractive } from '@repo/shared-game-utils/utils/previewInteractive'
import { GameContext, useGame } from '@repo/shared-game-utils/hooks/useGame'

const GRID_SIZE = 4
const MOVE_COOLDOWN = 80

const useGameStorage = ({ widgetId, config, inputEnabled, isPreview, resolveAssetUrl }: GameRuntimeComponentProps<Game2048Config>) => {
    const [livesCount, setLivesCount] = useGameAtom(widgetId, 'livesCount', config.gameEndHandler?.useLives ? config.gameEndHandler?.livesCount || 3 : Number.MAX_SAFE_INTEGER)
    const [score, setScore] = useGameAtom(widgetId, 'score', 0)
    const [bestScore, setBestScore] = useGameAtom(widgetId, 'bestScore', 0)
    const [tiles, setTiles] = useGameAtom(widgetId, 'tiles', [])
    const [attemptsTaken, setAttemptsTaken] = useGameAtom(widgetId, 'attemptsTaken', 0)

    return {
        livesCount,
        score,
        bestScore,
        tiles,
        attemptsTaken,
        setLivesCount,
        setScore,
        setBestScore,
        setTiles,
        setAttemptsTaken,
    }
}

export type Game2048RuntimeProps = GameRuntimeComponentProps<Game2048Config> & {
    currentScreenId: 'previewScreen_Main' | 'previewScreen_TryAgain' | 'previewScreen_OutOfLives' | 'previewScreen_Reward'
}




export default function Runtime({ config: gameConfig, widgetId, inputEnabled = true, isPreview = true, resolveAssetUrl, currentScreenId: initialScreenId}: Game2048RuntimeProps) {

    const initialGameScreenChecked = useRef(false)

    return <GameContext.Provider value={{
        config: gameConfig,
        widgetId,
        inputEnabled,
        isPreview,
        resolveAssetUrl,
        currentScreenId: initialScreenId,
        setCurrentScreenId: (screenId: 'previewScreen_Main' | 'previewScreen_TryAgain' | 'previewScreen_OutOfLives' | 'previewScreen_Reward') => {},
        defaultConfig: game2048DefaultConfig,
        initialGameScreenChecked,
    }}>
        <RuntimeInternal config={gameConfig} widgetId={widgetId} inputEnabled={inputEnabled} isPreview={isPreview} resolveAssetUrl={resolveAssetUrl} currentScreenId={initialScreenId} />
    </GameContext.Provider>
}

  function RuntimeInternal({ config: gameConfig, widgetId, inputEnabled = true, isPreview = true, resolveAssetUrl, currentScreenId: initialScreenId }: Game2048RuntimeProps) {
    const { config } = useGame<Game2048Config>()
    const tileSpacing = config.tileSpacing ?? 15
    const gridMargin = config.gridMargin ?? 20
    const [cellSize, setCellSize] = useState(80)
    const [showLoseLife, setShowLoseLife] = useState(false)
    const [showOutOfLives, setShowOutOfLives] = useState(false)
    const [showRewardScreen, setShowRewardScreen] = useState(false)
    const [lastMoveTime, setLastMoveTime] = useState(0)
    const [editorSelection, setEditorSelection] = useAtom(selectedEditorItemAtom)
    const [currentScreenId, setCurrentScreenId] = useState('previewScreen_Main')

    console.log('Rendering some shit..??')

    //@ts-ignore
    const {
        livesCount: lives,
        score,
        bestScore,
        tiles,
        attemptsTaken,
        setTiles,
        setBestScore,
        setLivesCount,
        setScore,
        setAttemptsTaken,
    } = useGameStorage({
        widgetId,
        config,
        inputEnabled,
        isPreview,
        resolveAssetUrl,
    })

    // Use the gameRewards hook with the attempt parameter
    const { reward, pickReward, hasWonReward } = useGameRewards({ gameWidgetId: widgetId, attempt: attemptsTaken, score: bestScore })

    const mainDivRef = useRef<HTMLDivElement>(null)
    const isInitialStateChecked = useRef(false)

    useEffect(() => {
        if (isPreview) {
            setupPreviewMode()
            setCurrentScreenId(initialScreenId)
        } else if (tiles?.length === 0) {
            setTiles(initializeTiles())
            setCurrentScreenId('previewScreen_Main')
        }
    }, [isPreview, initialScreenId])

    // We'll move this effect after the onGameAssetSelect function declaration

    useEffect(() => {
        const handleResize = () => {
            if (!mainDivRef.current) return
            const mainDivSize = mainDivRef.current?.clientWidth || 0
            const size = Math.min(80, mainDivSize / (GRID_SIZE * 1.5))
            setCellSize(size)
        }

        handleResize()
        window.addEventListener('resize', handleResize)
        return () => window.removeEventListener('resize', handleResize)
    }, [])

    useEffect(() => {
        const handleKeyDown = (e) => {
            if (!inputEnabled) return
            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
                e.preventDefault()
                const direction = e.key.replace('Arrow', '').toLowerCase()
                move(direction)
            }
        }

        window.addEventListener('keydown', handleKeyDown)
        return () => window.removeEventListener('keydown', handleKeyDown)
    }, [tiles])

    useEffect(() => {
        if (score == 0 && lives == 3 && bestScore == 0) {
            return
        }
    }, [lives, bestScore])

    const checkInitialState = () => {
        const useLives = config.gameEndHandler?.useLives || false
        const rewardsEnabled = config.rewardsHandler?.rewardsEnabled || false

        if (rewardsEnabled && hasWonReward) {
            return 'previewScreen_Reward'
        }

        if (useLives && lives <= 0) {
            return 'previewScreen_OutOfLives'
        }
    }

    useEffect(() => {
        if (isInitialStateChecked.current == true) {
            return
        }

        if (bestScore == 0) {
            return
        }

        isInitialStateChecked.current = true
        const initialScreen = checkInitialState()
        if (initialScreen) {
            setCurrentScreenId(initialScreen)
        }
    }, [bestScore])

    const handlers = useSwipeable({
        onSwipedLeft: () => !isPreview && move('left'),
        onSwipedRight: () => !isPreview && move('right'),
        onSwipedUp: () => !isPreview && move('up'),
        onSwipedDown: () => !isPreview && move('down'),
        trackMouse: !isPreview,
        trackTouch: !isPreview,
    })

    function initializeTiles() {
        const initialTiles = []
        for (let i = 0; i < 2; i++) {
            const newTile = createRandomTile(initialTiles)
            if (newTile) initialTiles.push(newTile)
        }
        return initialTiles
    }

    function createRandomTile(existingTiles) {
        const emptyPositions = []
        for (let i = 0; i < GRID_SIZE; i++) {
            for (let j = 0; j < GRID_SIZE; j++) {
                if (!existingTiles.some((tile) => tile.row === i && tile.col === j)) {
                    emptyPositions.push({ row: i, col: j })
                }
            }
        }

        if (emptyPositions.length === 0) return null

        const { row, col } = emptyPositions[Math.floor(Math.random() * emptyPositions.length)]
        return {
            id: `${row}-${col}-${Date.now()}`,
            value: Math.random() < 0.9 ? 2 : 4,
            row,
            col,
        }
    }

    function move(direction) {
        if (showLoseLife || currentScreenId !== 'previewScreen_Main' || isPreview) return

        const currentTime = Date.now()
        if (currentTime - lastMoveTime < MOVE_COOLDOWN) return

        let newTiles = JSON.parse(JSON.stringify(tiles))
        let moved = false
        let newScore = score

        const sortOrder = {
            up: (a, b) => a.row - b.row,
            down: (a, b) => b.row - a.row,
            left: (a, b) => a.col - b.col,
            right: (a, b) => b.col - a.col,
        }
        newTiles.sort(sortOrder[direction])

        newTiles.forEach((tile) => {
            let { row, col } = tile
            let newRow = row
            let newCol = col

            while (true) {
                newRow += direction === 'up' ? -1 : direction === 'down' ? 1 : 0
                newCol += direction === 'left' ? -1 : direction === 'right' ? 1 : 0

                if (newRow < 0 || newRow >= GRID_SIZE || newCol < 0 || newCol >= GRID_SIZE) {
                    newRow -= direction === 'up' ? -1 : direction === 'down' ? 1 : 0
                    newCol -= direction === 'left' ? -1 : direction === 'right' ? 1 : 0
                    break
                }

                const targetTile = newTiles.find((t) => t.row === newRow && t.col === newCol)
                if (targetTile) {
                    if (targetTile.value === tile.value && !targetTile.mergedFrom) {
                        newTiles = newTiles.filter((t) => t.id !== tile.id)
                        targetTile.value *= 2
                        targetTile.mergedFrom = true
                        newScore += targetTile.value
                        moved = true
                    } else {
                        newRow -= direction === 'up' ? -1 : direction === 'down' ? 1 : 0
                        newCol -= direction === 'left' ? -1 : direction === 'right' ? 1 : 0
                    }
                    break
                }

                moved = true
            }

            tile.row = newRow
            tile.col = newCol
        })

        if (moved) {
            setLastMoveTime(currentTime)
            newTiles.forEach((tile) => delete tile.mergedFrom)
            const newTile = createRandomTile(newTiles)
            if (newTile) newTiles.push(newTile)
            setTiles(newTiles)
            setScore(newScore)

            if (newScore > bestScore) {
                setBestScore(newScore)
            }

            // Check for game over after the move
            if (!isMovePossible(newTiles)) {
                handleLoseLife()
            }
        }
    }

    function isMovePossible(currentTiles) {
        if (window.location.href.includes('testing')) {
            //TESTING
            return false
        }

        if (currentTiles.length < GRID_SIZE * GRID_SIZE) {
            return true
        }

        for (let i = 0; i < GRID_SIZE; i++) {
            for (let j = 0; j < GRID_SIZE; j++) {
                const currentTile = currentTiles.find((t) => t.row === i && t.col === j)
                if (currentTile) {
                    if (j < GRID_SIZE - 1) {
                        const rightTile = currentTiles.find((t) => t.row === i && t.col === j + 1)
                        if (rightTile && rightTile.value === currentTile.value) {
                            return true
                        }
                    }
                    if (i < GRID_SIZE - 1) {
                        const downTile = currentTiles.find((t) => t.row === i + 1 && t.col === j)
                        if (downTile && downTile.value === currentTile.value) {
                            return true
                        }
                    }
                }
            }
        }

        return false
    }

    async function handleLoseLife() {
        if (showLoseLife) return

        const rewardsEnabled = config.rewardsHandler?.rewardsEnabled || false
        const useLives = config.gameEndHandler?.useLives

        // Decrement lives
        setLivesCount(lives - 1)

        // Increment attempts
        setAttemptsTaken(prev => prev + 1)

        // Handle rewards if enabled
        if (rewardsEnabled) {
            const pickResult = await pickReward()

            if (pickResult.hasWon) {
                setCurrentScreenId('previewScreen_Reward')
                return
            }
        }

        // If lives system is disabled, show try again screen
        if (!useLives) {
            setCurrentScreenId('previewScreen_TryAgain')
            return
        }

        // Handle lives system
        if (useLives && lives <= 1) {
            setCurrentScreenId('previewScreen_OutOfLives')
            return
        }

        setShowLoseLife(true)
        setCurrentScreenId('previewScreen_TryAgain')
    }

    function resetGame() {
        setTiles(initializeTiles())
        setScore(0)
    }

    function setupPreviewMode() {
        const previewTiles = []
        const possibleValues = [2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096]
        let valueIndex = 0

        // Create a visually appealing arrangement of tiles for preview
        for (let row = 0; row < GRID_SIZE; row++) {
            for (let col = 0; col < GRID_SIZE; col++) {
                if (valueIndex < possibleValues.length) {
                    previewTiles.push({
                        id: `tile-${possibleValues[valueIndex]}`,
                        value: possibleValues[valueIndex],
                        row,
                        col,
                    })
                    valueIndex++
                }
            }
        }

        setTiles(previewTiles)

        // Use the new utility function to make elements interactive
        setTimeout(() => {
            makeElementsInteractive(onGameAssetSelect)
        }, 100)
    }

    const onGameAssetSelect = useCallback(
        (assetKey: string, bounds: { x: number; y: number; width: number; height: number }) => {
            console.log(`onGameAssetSelect called for ${assetKey}`, {
                isPreview,
                widgetId,
                bounds,
            })

            if (!isPreview || !widgetId) {
                console.warn(`Cannot select asset - isPreview: ${isPreview}, widgetId: ${widgetId}`)
                return
            }

            setEditorSelection({
                widgetId,
                assetKey,
                bounds: {
                    x: bounds.x,
                    y: bounds.y,
                    width: bounds.width,
                    height: bounds.height,
                    isAbsolute: true,
                },
                type: 'game-asset',
                time: Date.now(),
            })
        },
        [isPreview, widgetId]
    )

    // Add a new effect to make elements interactive when the screen changes
    useEffect(() => {
        if (!isPreview) return

        // Use a small timeout to ensure the DOM is updated
        setTimeout(() => {
            makeElementsInteractive(onGameAssetSelect)
        }, 100)
    }, [currentScreenId, isPreview, onGameAssetSelect])

    // The makeInteractive function is no longer needed as we're using the utility function

    const assetToBackgroundImageCss = (value: AssetUrl) => {
        const url = resolveAssetUrl(value)
        return url ? `url(${url})` : undefined
    }

    const getCellBackgroundStyle = (value) => {
        const colors = {
            2: {
                backgroundColor: config.tile2?.useBackgroundColor !== false ? (config.tile2?.fill ?? '#00000000') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.tile2?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                color: config.tile2?.textColor,
            } as React.CSSProperties,

            4: {
                backgroundColor: config.tile4?.useBackgroundColor !== false ? (config.tile4?.fill ?? '#00000000') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.tile4?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                color: config.tile4?.textColor,
            } as React.CSSProperties,

            8: {
                backgroundColor: config.tile8?.useBackgroundColor !== false ? (config.tile8?.fill ?? '#00000000') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.tile8?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                color: config.tile8?.textColor,
            } as React.CSSProperties,

            16: {
                backgroundColor: config.tile16?.useBackgroundColor !== false ? (config.tile16?.fill ?? '#00000000') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.tile16?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                color: config.tile16?.textColor,
            } as React.CSSProperties,

            32: {
                backgroundColor: config.tile32?.useBackgroundColor !== false ? (config.tile32?.fill ?? '#00000000') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.tile32?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                color: config.tile32?.textColor,
            } as React.CSSProperties,

            64: {
                backgroundColor: config.tile64?.useBackgroundColor !== false ? (config.tile64?.fill ?? '#00000000') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.tile64?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                color: config.tile64?.textColor,
            } as React.CSSProperties,

            128: {
                backgroundColor: config.tile128?.useBackgroundColor !== false ? (config.tile128?.fill ?? '#00000000') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.tile128?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                color: config.tile128?.textColor,
            } as React.CSSProperties,

            256: {
                backgroundColor: config.tile256?.useBackgroundColor !== false ? (config.tile256?.fill ?? '#00000000') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.tile256?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                color: config.tile256?.textColor,
            } as React.CSSProperties,

            512: {
                backgroundColor: config.tile512?.useBackgroundColor !== false ? (config.tile512?.fill ?? '#00000000') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.tile512?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                color: config.tile512?.textColor,
            } as React.CSSProperties,

            1024: {
                backgroundColor: config.tile1024?.useBackgroundColor !== false ? (config.tile1024?.fill ?? '#00000000') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.tile1024?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                color: config.tile1024?.textColor,
            } as React.CSSProperties,

            2048: {
                backgroundColor: config.tile2048?.useBackgroundColor !== false ? (config.tile2048?.fill ?? '#00000000') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.tile2048?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                color: config.tile2048?.textColor,
            } as React.CSSProperties,

            4096: {
                backgroundColor: config.tile4096?.useBackgroundColor !== false ? (config.tile4096?.fill ?? '#00000000') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.tile4096?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                color: config.tile4096?.textColor,
            } as React.CSSProperties,
        }
        const css = colors[value] || { backgroundColor: 'red' }
        return css
    }

    const shouldShowTileText = (value) => {
        switch (value) {
            case 2:
                return config.tile2?.showText !== false
            case 4:
                return config.tile4?.showText !== false
            case 8:
                return config.tile8?.showText !== false
            case 16:
                return config.tile16?.showText !== false
            case 32:
                return config.tile32?.showText !== false
            case 64:
                return config.tile64?.showText !== false
            case 128:
                return config.tile128?.showText !== false
            case 256:
                return config.tile256?.showText !== false
            case 512:
                return config.tile512?.showText !== false
            case 1024:
                return config.tile1024?.showText !== false
            case 2048:
                return config.tile2048?.showText !== false
            case 4096:
                return config.tile4096?.showText !== false
            default:
                return true
        }
    }

    const emptySlots = []
    for (let row = 0; row < GRID_SIZE; row++) {
        for (let col = 0; col < GRID_SIZE; col++) {
            emptySlots.push({ row, col })
        }
    }

    const getScoreboardTextAlignment = (textAlign) => {
        switch (textAlign) {
            case 'left':
                return { justifyContent: 'flex-start' }
            case 'center':
                return { justifyContent: 'center' }
            case 'right':
                return { justifyContent: 'flex-end' }
            case 'top-left':
                return { justifyContent: 'flex-start', alignItems: 'flex-start' }
            case 'top-center':
                return { justifyContent: 'center', alignItems: 'flex-start' }
            case 'top-right':
                return { justifyContent: 'flex-end', alignItems: 'flex-start' }
            case 'bottom-left':
                return { justifyContent: 'flex-start', alignItems: 'flex-end' }
            case 'bottom-center':
                return { justifyContent: 'center', alignItems: 'flex-end' }
            case 'bottom-right':
                return { justifyContent: 'flex-end', alignItems: 'flex-end' }
            default:
                return { justifyContent: 'center' }
        }
    }

    const getFontFamily = (value) => {
        switch (value) {
            case 2:
                return config.tile2?.fontFamily_tFontFamily || 'Londrina Solid'
            case 4:
                return config.tile4?.fontFamily_tFontFamily || 'Londrina Solid'
            case 8:
                return config.tile8?.fontFamily_tFontFamily || 'Londrina Solid'
            case 16:
                return config.tile16?.fontFamily_tFontFamily || 'Londrina Solid'
            case 32:
                return config.tile32?.fontFamily_tFontFamily || 'Londrina Solid'
            case 64:
                return config.tile64?.fontFamily_tFontFamily || 'Londrina Solid'
            case 128:
                return config.tile128?.fontFamily_tFontFamily || 'Londrina Solid'
            case 256:
                return config.tile256?.fontFamily_tFontFamily || 'Londrina Solid'
            case 512:
                return config.tile512?.fontFamily_tFontFamily || 'Londrina Solid'
            case 1024:
                return config.tile1024?.fontFamily_tFontFamily || 'Londrina Solid'
            case 2048:
                return config.tile2048?.fontFamily_tFontFamily || 'Londrina Solid'
            case 4096:
                return config.tile4096?.fontFamily_tFontFamily || 'Londrina Solid'
            default:
                return 'Londrina Solid'
        }
    }

    const getFontSize = (value) => {
        switch (value) {
            case 2:
                return config.tile2?.fontSize || 22
            case 4:
                return config.tile4?.fontSize || 22
            case 8:
                return config.tile8?.fontSize || 22
            case 16:
                return config.tile16?.fontSize || 22
            case 32:
                return config.tile32?.fontSize || 22
            case 64:
                return config.tile64?.fontSize || 22
            case 128:
                return config.tile128?.fontSize || 22
            case 256:
                return config.tile256?.fontSize || 22
            case 512:
                return config.tile512?.fontSize || 22
            case 1024:
                return config.tile1024?.fontSize || 20
            case 2048:
                return config.tile2048?.fontSize || 20
            case 4096:
                return config.tile4096?.fontSize || 20
            default:
                return 22
        }
    }

    return (
        <div
            data-game-widget-id={widgetId}
            ref={mainDivRef}
            className="flex flex-col items-center justify-center h-full w-full touch-none select-none overscroll-none p-4"
            data-editor-selectable-key="mainBackground"
            data-editor-selectable-is-background={true}
            style={{
                backgroundColor: config.mainBackground?.useBackgroundColor !== false ? (config.mainBackground?.fill ?? '#faf8ef') : 'transparent',
                backgroundImage: assetToBackgroundImageCss(config.mainBackground?.asset),
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                backgroundSize: 'cover',
            }}
        >
            <div className="">
                <GameStats
                    score={isPreview ? 0 : score}
                    bestScore={isPreview ? 0 : bestScore}
                    lives={isPreview ? (config.gameEndHandler?.useLives ? config.gameEndHandler?.livesCount || 3 : undefined) : config.gameEndHandler?.useLives ? lives : undefined}
                    config={config}
                    resolveAssetUrl={resolveAssetUrl}
                    getScoreboardTextAlignment={getScoreboardTextAlignment}
                />

                {/* Show screens based on currentScreenId */}
                {currentScreenId === 'previewScreen_TryAgain' && (
                    <div className="relative">
                        <div
                            className="game-grid rounded-2xl shadow-2xl relative"
                            style={{
                                width: `${GRID_SIZE * cellSize + (GRID_SIZE - 1) * tileSpacing + 2 * gridMargin}px`,
                                height: `${GRID_SIZE * cellSize + (GRID_SIZE - 1) * tileSpacing + 2 * gridMargin}px`,
                            }}
                        >
                            <TryAgainScreen
                                config={config}
                                widgetId={widgetId}
                                resolveAssetUrl={resolveAssetUrl}
                                onButtonClick={() => {
                                    if (isPreview) return

                                    setShowLoseLife(false)
                                    resetGame()
                                    setCurrentScreenId('previewScreen_Main')
                                }}
                            />
                        </div>
                    </div>
                )}

                {currentScreenId === 'previewScreen_Reward' && (
                    <div className="relative">
                        <div
                            className="game-grid rounded-2xl shadow-2xl relative"
                            style={{
                                width: `${GRID_SIZE * cellSize + (GRID_SIZE - 1) * tileSpacing + 2 * gridMargin}px`,
                                height: `${GRID_SIZE * cellSize + (GRID_SIZE - 1) * tileSpacing + 2 * gridMargin}px`,
                            }}
                        >
                            <RewardScreen
                                config={config}
                                widgetId={widgetId}
                                resolveAssetUrl={resolveAssetUrl}
                                onButtonClick={() => {
                                    if (isPreview) return

                                    gameEvents.emit('GameFinishedWithReward', {
                                        score: bestScore,
                                        widgetId: widgetId,
                                    })
                                }}
                            />
                        </div>
                    </div>
                )}

                {currentScreenId === 'previewScreen_OutOfLives' && (
                    <div className="relative">
                        <div
                            className="game-grid rounded-2xl shadow-2xl relative"
                            style={{
                                width: `${GRID_SIZE * cellSize + (GRID_SIZE - 1) * tileSpacing + 2 * gridMargin}px`,
                                height: `${GRID_SIZE * cellSize + (GRID_SIZE - 1) * tileSpacing + 2 * gridMargin}px`,
                            }}
                        >
                            <OutOfLivesScreen
                                config={config}
                                widgetId={widgetId}
                                resolveAssetUrl={resolveAssetUrl}
                                onButtonClick={() => {
                                    if (isPreview) return

                                    gameEvents.emit('GameFinishedNoReward', {
                                        score: bestScore,
                                        widgetId: widgetId,
                                    })
                                    resetGame()
                                    setCurrentScreenId('previewScreen_Main')
                                }}
                            />
                        </div>
                    </div>
                )}

                {currentScreenId === 'previewScreen_Main' && (
                    <div
                        {...handlers}
                        className="game-grid grid gap-3 rounded-2xl shadow-2xl relative"
                        data-editor-selectable-key="gridBackground"
                        data-editor-selectable-is-background={true}
                        style={{
                            gridTemplateColumns: `repeat(${GRID_SIZE}, ${cellSize}px)`,
                            gridTemplateRows: `repeat(${GRID_SIZE}, ${cellSize}px)`,
                            position: 'relative',
                            zIndex: 1,
                            padding: `${gridMargin}px`,
                            gap: `${tileSpacing}px`,
                            backgroundColor: config.gridBackground?.useBackgroundColor !== false ? (config.gridBackground?.fill ?? '#bbada0') : 'transparent',
                            backgroundImage: assetToBackgroundImageCss(config.gridBackground?.asset),
                            backgroundPosition: 'center',
                            backgroundRepeat: 'no-repeat',
                            backgroundSize: 'cover',
                        }}
                    >
                        {emptySlots.map(({ row, col }) => (
                            <div
                                key={`empty-${row}-${col}`}
                                className="empty-tile-slot rounded-xl"
                                data-editor-selectable-key="emptyTile"
                                style={{
                                    width: `${cellSize}px`,
                                    height: `${cellSize}px`,
                                    zIndex: 5,
                                    position: 'relative',
                                    backgroundColor: config.emptyTile?.useBackgroundColor !== false ? (config.emptyTile?.fill ?? '#cdc1b4') : 'transparent',
                                    backgroundImage: assetToBackgroundImageCss(config.emptyTile?.asset),
                                    backgroundPosition: 'center',
                                    backgroundRepeat: 'no-repeat',
                                    backgroundSize: 'cover',
                                }}
                            />
                        ))}

                        <AnimatePresence>
                            {tiles &&
                                tiles.map((tile) => (
                                    <motion.div
                                        data-editor-selectable-key={`tile${tile.value}`}
                                        key={tile.id}
                                        initial={{ scale: 0 }}
                                        animate={{ scale: 1 }}
                                        exit={{ scale: 0, opacity: 0 }}
                                        transition={{
                                            type: 'spring',
                                            stiffness: 700,
                                            damping: 50,
                                        }}
                                        layout
                                        className={`absolute flex items-center justify-center rounded-xl font-bold shadow-lg`}
                                        style={{
                                            top: `${tile.row * (cellSize + tileSpacing) + gridMargin}px`,
                                            left: `${tile.col * (cellSize + tileSpacing) + gridMargin}px`,
                                            width: `${cellSize}px`,
                                            height: `${cellSize}px`,
                                            zIndex: 10,
                                            fontFamily: getFontFamily(tile.value),
                                            fontSize: getFontSize(tile.value),
                                            ...getCellBackgroundStyle(tile.value),
                                        }}
                                    >
                                        {shouldShowTileText(tile.value) ? tile.value : null}
                                    </motion.div>
                                ))}
                        </AnimatePresence>
                    </div>
                )}
            </div>
        </div>
    )
}

const GameStats = ({ score, bestScore, lives, config, resolveAssetUrl, getScoreboardTextAlignment }) => {
    const assetToBackgroundImageCss = (value: AssetUrl) => {
        const url = resolveAssetUrl(value)
        return url ? `url(${url})` : undefined
    }

    return (
        <div className="flex justify-center gap-2 items-center w-full">
            <div
                className="score-element flex items-center justify-center rounded-lg"
                data-editor-selectable-key="scoreStyle"
                style={{
                    backgroundColor: config.scoreStyle?.useBackgroundColor !== false ? (config.scoreStyle?.fill ?? '#776e65') : 'transparent',
                    backgroundImage: assetToBackgroundImageCss(config.scoreStyle?.asset),
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: 'contain',
                    width: config.scoreStyle?.width ? `${config.scoreStyle.width}px` : '80px',
                    height: config.scoreStyle?.height ? `${config.scoreStyle.height}px` : '45px',
                    marginBottom: config.scoreStyle?.marginBottom ? `${config.scoreStyle.marginBottom}px` : '0',
                    display: 'flex',
                    alignItems: getScoreboardTextAlignment(config.scoreStyle?.textAlign)?.alignItems || 'center',
                    justifyContent: getScoreboardTextAlignment(config.scoreStyle?.textAlign)?.justifyContent || 'center',
                }}
            >
                <p
                    className="text-lg font-bold"
                    style={{
                        color: config.scoreStyle?.textColor ?? '#f9f6f2',
                        fontFamily: config.scoreStyle?.fontFamily_tFontFamily ?? 'Londrina Solid',
                        fontSize: config.scoreStyle?.fontSize ? `${config.scoreStyle.fontSize}px` : '16px',
                        padding: `${config.scoreStyle?.padding?.top || 5}px ${config.scoreStyle?.padding?.right || 5}px ${config.scoreStyle?.padding?.bottom || 5}px ${config.scoreStyle?.padding?.left || 5}px`,
                    }}
                >
                    {score}
                </p>
            </div>
            <div
                className="best-score-element flex items-center justify-center rounded-lg"
                data-editor-selectable-key="bestScoreStyle"
                style={{
                    backgroundColor: config.bestScoreStyle?.useBackgroundColor !== false ? (config.bestScoreStyle?.fill ?? '#776e65') : 'transparent',
                    backgroundImage: assetToBackgroundImageCss(config.bestScoreStyle?.asset),
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: 'contain',
                    width: config.bestScoreStyle?.width ? `${config.bestScoreStyle.width}px` : '80px',
                    height: config.bestScoreStyle?.height ? `${config.bestScoreStyle.height}px` : '45px',
                    marginBottom: config.bestScoreStyle?.marginBottom ? `${config.bestScoreStyle.marginBottom}px` : '0',
                    display: 'flex',
                    alignItems: getScoreboardTextAlignment(config.bestScoreStyle?.textAlign)?.alignItems || 'center',
                    justifyContent: getScoreboardTextAlignment(config.bestScoreStyle?.textAlign)?.justifyContent || 'center',
                }}
            >
                <p
                    className="text-lg font-bold"
                    style={{
                        color: config.bestScoreStyle?.textColor ?? '#f9f6f2',
                        fontFamily: config.bestScoreStyle?.fontFamily_tFontFamily ?? 'Londrina Solid',
                        fontSize: config.bestScoreStyle?.fontSize ? `${config.bestScoreStyle.fontSize}px` : '16px',
                        padding: `${config.bestScoreStyle?.padding?.top || 5}px ${config.bestScoreStyle?.padding?.right || 5}px ${config.bestScoreStyle?.padding?.bottom || 5}px ${config.bestScoreStyle?.padding?.left || 5}px`,
                    }}
                >
                    {bestScore}
                </p>
            </div>
            {lives != undefined && (
                <div
                    className="lives-element flex items-center justify-center rounded-lg"
                    data-editor-selectable-key="livesStyle"
                    style={{
                        backgroundColor: config.livesStyle?.useBackgroundColor !== false ? (config.livesStyle?.fill ?? '#776e65') : 'transparent',
                        backgroundImage: assetToBackgroundImageCss(config.livesStyle?.asset),
                        backgroundPosition: 'center',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: 'contain',
                        width: config.livesStyle?.width ? `${config.livesStyle.width}px` : '80px',
                        height: config.livesStyle?.height ? `${config.livesStyle.height}px` : '45px',
                        marginBottom: config.livesStyle?.marginBottom ? `${config.livesStyle.marginBottom}px` : '0',
                        display: 'flex',
                        alignItems: getScoreboardTextAlignment(config.livesStyle?.textAlign)?.alignItems || 'center',
                        justifyContent: getScoreboardTextAlignment(config.livesStyle?.textAlign)?.justifyContent || 'center',
                    }}
                >
                    <p
                        className="text-lg font-bold"
                        style={{
                            color: config.livesStyle?.textColor ?? '#f9f6f2',
                            fontFamily: config.livesStyle?.fontFamily_tFontFamily ?? 'Londrina Solid',
                            fontSize: config.livesStyle?.fontSize ? `${config.livesStyle.fontSize}px` : '16px',
                            padding: `${config.livesStyle?.padding?.top || 5}px ${config.livesStyle?.padding?.right || 5}px ${config.livesStyle?.padding?.bottom || 5}px ${config.livesStyle?.padding?.left || 5}px`,
                        }}
                    >
                        {lives}
                    </p>
                </div>
            )}
        </div>
    )
}
