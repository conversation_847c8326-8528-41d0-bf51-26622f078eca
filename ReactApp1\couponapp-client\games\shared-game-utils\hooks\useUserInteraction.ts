import {  useEffect } from 'react'
import { atom, useAtom } from 'jotai'

export const userIntaractionAtom = atom(false)

export const useUserInteraction = (): boolean => {
    const [hasInteracted, setHasInteracted] = useAtom(userIntaractionAtom)

    useEffect(() => {
        // If already interacted, no need to add listeners
        if (hasInteracted) {
            return
        }

        const handleInteraction = () => {
            setHasInteracted(true)
        }

        // Options to ensure the listener is added only once
        const listenerOptions = { once: true, capture: true } // Use capture to catch event early

        // Add listeners for common interaction events
        document.addEventListener('click', handleInteraction, listenerOptions)
        document.addEventListener('keydown', handleInteraction, listenerOptions)
        document.addEventListener('touchstart', handleInteraction, listenerOptions)

        // Cleanup function in case the component unmounts before interaction
        return () => {
            // Although { once: true } should remove them, explicit removal is safer
            // especially if the effect re-runs before interaction.
            document.removeEventListener('click', handleInteraction, listenerOptions)
            document.removeEventListener('keydown', handleInteraction, listenerOptions)
            document.removeEventListener('touchstart', handleInteraction, listenerOptions)
        }
    }, [hasInteracted]) // Re-run effect only if hasInteracted changes (though it only goes false -> true)

    return hasInteracted
}
