import { useGame } from '../hooks/useGame'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { BackgroundStyle, GameButtonStyle, GameTextSettings, ScoreElementStyle, TextStyle } from '../types/uiStyles'
import Phaser from 'phaser'

/**
 * Generate pixelated clip-path based on border radius
 */
export const getPixelatedClipPath = (borderRadius: number = 4): string => {
    // Clamp border radius to a reasonable range for pixelated effect
    const pixelSize = Math.max(2, Math.min(borderRadius, 20))
    
    return `polygon(0 calc(100% - ${pixelSize}px), ${pixelSize}px calc(100% - ${pixelSize}px), ${pixelSize}px 100%, calc(100% - ${pixelSize}px) 100%, calc(100% - ${pixelSize}px) calc(100% - ${pixelSize}px), 100% calc(100% - ${pixelSize}px), 100% ${pixelSize}px, calc(100% - ${pixelSize}px) ${pixelSize}px, calc(100% - ${pixelSize}px) 0, ${pixelSize}px 0, ${pixelSize}px ${pixelSize}px, 0 ${pixelSize}px)`
}

export const getTextStyle = (textObj?: GameTextSettings): React.CSSProperties => {
    if (!textObj) return {}

    const style = textObj.style || {}

    const textShadow = style.shadow ? `${style.shadow.offsetX || 0}px ${style.shadow.offsetY || 0}px ${style.shadow.blur || 0}px ${style.shadow.color || '#000000'}` : 'none'

    // Handle custom textAlign values by extracting only the horizontal alignment part
    const getValidTextAlign = (align?: string) => {
        if (!align) return 'center'
        if (align === 'left' || align === 'center' || align === 'right') return align
        if (align.includes('left')) return 'left'
        if (align.includes('right')) return 'right'
        return 'center'
    }

    return {
        fontFamily: style.fontFamily_tFontFamily || 'Londrina Solid',
        fontSize: style.fontSize ? `${style.fontSize}px` : '28px',
        color: style.fill || '#776e65',
        textShadow: textShadow,
        textAlign: getValidTextAlign(style.textAlign),
        display: style.isVisible === false ? 'none' : 'block',
    }
}

export const getButtonStyle = (buttonConfig?: GameButtonStyle): React.CSSProperties => {
    if (!buttonConfig) return {}

    const { resolveAssetUrl } = useGame()

    // Determine background size and image rendering based on scale mode
    let backgroundSize: string | undefined = undefined;
    let imageRendering: string | undefined = undefined;

    if (buttonConfig.asset) {
        if (buttonConfig.backgroundScaleMode === 'pixelated') {
            backgroundSize = 'contain';
            imageRendering = 'pixelated';
        } else if (buttonConfig.backgroundScaleMode === 'pixelated-cover') {
            backgroundSize = 'cover';
            imageRendering = 'pixelated';
        } else if (buttonConfig.backgroundScaleMode === 'pixelated-stretch') {
            backgroundSize = '100% 100%';
            imageRendering = 'pixelated';
        } else if (buttonConfig.backgroundScaleMode) {
            backgroundSize = buttonConfig.backgroundScaleMode;
        } else {
            backgroundSize = 'cover';
        }
    }

    return {
        // Button styling
        backgroundColor: buttonConfig.useBackgroundColor ? buttonConfig.fill || '#8f7a66' : undefined,
        backgroundImage: buttonConfig?.asset && resolveAssetUrl ? `url(${resolveAssetUrl(buttonConfig.asset)})` : undefined,
        backgroundSize,
        backgroundPosition: buttonConfig.asset ? 'center' : undefined,

        //@ts-ignore
        imageRendering: imageRendering,

        // Dimensions
        width: buttonConfig.width ? `${buttonConfig.width}px` : '120px',
        height: buttonConfig.height ? `${buttonConfig.height}px` : '40px',
        transform: `translate(${buttonConfig.offsetX || 0}px, ${buttonConfig.offsetY || 0}px)`,

        // Border styling
        borderRadius: buttonConfig.pixelated ? '0' : (buttonConfig.borderRadius ? `${buttonConfig.borderRadius}px` : '4px'),
        clipPath: buttonConfig.pixelated ? getPixelatedClipPath(buttonConfig.borderRadius || 4) : undefined,

        // Remove padding from button as it will be handled by GameText
        padding: 0,
    }
}

/**
 * Convert alignment string to CSS flexbox alignment properties
 */
export const getTextAlignmentStyles = (textAlign?: string) => {
    switch (textAlign) {
        case 'left':
            return { justifyContent: 'flex-start', alignItems: 'center' }
        case 'center':
            return { justifyContent: 'center', alignItems: 'center' }
        case 'right':
            return { justifyContent: 'flex-end', alignItems: 'center' }
        case 'top-left':
            return { justifyContent: 'flex-start', alignItems: 'flex-start' }
        case 'top-center':
            return { justifyContent: 'center', alignItems: 'flex-start' }
        case 'top-right':
            return { justifyContent: 'flex-end', alignItems: 'flex-start' }
        case 'bottom-left':
            return { justifyContent: 'flex-start', alignItems: 'flex-end' }
        case 'bottom-center':
            return { justifyContent: 'center', alignItems: 'flex-end' }
        case 'bottom-right':
            return { justifyContent: 'flex-end', alignItems: 'flex-end' }
        default:
            return { justifyContent: 'center', alignItems: 'center' }
    }
}

/**
 * Convert a ScoreboardNumbersStyle config to CSS style properties for the container
 */
export const getScoreboardStyle = (style?: ScoreElementStyle, resolveAssetUrl?: (id: AssetUrl) => string): React.CSSProperties => {
    if (!style) return {}

    const alignment = getTextAlignmentStyles(style.textAlign)

    return {
        backgroundColor: style.useBackgroundColor !== false ? (style.fill ?? '#776e65') : 'transparent',
        backgroundImage: style.asset && resolveAssetUrl ? `url(${resolveAssetUrl(style.asset)})` : undefined,
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'contain',
        width: style.width ? `${style.width}px` : '80px',
        height: style.height ? `${style.height}px` : '45px',
        marginBottom: style.marginBottom ? `${style.marginBottom}px` : '0',
        display: 'flex',
        alignItems: alignment.alignItems,
        justifyContent: alignment.justifyContent,
        borderRadius: style.borderRadius ? `${style.borderRadius}px` : '0',
    }
}

/**
 * Get text style properties for scoreboard numbers
 */
export const getScoreboardTextStyle = (style?: ScoreElementStyle): React.CSSProperties => {
    if (!style) return {}

    return {
        color: style.textColor ?? '#f9f6f2',
        fontFamily: style.fontFamily_tFontFamily ?? 'Londrina Solid',
        fontSize: style.fontSize ? `${style.fontSize}px` : '16px',
        padding: style.padding ? `${style.padding.top || 5}px ${style.padding.right || 5}px ${style.padding.bottom || 5}px ${style.padding.left || 5}px` : '5px',
    }
}

/**
 * Get background image styles with pixel art support
 */
export const getBackgroundImageStyles = (config: {
    asset?: AssetUrl,
    backgroundScaleMode?: 'cover' | 'contain' | 'pixelated' | 'pixelated-stretch' | 'pixelated-cover'
}, resolveAssetUrl?: (id: AssetUrl) => string): React.CSSProperties => {
    if (!config.asset || !resolveAssetUrl) return {}

    // Determine background size and image rendering based on scale mode
    let backgroundSize: string = 'contain';
    let imageRendering: string | undefined = undefined;

    if (config.backgroundScaleMode === 'pixelated') {
        backgroundSize = 'contain';
        imageRendering = 'pixelated';
    } else if (config.backgroundScaleMode === 'pixelated-cover') {
        backgroundSize = 'cover';
        imageRendering = 'pixelated';
    } else if (config.backgroundScaleMode === 'pixelated-stretch') {
        backgroundSize = '100% 100%';
        imageRendering = 'pixelated';
    } else if (config.backgroundScaleMode) {
        backgroundSize = config.backgroundScaleMode;
    }

    return {
        backgroundImage: `url(${resolveAssetUrl(config.asset)})`,
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize,
        //@ts-ignore
        imageRendering,
    }
}

/**
 * Convert a Background config to CSS style properties
 */
export const getBackgroundStyle = (background?: BackgroundStyle, resolveAssetUrl?: (id: AssetUrl) => string): React.CSSProperties => {
    if (!background) return {}

    // Determine background size and image rendering based on scale mode
    let backgroundSize: string | undefined = 'cover';
    let imageRendering: string | undefined = undefined;
    let objectFit: string | undefined = undefined;

    if (background.asset) {
        if (background.backgroundScaleMode === 'pixelated') {
            backgroundSize = 'contain';
            imageRendering = 'pixelated';
        } else if (background.backgroundScaleMode === 'pixelated-cover') {
            backgroundSize = 'cover';
            imageRendering = 'pixelated';
        } else if (background.backgroundScaleMode === 'pixelated-stretch') {
            backgroundSize = '100% 100%';
            imageRendering = 'pixelated';
        } else if (background.backgroundScaleMode) {
            backgroundSize = background.backgroundScaleMode;
        }
    }

    return {
        backgroundColor: background.useBackgroundColor !== false ? (background.fill ?? 'transparent') : 'transparent',
        backgroundImage: background.asset && resolveAssetUrl ? `url(${resolveAssetUrl(background.asset)})` : undefined,
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize,

        //@ts-ignore
        imageRendering,
    }
}


export function toPhaserTextStyle(
  style?: TextStyle
): Phaser.Types.GameObjects.Text.TextStyle {
  if (!style) return {}

  const toggleVisibilityOptions = {
    fixedWidth: 0.01,
    fixedHeight: 0.01,
  };

  return {
      fontFamily: style.fontFamily_tFontFamily ,
      color: style?.fill || "#ffffff",
    ...(style.isVisible == false ? toggleVisibilityOptions : {}),
    fontSize: style.fontSize,

    fontStyle: `${style.fontWeight ?? 400}`,

    shadow: {
      offsetX: style.shadow?.offsetX || 0,
      offsetY: style.shadow?.offsetY || 0,
      color: style.shadow?.color || "#000000",
      blur: style.shadow?.blur || 0,
      fill: true,
    },
  };
}
