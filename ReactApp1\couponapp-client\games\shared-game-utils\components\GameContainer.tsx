import React from 'react'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { ContainerStyle } from '../types/uiStyles'
import { getPixelatedClipPath } from '../utils/gameStyleUtils'

interface GameContainerProps {
    config?: ContainerStyle
    dataConfigKey?: string
    className?: string
    children: React.ReactNode
    resolveAssetUrl?: (asset: AssetUrl) => string
    onClick?: (e: React.MouseEvent) => void
    style?: React.CSSProperties
}

export const GameContainer: React.FC<GameContainerProps> = ({
    config,
    dataConfigKey,
    className = '',
    children,
    resolveAssetUrl,
    onClick,
    style = {},
}) => {
    if (!config) {
        return <div className={className} style={style}>{children}</div>
    }

    const containerStyle: React.CSSProperties = {
        backgroundColor: config.useBackgroundColor !== false ? (config.fill ?? 'transparent') : 'transparent',
        backgroundImage: config.asset && resolveAssetUrl ? `url(${resolveAssetUrl(config.asset)})` : undefined,
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        borderRadius: config.pixelated ? '0' : (config.borderRadius !== undefined ? `${config.borderRadius}px` : undefined),
        clipPath: config.pixelated ? getPixelatedClipPath(config.borderRadius || 0) : undefined,
        overflow: 'hidden', // Add overflow hidden to ensure content respects border radius
        maxWidth: config.maxWidth ? `${config.maxWidth}px` : undefined,
        minHeight: config.minHeight ? `${config.minHeight}px` : undefined,
        padding: config.padding ? `${config.padding.top || 0}px ${config.padding.right || 0}px ${config.padding.bottom || 0}px ${config.padding.left || 0}px` : undefined,
        ...style,
    }

    return (
        <div
            data-editor-selectable-key={dataConfigKey}
            data-editor-selectable-is-background={true}
            className={`flex flex-col items-center justify-center ${className}`}
            style={containerStyle}
            onClick={onClick}
        >
            {children}
        </div>
    )
}
