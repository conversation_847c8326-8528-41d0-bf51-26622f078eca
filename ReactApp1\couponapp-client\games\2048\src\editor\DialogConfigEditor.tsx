import { useState } from 'react'
import { GameEditorComponentProps } from '@repo/shared/lib/game/game'
import { Button } from '@repo/shared/components/ui/button'
import { GameOverlaysEditor } from './components/GameOverlaysEditor'
import { GameTextsEditor } from './components/GameTextsEditor'
import { GridSettingsEditor } from './components/GridSettingsEditor'
import { ScoreboardSettingsEditor } from './components/ScoreboardSettingsEditor'
import { TileSettingsEditor } from './components/TileSettingsEditor'


type CategoryType = 'tiles' | 'grid' | 'scoreboard' | 'overlays' | 'texts'

interface Category {
    id: CategoryType
    label: string
    description: string
}

const categories: Category[] = [
    {
        id: 'tiles',
        label: 'Tile Settings',
        description: 'Configure tile appearance and styles'
    },
    {
        id: 'grid',
        label: 'Grid & Layout',
        description: 'Configure grid background and spacing'
    },
    {
        id: 'scoreboard',
        label: 'Scoreboard',
        description: 'Configure score, best score, and lives display'
    },
    {
        id: 'overlays',
        label: 'Game Overlays',
        description: 'Configure game over and reward overlays'
    },
    {
        id: 'texts',
        label: 'Game Texts',
        description: 'Configure game messages and button texts'
    }
]

export default function DialogConfigEditor({ config, updateConfig }: GameEditorComponentProps) {
    if (!config) config = {}

    const [selectedCategory, setSelectedCategory] = useState<CategoryType>('tiles')

    const handleConfigChange = (updates: any) => {
        updateConfig({ ...config, ...updates })
    }

    const renderCategoryContent = () => {
        switch (selectedCategory) {
            case 'tiles':
                return (
                    <TileSettingsEditor
                        config={config}
                        onChange={handleConfigChange}
                    />
                )
            case 'grid':
                return (
                    <GridSettingsEditor
                        config={config}
                        onChange={handleConfigChange}
                    />
                )
            case 'scoreboard':
                return (
                    <ScoreboardSettingsEditor
                        config={config}
                        onChange={handleConfigChange}
                    />
                )
            case 'overlays':
                return (
                    <GameOverlaysEditor
                        config={config}
                        onChange={handleConfigChange}
                    />
                )
            case 'texts':
                return (
                    <GameTextsEditor
                        config={config}
                        onChange={handleConfigChange}
                    />
                )
            default:
                return null
        }
    }

    return (
        <div className="flex h-[70vh]">
            {/* Category sidebar */}
            <div className="w-1/4 border-r border-gray-200 p-4 overflow-y-auto">
                <div className="space-y-2">
                    {categories.map((category) => (
                        <Button
                            key={category.id}
                            variant={selectedCategory === category.id ? "default" : "ghost"}
                            className="w-full justify-start h-auto p-3 text-left"
                            onClick={() => setSelectedCategory(category.id)}
                        >
                            <div>
                                <div className="font-medium">{category.label}</div>
                                <div className="text-xs text-muted-foreground mt-1">
                                    {category.description}
                                </div>
                            </div>
                        </Button>
                    ))}
                </div>
            </div>

            {/* Content area */}
            <div className="flex-1 p-6 overflow-y-auto">
                <div className="mb-4">
                    <h3 className="text-lg font-semibold">
                        {categories.find(c => c.id === selectedCategory)?.label}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                        {categories.find(c => c.id === selectedCategory)?.description}
                    </p>
                </div>
                {renderCategoryContent()}
            </div>
        </div>
    )
} 