import { Widget, WidgetRenderingContext } from "@repo/shared/lib/types/editor";
import { useScene } from "@repo/shared/lib/hooks/useScene";
import { useCampaignData } from "@repo/shared/lib/hooks/useCampaignStore";
import { WidgetMetadataRegistry } from "@repo/shared/lib/widget-metadata";
import { AssetUrl } from "@repo/shared/lib/types/widgetSettings";
import { useEffect, useState } from "react";
import "@repo/shared/components/react-templates/widgetsImport";

interface WidgetRendererProps {
  rootWidget: Widget;
}

const useViewport = () => {
  const [breakpoint, setBreakpoint] = useState<"mobile" | "desktop">("desktop");

  useEffect(() => {
    const handleResize = () => {
      setBreakpoint(window.innerWidth < 768 ? "mobile" : "desktop");
    };

    // Set initial breakpoint
    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return breakpoint;
};

const WidgetRenderer: React.FC<WidgetRendererProps> = ({
  rootWidget,
}: WidgetRendererProps) => {
  const { sceneId } = useScene();
  const currentBreakpoint = useViewport();

  const { campaignData } = useCampaignData();
  
   function resolveAssetUrl(id: AssetUrl) {
     
    console.log("Resolving asset url: ", id);
     if(id.assetId) {
       console.log("AssetId: ", id?.assetId);
       return campaignData?.assets.find((a) => a.id == id?.assetId)?.url
      }
      
      console.log("Resolving by absoluteUrl: ", id?.absoluteUrl);
    return id?.absoluteUrl;
   }

  const renderWidget = (widget: Widget) => {
    if (!widget || !widget.children) {
      return;
    }

    const WidgetComponent =
      WidgetMetadataRegistry[widget.componentName]?.componentType;

    if (!WidgetComponent) {
      console.log("Registry: ", JSON.stringify(WidgetMetadataRegistry));
      return (
        <span className="text-destructive">
          Widget template not found: {widget?.componentName}
        </span>
      );
    }

    const context: WidgetRenderingContext = {
      widgetId: widget.id,
      sceneId: sceneId,
      isEditorMode: false,
      breakpoint: currentBreakpoint,
    };

    const renderedChildren = widget.children.map((child) =>
      renderWidget(child)
    );
    const unwrappedSettings = widget.settings; //TODO Responsive settings //unwrapResponsiveValues(widget.settings, currentBreakpoint);

    return (
      <WidgetComponent
        data-widget-id={widget.id}
        settings={unwrappedSettings}
        {...context}
        resolveAssetUrl={resolveAssetUrl}
      >
        {renderedChildren}
      </WidgetComponent>
    );
  };

  return <>{renderWidget(rootWidget)}</>;
};

interface UiRendererSecondProps {
  rootWidget: Widget;
}
export const UiRendererSecond: React.FC<UiRendererSecondProps> = ({
  rootWidget,
}: UiRendererSecondProps) => {
  return <WidgetRenderer rootWidget={rootWidget} />;
};
