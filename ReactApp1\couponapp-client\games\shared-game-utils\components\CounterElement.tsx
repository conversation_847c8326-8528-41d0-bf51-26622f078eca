import { useGame } from '../hooks/useGame'
import { ButtonIconStyle, CounterElementStyle } from '../types/uiStyles'
import { getButtonStyle, getTextAlignmentStyles } from '../utils/gameStyleUtils'
import { GameText } from './GameText'
import { Icon } from '@repo/shared/components/ui/icon-picker'

// GameButtonIcon component for handling button icons
interface GameButtonIconProps {
    iconStyle?: ButtonIconStyle
}

export const GameButtonIcon: React.FC<GameButtonIconProps> = ({ iconStyle }) => {
    if (!iconStyle || !iconStyle.iconName_tIcon || iconStyle.isVisible === false) {
        return null
    }

    const iconSize = iconStyle.size || 24
    const iconColor = iconStyle.color || '#000000'
    const offsetX = iconStyle.offsetX || 0
    const offsetY = iconStyle.offsetY || 0
    const fill = iconStyle.fill ? iconColor : '#00000000'
    const strokeWidth = iconStyle.strokeWidth || undefined

    return (
        <div style={{ transform: `translate(${offsetX}px, ${offsetY}px)` }}>
            <Icon name={iconStyle.iconName_tIcon as any} size={iconSize} color={iconColor} fill={fill} strokeWidth={strokeWidth} />
        </div>
    )
}

interface CounterElementProps {
    children: React.ReactNode
    configKey: string
    style?: CounterElementStyle
}

export const CounterElement: React.FC<CounterElementProps> = ({ children, configKey, style }) => {
    // Extract text style properties
    const { isPreview } = useGame()

    const textStyle = style?.textConfig?.style || {}
    const textAlign = textStyle.textAlign || 'center'
    const alignmentStyles = getTextAlignmentStyles(textAlign)
    const iconStyle = style?.iconStyle
    const hasIcon = iconStyle && iconStyle.iconName_tIcon && iconStyle.isVisible !== false

    if (!isPreview && style?.isVisible == false) {
        return null
    }

    // Function to determine icon position styles
    const getIconPosition = () => {
        if (!hasIcon) return {}

        switch (iconStyle?.position) {
            case 'left':
                return { flexDirection: 'row' as const }
            case 'right':
                return { flexDirection: 'row-reverse' as const }
            case 'top':
                return { flexDirection: 'column' as const }
            case 'bottom':
                return { flexDirection: 'column-reverse' as const }
            default:
                return { flexDirection: 'row' as const }
        }
    }

    return (
        <div
            data-editor-selectable-key={configKey}
            style={{
                ...getButtonStyle(style),
                display: 'flex',
                ...alignmentStyles,
                opacity: isPreview && style?.isVisible == false ? 0.5 : 1,
                ...getIconPosition(),
                alignItems: 'center',
                justifyContent: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',
                gap: iconStyle?.position === 'center' ? 0 : '8px', // Space between icon and text, except for center position
                marginBottom: style?.marginBottom ? `${style.marginBottom}px` : '0', // Keep marginBottom for backward compatibility
            }}
        >
            {isPreview && style?.isVisible == false && <div>[hidden]</div>}

            {style?.isVisible !== false && (
                <>
                    {hasIcon && iconStyle?.position !== 'center' && <GameButtonIcon iconStyle={iconStyle} />}
                    <div
                        style={{
                            width: !hasIcon || iconStyle?.position === 'center' ? '100%' : 'auto',
                            textAlign: textAlign as 'left' | 'center' | 'right',
                        }}
                    >
                        {style?.textConfig ? (
                            <GameText
                                config={{
                                    text: '', // Empty text as we'll use children
                                    style: style.textConfig.style
                                }}
                                dataConfigKey={null}
                            >
                                {children} 
                            </GameText>
                        ) : (
                            <div style={{ fontFamily: 'Londrina Solid', fontSize: '18px', color: '#ffffff' }}>
                                {children}
                            </div>
                        )}
                    </div>
                    {hasIcon && iconStyle?.position === 'center' && (
                        <div
                            style={{
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: `translate(-50%, -50%) translate(${iconStyle?.offsetX || 0}px, ${iconStyle?.offsetY || 0}px)`,
                            }}
                        >
                            <GameButtonIcon iconStyle={iconStyle} />
                        </div>
                    )}
                </>
            )}
        </div>
    )
}
