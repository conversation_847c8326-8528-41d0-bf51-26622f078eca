import React from 'react'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { ReactGameConfig } from '../../types/config'
import { getBackgroundStyle } from '@repo/shared-game-utils/utils/gameStyleUtils'
interface MemoryGridProps {
    children: React.ReactNode
    dataConfigKey?: string
    className?: string
}

export const MemoryGrid: React.FC<MemoryGridProps> = ({ children, dataConfigKey, className = '' }) => {
    const { config, resolveAssetUrl, onGameAssetSelect, isPreview } = useGame<ReactGameConfig>()
    const gridConfig = config.memoryGrid

    // Ensure we have valid values for the grid within constraints (2x2 to 5x5)
    const columns = Math.min(Math.max(gridConfig?.columns || 3, 2), 5)
    const rows = Math.min(Math.max(gridConfig?.rows || 3, 2), 5)
    const spacing = gridConfig?.spacing || 10
    const padding = gridConfig?.padding || 20

    // Validate that columns * rows is even for card pairs
    const totalCells = columns * rows
    const isValidGrid = totalCells % 2 === 0

    // If grid is invalid in non-preview mode, adjust to make it valid
    // In preview mode, we'll show it as is to allow editing
    const adjustedColumns = isPreview || isValidGrid ? columns : columns + (columns % 2)

    // Log grid configuration for debugging
    if (isPreview) {
        console.log(`MemoryGrid rendering with ${columns}x${rows} grid (${totalCells} cells)`)
    }

    // Get background style from config or use default
    const backgroundStyle = getBackgroundStyle(gridConfig.background, resolveAssetUrl)

    const gridStyle = {
        display: 'grid',
        gridTemplateColumns: `repeat(${adjustedColumns}, 1fr)`,
        gridTemplateRows: `repeat(${rows}, 1fr)`,
        gap: `${spacing}px`,
        padding: `${padding}px`,
        margin: '0 auto',
        borderRadius: `${gridConfig?.borderRadius || 8}px`,
        ...backgroundStyle,
    }

    return (
        <div
            data-editor-selectable-key={dataConfigKey}
             data-editor-selectable-is-background={true}
            className={`memory-grid ${className}`}
            style={gridStyle}
            onClick={(e) => {
                if (isPreview && onGameAssetSelect && dataConfigKey) {
                    e.stopPropagation()
                    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
                    onGameAssetSelect(dataConfigKey, {
                        x: rect.left,
                        y: rect.top,
                        width: rect.width,
                        height: rect.height,
                    })
                }
            }}
        >
       
            {children}
        </div>
    )
}
