import React from 'react'
import { Label } from '@repo/shared/components/ui/label'
import CustomColorPicker from '@repo/shared/components/ui/color-picker'
import { GameTimerStyle } from '@repo/shared-game-utils/types/uiStyles'

interface GameTimerEditorProps {
    config: GameTimerStyle
    configKey: string
    onChange: (changes: Partial<GameTimerStyle>) => void
}

export const GameTimerEditor: React.FC<GameTimerEditorProps> = ({ config, onChange }) => {
    const progressColor = config?.progressColor || '#22c55e'
    const backgroundColor = config?.backgroundColor || '#4338ca'

    const handleProgressColorChange = (color: string) => {
        onChange({ progressColor: color })
    }

    const handleBackgroundColorChange = (color: string) => {
        onChange({ backgroundColor: color })
    }

    return (
        <div className="space-y-4">
            <div className="space-y-2">
                <Label>Timer Color</Label>
                <CustomColorPicker
                    color={progressColor}
                    onChange={handleProgressColorChange}
                />
            </div>

            <div className="space-y-2">
                <Label>Background Color</Label>
                <CustomColorPicker
                    color={backgroundColor}
                    onChange={handleBackgroundColorChange}
                />
            </div>
        </div>
    )
}
