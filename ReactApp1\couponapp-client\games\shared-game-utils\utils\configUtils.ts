export function mergeConfigWithFallback<T extends object>(userConfig: Partial<T> | null | undefined, defaultConfig: T): T {
    // If user config is completely missing, return the default config
    if (!userConfig) return { ...defaultConfig }

    const result = { ...defaultConfig }

    // Iterate through all properties in the user config
    Object.entries(userConfig).forEach(([key, value]) => {
        const typedKey = key as keyof T

        // If the user value exists and is not empty, use it
        // Otherwise, keep the default value (already in result)
        if (!isEmpty(value)) {
            // Directly assign the value without recursive merging
            ;(result[typedKey] as any) = value
        }
    })

    return result
}

const isEmpty = (value: any): boolean => {
    if (value === null || value === undefined) return true
    if (typeof value === 'string' && value.trim() === '') return false
    if (typeof value === 'object' && !Array.isArray(value)) {
        return Object.keys(value).length === 0
    }
    return false
}
