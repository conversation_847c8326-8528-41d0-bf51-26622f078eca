import React from 'react'
import { ConfigKeyEditor } from '../ConfigKeyEditor'
import { Card, CardContent, CardHeader, CardTitle } from '@repo/shared/components/ui/card'

interface TileSettingsEditorProps {
    config: any
    onChange: (config: any) => void
}

export const TileSettingsEditor: React.FC<TileSettingsEditorProps> = ({ config, onChange }) => {
    const tileConfigs = [
        { key: 'emptyTile', title: 'Empty Tile' },
        { key: 'tile2', title: 'Tile 2' },
        { key: 'tile4', title: 'Tile 4' },
        { key: 'tile8', title: 'Tile 8' },
        { key: 'tile16', title: 'Tile 16' },
        { key: 'tile32', title: 'Tile 32' },
        { key: 'tile64', title: 'Tile 64' },
        { key: 'tile128', title: 'Tile 128' },
        { key: 'tile256', title: 'Tile 256' },
        { key: 'tile512', title: 'Tile 512' },
        { key: 'tile1024', title: 'Tile 1024' },
        { key: 'tile2048', title: 'Tile 2048' },
        { key: 'tile4096', title: 'Tile 4096' },
        { key: 'tile8192', title: 'Tile 8192' },
    ]

    return (
        <div className="space-y-4">
            {tileConfigs.map(({ key, title }) => (
                <Card key={key}>
                    <CardHeader>
                        <CardTitle className="text-lg">{title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <ConfigKeyEditor
                            configKey={key}
                            config={config}
                            updateConfig={onChange}
                        />
                    </CardContent>
                </Card>
            ))}
        </div>
    )
} 