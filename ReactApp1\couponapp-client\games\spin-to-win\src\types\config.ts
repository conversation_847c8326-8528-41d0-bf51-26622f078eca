import { GameConfig, gameConfigKey } from '@repo/shared/lib/game/gameConfig'
import { AssetUrl, SoundAssetUrl } from '@repo/shared/lib/types/widgetSettings'
import {
    TextStyle,
    GameTextSettings,
    BackgroundStyle,
    ContainerStyle,
    ScoreElementStyle,
    ButtonIconStyle,
    CounterElementStyle,
    GameButtonStyle,
    GameSoundSwitchStyle,
    RewardComponentStyle,
    GameEndHandler,
    GameRewardsHandler as BaseGameRewardsHandler
} from '@repo/shared-game-utils/types/uiStyles'

export type PointerPosition =
    'top-center' |
    'bottom-center' |
    'left-center' |
    'right-center' |
    'center-center' |
    'top-left' |
    'top-right' |
    'bottom-left' |
    'bottom-right'

export type SpinWheelPointerSettings = {
    asset?: AssetUrl
    backgroundScaleMode?: 'cover' | 'contain' | 'pixelated' | 'pixelated-stretch' | 'pixelated-cover'
    offsetX?: number
    offsetY?: number
    position?: PointerPosition
}

export type RewardStyleSettings = {
    backgroundColor: string
    textColor: string
    useImage?: boolean
}

export type BlankSliceSettings = {
    count: number
    backgroundColor: string
    textColor: string
    text: string
    asset?: AssetUrl
    backgroundScaleMode?: 'cover' | 'contain' | 'pixelated' | 'pixelated-stretch' | 'pixelated-cover'
}

export type SpinWheelBorderSettings = {
    width: number
    color: string
    enabled: boolean
}

export type SpinWheelDividerSettings = {
    width: number
    color: string
    enabled: boolean
}

export type SpinWheelOverlaySettings = {
    asset?: AssetUrl
    backgroundScaleMode?: 'cover' | 'contain' | 'pixelated' | 'pixelated-stretch' | 'pixelated-cover'
    enabled: boolean
    offsetX?: number
    offsetY?: number
    scale?: number
}

export type SpinWheelSettings = {
    pointer?: SpinWheelPointerSettings
    rewardStyles?: Record<string, RewardStyleSettings>
    blankSlices?: BlankSliceSettings
    border?: SpinWheelBorderSettings
    dividers?: SpinWheelDividerSettings
    overlay?: SpinWheelOverlaySettings
    spinDuration?: number // Duration in seconds for the wheel to spin
    size?: number // Size of the wheel in pixels (width and height)
}

export type GameRewardsHandler = BaseGameRewardsHandler & {
    amountOfCoinsForReward?: number
    enableCtaButton?: boolean
    showRewardInGame?: boolean
}

export class ReactGameConfig extends GameConfig {
    @gameConfigKey({ name: 'Lives Handler', configEditorType: 'lives-handler' })
    gameEndHandler?: GameEndHandler

    @gameConfigKey({ name: 'Rewards Handler', configEditorType: 'rewards-handler' })
    gameRewardsHandler?: GameRewardsHandler

    @gameConfigKey({ name: 'Background Music', configEditorType: 'audio' })
    backgroundMusic?: SoundAssetUrl

    @gameConfigKey({ name: 'Spin Sound', configEditorType: 'audio' })
    spinSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Win Sound', configEditorType: 'audio' })
    winSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Picking Reward Sound', configEditorType: 'audio' })
    pickingRewardSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Game Over Sound', configEditorType: 'audio' })
    gameOverSound?: SoundAssetUrl



    @gameConfigKey({ name: 'Spin Button', configEditorType: 'game-button' })
    spinButton?: GameButtonStyle

    @gameConfigKey({
        name: 'Main Background',
        configEditorType: 'background',
        width: 800,
        height: 600,
    })
    mainBackground: BackgroundStyle

    @gameConfigKey({
        name: 'Score Style',
        configEditorType: 'counter',
    })
    scoreStyle: CounterElementStyle

    @gameConfigKey({
        name: 'Lives Style',
        configEditorType: 'counter',
    })
    livesStyle: CounterElementStyle

    @gameConfigKey({
        name: 'Game Over Text',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    gameOverText: GameTextSettings

    @gameConfigKey({
        name: 'Out of Lives Try Again Button',
        configEditorType: 'game-button',
        editorSettings: { toggleableVisibility: true },
    })
    outOfLivesContinueButton: GameButtonStyle

    @gameConfigKey({ name: 'Lose Life Overlay', configEditorType: 'container' })
    loseLifeOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Lose Life Title', configEditorType: 'text' })
    loseLifeTitle?: GameTextSettings

    @gameConfigKey({ name: 'Continue Button', configEditorType: 'game-button' })
    continueButton?: GameButtonStyle

    @gameConfigKey({ name: 'Game Over Overlay', configEditorType: 'container' })
    gameOverOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Game Over Title', configEditorType: 'text' })
    gameOverTitle?: GameTextSettings

    @gameConfigKey({ name: 'Game Over Message', configEditorType: 'text' })
    gameOverMessage?: GameTextSettings

    @gameConfigKey({ name: 'Game Over Continue Button', configEditorType: 'game-button' })
    gameOverContinueButton?: GameButtonStyle

    @gameConfigKey({ name: 'Reward Overlay', configEditorType: 'container' })
    rewardOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Reward Title', configEditorType: 'text' })
    rewardTitle?: GameTextSettings

    @gameConfigKey({ name: 'Reward Claim Button', configEditorType: 'game-button' })
    rewardClaimButton?: GameButtonStyle

    @gameConfigKey({ name: 'Out of Lives Overlay', configEditorType: 'container' })
    outOfLivesOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Out of Lives Title', configEditorType: 'text' })
    outOfLivesTitle?: GameTextSettings

    @gameConfigKey({ name: 'Claim Reward Overlay', configEditorType: 'container' })
    claimRewardOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Claim Reward Title', configEditorType: 'text' })
    claimRewardTitle?: GameTextSettings

    @gameConfigKey({ name: 'Claim Reward Button', configEditorType: 'game-button' })
    claimRewardButton?: GameButtonStyle

    @gameConfigKey({ name: 'Try Again Button', configEditorType: 'game-button' })
    tryAgainButton?: GameButtonStyle

    @gameConfigKey({ name: 'Reward Component', configEditorType: 'reward-component-style' })
    rewardComponent?: RewardComponentStyle

    @gameConfigKey({
        name: 'Spin Wheel',
        configEditorType: 'spin-wheel',
        editorSettings: {
            spinDuration: {
                type: 'slider',
                min: 2,
                max: 10,
                step: 0.5,
                label: 'Spin Duration (seconds)'
            },
            size: {
                type: 'slider',
                min: 100,
                max: 800,
                step: 10,
                label: 'Wheel Size (px)'
            }
        }
    })
    spinWheel?: SpinWheelSettings

    @gameConfigKey({ name: 'Sound Switch', configEditorType: 'sound-switch', editorSettings: { toggleableVisibility: true } })
    gameSoundSwitch?: GameSoundSwitchStyle
}

export const defaultGameConfig: ReactGameConfig = {
    gameEndHandler: {
        useLives: false,
        livesCount: 3,
    },

    gameRewardsHandler: {
        rewardsEnabled: false,
        enableCtaButton: true,
        showRewardInGame: false,
        amountOfCoinsForReward: 2048,
    },

    backgroundMusic: {
        enabled: true,
        absoluteUrl: 'https://example.com/background-music.mp3',
    },

    spinSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/spin-sound.mp3',
    },

    winSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/win-sound.mp3',
    },

    pickingRewardSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/picking-reward-sound.mp3',
    },

    gameOverSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/game-over-sound.mp3',
    },



    spinButton: {
        textConfig: {
            text: 'SPIN',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 22,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 50,
        offsetX: 0,
        offsetY: 20,
        useBackgroundColor: true,
        borderRadius: 8,
        isVisible: true,
    },

    mainBackground: {
        fill: '#faf8ef',
        useBackgroundColor: true,
    },

    scoreStyle: {
        textConfig: {
            text: 'Score: ',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#776e65',
                isVisible: true,
            },
        },
        fill: '#eee4da',
        width: 100,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
        isVisible: true,
    },

    livesStyle: {
        textConfig: {
            text: 'Lives: ',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#776e65',
                isVisible: true,
            },
        },
        fill: '#eee4da',
        width: 100,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
        isVisible: true,
        position: 'left',
        iconStyle: {
            iconName_tIcon: 'Heart',
            color: '#e74c3c',
            size: 20,
            position: 'left',
            isVisible: true,
            fill: true,
        },
    },

    gameOverText: {
        text: 'Game Over!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    continueButton: {
        textConfig: {
            text: 'Ok',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    outOfLivesContinueButton: {
        textConfig: {
            text: 'Ok',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    loseLifeOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    loseLifeTitle: {
        text: 'You Lost a Life',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
       borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    gameOverTitle: {
        text: 'Game Over',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverMessage: {
        text: 'Better luck next time!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 22,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverContinueButton: {
        textConfig: {
            text: 'Continue',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    rewardOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
       borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    rewardTitle: {
        text: 'You Earned a Reward!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    rewardClaimButton: {
        textConfig: {
            text: 'Claim Reward',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    outOfLivesOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
       borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    outOfLivesTitle: {
        text: 'Out of Lives',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    claimRewardTitle: {
        text: 'Claim Your Reward or Try Again',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    claimRewardOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    claimRewardButton: {
        textConfig: {
            text: 'Claim Reward',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    tryAgainButton: {
        textConfig: {
            text: 'Try Again',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    rewardComponent: {
        imageWidth: 150,
        imageHeight: 150,
        titleFontFamily: 'Londrina Solid',
        titleFontSize: 22,
        titleColor: '#776e65',
        titleTextAlign: 'left',
        descriptionFontFamily: 'Poppins',
        descriptionFontSize: 16,
        descriptionColor: '#776e65',
        descriptionTextAlign: 'left',
        padding: 16,
        maxWidth: 400,
        layout: 'horizontal-left',
        imageBorderRadius: 8,
        imageMargin: 16,
        containerBackgroundColor: 'transparent',
        containerBorderRadius: 12,
        containerShadow: false,
        spacing: 16,
        showImage: true,
        showTitle: true,
        showDescription: true,
    },

    spinWheel: {
        pointer: {
            asset: {
                assetId: '',
            },
            backgroundScaleMode: 'cover',
            offsetX: 0,
            offsetY: 0,
            position: 'top-center'
        },
        rewardStyles: {},
        blankSlices: {
            count: 2,
            backgroundColor: '#cccccc',
            textColor: '#000000',
            text: 'Try Again',
            backgroundScaleMode: 'cover'
        },
        border: {
            width: 4,
            color: '#000000',
            enabled: true
        },
        dividers: {
            width: 2,
            color: '#ffffff',
            enabled: true
        },
        overlay: {
            asset: {
                assetId: '',
            },
            backgroundScaleMode: 'cover',
            enabled: false,
            offsetX: 0,
            offsetY: 0,
            scale: 1
        },
        spinDuration: 6, // Default spin duration in seconds
        size: 450 // Default size in pixels
    },

    gameSoundSwitch: {
        onAsset: {
            absoluteUrl: 'https://placehold.co/48x48/f5cb5c/000000?text=ON',
        },
        offAsset: {
            absoluteUrl: 'https://placehold.co/48x48/f5cb5c/000000?text=OFF',
        },
        width: 48,
        height: 48,
        offsetX: 0,
        offsetY: 0,
        position: 'right',
        alignment: 'right', // Keep for backward compatibility
        isVisible: true,
    },
}
