import React from 'react'
import { Label } from '@repo/shared/components/ui/label'
import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { Switch } from '@repo/shared/components/ui/switch'
import { Slider } from '@repo/shared/components/ui/slider'
import { ContainerStyle } from '../types/uiStyles'

interface ContainerEditorProps {
    config: ContainerStyle
    onChange: (changes: any) => void
}

export function ContainerEditor({ config, onChange }: ContainerEditorProps) {
    return (
        <div className="space-y-6">
            {/* Background Settings */}
            <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-3">Background</h4>
                <div className="space-y-4">
                    <div>
                        <Label className="mb-2 block">Background Image</Label>
                        <AssetPicker
                            onSelect={(assetId) =>
                                onChange({
                                    asset: { assetId },
                                })
                            }
                            assetUrl={config?.asset}
                            extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']}
                        />
                    </div>

                    <div>
                        <div className="flex items-center space-x-2 mb-2">
                            <Label className="flex-grow">Background Color</Label>
                            <Switch checked={config?.useBackgroundColor !== false} onCheckedChange={(useBackgroundColor) => onChange({ useBackgroundColor })} />
                        </div>
                        <div className={`transition-opacity ${config?.useBackgroundColor !== false ? 'opacity-100' : 'opacity-40'}`}>
                            <ColorPicker color={config?.fill || '#ffffff'} onChange={(fill) => onChange({ fill })} />
                        </div>
                    </div>
                </div>
            </div>

            {/* Border Radius Settings */}
            <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-3">Border Radius</h4>
                <div className="space-y-4">
                    <div>
                        <div className="flex justify-between items-center mb-2">
                            <Label></Label>
                            <span className="text-sm">{config?.borderRadius || 0}px</span>
                        </div>
                        <Slider defaultValue={[config?.borderRadius || 0]} max={50} step={1} onValueChange={(values) => onChange({ borderRadius: values[0] })} />
                    </div>
                    <div>
                        <div className="flex items-center space-x-2 mb-2">
                            <Label className="flex-grow">Pixelated</Label>
                            <Switch
                                checked={config?.pixelated === true}
                                onCheckedChange={(pixelated) => onChange({ pixelated })}
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* Max Width Settings */}
            <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-3">Max Width</h4>
                <div className="space-y-4">
                    <div>
                        <div className="flex justify-between items-center mb-2">
                            <Label>Maximum Width</Label>
                            <span className="text-sm">{config?.maxWidth || 0}px</span>
                        </div>
                        <Slider defaultValue={[config?.maxWidth || 0]} max={1000} step={10} onValueChange={(values) => onChange({ maxWidth: values[0] })} />
                        <div className="mt-2 text-xs text-muted-foreground">Set to 0 for no maximum width</div>
                    </div>
                </div>
            </div>

            {/* Min Height Settings */}
            <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-3">Min Height</h4>
                <div className="space-y-4">
                    <div>
                        <div className="flex justify-between items-center mb-2">
                            <Label>Minimum Height</Label>
                            <span className="text-sm">{config?.minHeight || 0}px</span>
                        </div>
                        <Slider defaultValue={[config?.minHeight || 0]} max={800} step={10} onValueChange={(values) => onChange({ minHeight: values[0] })} />
                        <div className="mt-2 text-xs text-muted-foreground">Set to 0 for no minimum height</div>
                    </div>
                </div>
            </div>

            {/* Padding Settings */}
            <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-3">Padding</h4>
                <div className="space-y-4">
                    <div>
                        <Label className="mb-2">Top Padding</Label>
                        <div className="flex justify-between items-center mb-2">
                            <span className="text-sm">{config?.padding?.top || 0}px</span>
                        </div>
                        <Slider
                            defaultValue={[config?.padding?.top || 0]}
                            max={50}
                            step={1}
                            onValueChange={(values) => onChange({
                                padding: {
                                    ...config?.padding,
                                    top: values[0]
                                }
                            })}
                        />
                    </div>

                    <div>
                        <Label className="mb-2">Right Padding</Label>
                        <div className="flex justify-between items-center mb-2">
                            <span className="text-sm">{config?.padding?.right || 0}px</span>
                        </div>
                        <Slider
                            defaultValue={[config?.padding?.right || 0]}
                            max={50}
                            step={1}
                            onValueChange={(values) => onChange({
                                padding: {
                                    ...config?.padding,
                                    right: values[0]
                                }
                            })}
                        />
                    </div>

                    <div>
                        <Label className="mb-2">Bottom Padding</Label>
                        <div className="flex justify-between items-center mb-2">
                            <span className="text-sm">{config?.padding?.bottom || 0}px</span>
                        </div>
                        <Slider
                            defaultValue={[config?.padding?.bottom || 0]}
                            max={50}
                            step={1}
                            onValueChange={(values) => onChange({
                                padding: {
                                    ...config?.padding,
                                    bottom: values[0]
                                }
                            })}
                        />
                    </div>

                    <div>
                        <Label className="mb-2">Left Padding</Label>
                        <div className="flex justify-between items-center mb-2">
                            <span className="text-sm">{config?.padding?.left || 0}px</span>
                        </div>
                        <Slider
                            defaultValue={[config?.padding?.left || 0]}
                            max={50}
                            step={1}
                            onValueChange={(values) => onChange({
                                padding: {
                                    ...config?.padding,
                                    left: values[0]
                                }
                            })}
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}
