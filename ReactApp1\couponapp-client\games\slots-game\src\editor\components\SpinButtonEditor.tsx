import { Label } from '@repo/shared/components/ui/label'
import { Input } from '@repo/shared/components/ui/input'
import { Switch } from '@repo/shared/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { SpinButtonStyle } from '../../types/config'
import { TextEditor } from '@repo/shared-game-utils/editor-components/TextEditor'
import { useConfigKeyDefinition } from '@repo/shared-game-utils/hooks/useConfigType'

interface SpinButtonEditorProps {
    config: SpinButtonStyle
    configKey: string
    onChange: (changes: any) => void
}

export function SpinButtonEditor({ config, configKey, onChange }: SpinButtonEditorProps) {
    // Get the definition to check if visibility is toggleable
    const definition = useConfigKeyDefinition(configKey)
    const isVisibilityToggleable = definition?.editorSettings?.toggleableVisibility

    return (
        <div className="space-y-4">
            {isVisibilityToggleable && (
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Button Visibility</Label>
                    <Switch 
                        checked={config?.isVisible !== false} 
                        onCheckedChange={(isVisible) => onChange({ isVisible })} 
                    />
                </div>
            )}

            <div>
                <TextEditor
                    text={
                        config?.textConfig || {
                            text: '',
                            style: {},
                        }
                    }
                    onChange={(textConfig) => onChange({ textConfig })}
                    configKey={'textConfig'}
                />
            </div>

            <div>
                <Label className="mb-2 block">Background Image</Label>
                <AssetPicker 
                    onSelect={(assetId) => onChange({ asset: { assetId } })} 
                    assetUrl={config?.asset} 
                    extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']} 
                />
            </div>

            <div>
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Background Color</Label>
                    <Switch 
                        checked={config?.useBackgroundColor !== false} 
                        onCheckedChange={(useBackgroundColor) => onChange({ useBackgroundColor })} 
                    />
                </div>
                <div className={`transition-opacity ${config?.useBackgroundColor !== false ? 'opacity-100' : 'opacity-40'}`}>
                    <ColorPicker 
                        color={config?.fill || '#8f7a66'} 
                        onChange={(fill) => onChange({ fill })} 
                    />
                </div>
            </div>

            <div>
                <Label className="mb-2 block">Button Position</Label>
                <Select 
                    value={config?.alignment || 'center'} 
                    onValueChange={(alignment) => onChange({ alignment })}
                >
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select position" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            <div>
                <Label className="mb-2 block">Dimensions</Label>
                <div className="grid grid-cols-2 gap-2">
                    <div>
                        <Label className="text-xs">Width</Label>
                        <Input 
                            type="number" 
                            value={config?.width || 120} 
                            onChange={(e) => onChange({ width: parseInt(e.target.value, 10) })} 
                        />
                    </div>
                    <div>
                        <Label className="text-xs">Height</Label>
                        <Input 
                            type="number" 
                            value={config?.height || 40} 
                            onChange={(e) => onChange({ height: parseInt(e.target.value, 10) })} 
                        />
                    </div>
                </div>
            </div>

            <div>
                <Label className="mb-2 block">Offset</Label>
                <div className="grid grid-cols-2 gap-2">
                    <div>
                        <Label className="text-xs">X</Label>
                        <Input 
                            type="number" 
                            value={config?.offsetX || 0} 
                            onChange={(e) => onChange({ offsetX: parseInt(e.target.value, 10) })} 
                        />
                    </div>

                    <div>
                        <Label className="text-xs">Y</Label>
                        <Input 
                            type="number" 
                            value={config?.offsetY || 0} 
                            onChange={(e) => onChange({ offsetY: parseInt(e.target.value, 10) })} 
                        />
                    </div>
                </div>
            </div>

            <div>
                <Label className="mb-2 block">Border Radius</Label>
                <Input 
                    type="number" 
                    value={config?.borderRadius || 4} 
                    onChange={(e) => onChange({ borderRadius: parseInt(e.target.value, 10) })} 
                />
            </div>
        </div>
    )
} 