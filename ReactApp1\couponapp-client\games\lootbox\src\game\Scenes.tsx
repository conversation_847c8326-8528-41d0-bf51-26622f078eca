import React, { useCallback, useEffect, useRef } from 'react'

import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { useAtom } from 'jotai'
import { gamePreviewScreenAtomFamily, selectedEditorItem<PERSON>tom } from '@repo/shared/lib/atoms/editor-atoms'
import { GameButtonStyle, ReactGameConfig } from '../types/config'
import { GameButton } from '@repo/shared-game-utils/components/GameButton'
import { GameContainer } from '@repo/shared-game-utils/components/GameContainer'
import { GameText } from '@repo/shared-game-utils/components/GameText'
import { RewardComponent } from '@repo/shared-game-utils/components/RewardComponent'
import MainGame, { useGameState } from './GameMain'
import { useGameRewards } from '@repo/shared/lib/game/useGameRewards'
import { useMusic } from '@repo/shared-game-utils/hooks/useSounds'
import { useGame<PERSON>tom } from '@repo/shared/lib/atoms/atom-extensions'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { getBackgroundStyle } from '@repo/shared-game-utils/utils/gameStyleUtils'


// Lose Life Screen - Shows the message when player loses a life
export const TryAgainScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, resolveAssetUrl } = useGame<ReactGameConfig>()
    useMusic(config.gameOverSound, true, false)


    return (
        <div className="h-full w-full" data-editor-is-background={true} data-editor-selectable-key="tryAgainBackground" >
            <div className="h-full w-full flex flex-col items-center justify-center">
                <GameContainer
                    config={config.loseLifeOverlay}
                    dataConfigKey="loseLifeOverlay"
                    resolveAssetUrl={resolveAssetUrl}
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                >
                    <GameText
                        config={config.loseLifeTitle}
                        dataConfigKey="loseLifeTitle"
                        className="mb-4"
                        onClick={(e) => {
                            e.stopPropagation()
                        }}
                    />
                    <GameButton
                        config={config.continueButton}
                        dataConfigKey="continueButton"
                        onClick={(e) => {
                            e.stopPropagation()
                            if (onButtonClick) {
                                onButtonClick()
                            }
                        }}
                    />
                </GameContainer>
            </div>
        </div>
    )
}

// Claim Reward or Try Again Screen - Shows when player has a reward to claim
export const ClaimRewardOrTryAgainScreen: React.FC<{
    onClaimReward: () => void
    onTryAgain: () => void
}> = ({ onClaimReward, onTryAgain }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    return (
        <div className="h-full w-full" data-editor-is-background={true} data-editor-selectable-key="claimRewardOrTryAgainBackground" style={getBackgroundStyle(config.claimRewardOrTryAgainBackground, resolveAssetUrl)}>
            <div className="h-full w-full flex flex-col items-center justify-center">
                <GameContainer
                    config={config.claimRewardOverlay}
                    dataConfigKey="claimRewardOverlay"
                    resolveAssetUrl={resolveAssetUrl}
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                >
                    <GameText
                        config={config.claimRewardTitle}
                        dataConfigKey="claimRewardTitle"
                        className="mb-4"
                        onClick={(e) => {
                            e.stopPropagation()
                        }}
                    />

                    <div className="flex flex-row gap-4">
                        <GameButton
                            config={config.claimRewardButton}
                            dataConfigKey="claimRewardButton"
                            onClick={(e) => {
                                e.stopPropagation()
                                if (onClaimReward) {
                                    onClaimReward()
                                }
                            }}
                        />

                        <GameButton
                            config={config.tryAgainButton}
                            dataConfigKey="tryAgainButton"
                            onClick={(e) => {
                                e.stopPropagation()
                                if (onTryAgain) {
                                    onTryAgain()
                                }
                            }}
                        />
                    </div>
                </GameContainer>
            </div>
        </div>
    )
}

// Game Over Screen - Shows when player reached the target score
export const GameOverScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    useMusic(config.gameOverSound, true, false)

    return (
        <div className="h-full w-full" data-editor-is-background={true} data-editor-selectable-key="gameOverBackground" >
            <div className="h-full w-full flex flex-col items-center justify-center">
                <GameContainer
                    config={config.gameOverOverlay}
                    dataConfigKey="gameOverOverlay"
                    resolveAssetUrl={resolveAssetUrl}
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                >
                    <GameText
                        config={config.gameOverTitle}
                        dataConfigKey="gameOverTitle"
                        className="mb-4"
                        onClick={(e) => {
                            e.stopPropagation()
                        }}
                    />
                    <GameText
                        config={config.gameOverMessage}
                        dataConfigKey="gameOverMessage"
                        className="mb-4"
                        onClick={(e) => {
                            e.stopPropagation()
                        }}
                    />
                    <GameButton
                        config={config.gameOverContinueButton || config.continueButton}
                        dataConfigKey="gameOverContinueButton"
                        onClick={(e) => {
                            e.stopPropagation()
                            if (onButtonClick) {
                                onButtonClick()
                            }
                        }}
                    />
                </GameContainer>
            </div>
        </div>
    )
}

// Reward Screen - Shows the reward screen when rewards are enabled
export const RewardScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    const { attemptsTaken } = useGameState()
    const { reward } = useGameRewards({ gameWidgetId: widgetId, attempt: attemptsTaken })

    useMusic(config.winSound, true, false)

    return (
        <div className="h-full w-full" data-editor-is-background={true} data-editor-selectable-key="rewardBackground" >
            <div className="h-full w-full flex flex-col items-center justify-center">
                <GameContainer
                    config={config.rewardOverlay}
                    dataConfigKey="rewardOverlay"
                    resolveAssetUrl={resolveAssetUrl}
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                >
                    <GameText
                        config={config.rewardTitle}
                        dataConfigKey="rewardTitle"
                        className="mb-4"
                        onClick={(e) => {
                            e.stopPropagation()
                        }}
                    />

                    {(config.gameRewardsHandler?.showRewardInGame || true) && (
                        <div className="mb-4">
                            <RewardComponent dataConfigKey="rewardComponent" reward={reward} />
                        </div>
                    )}

                    {config.gameRewardsHandler?.enableCtaButton !== false && (
                        <GameButton
                            config={config.rewardClaimButton || config.continueButton}
                            dataConfigKey="rewardClaimButton"
                            onClick={(e) => {
                                e.stopPropagation()
                                if (onButtonClick) {
                                    onButtonClick()
                                }
                            }}
                        />
                    )}
                </GameContainer>
            </div>
        </div>
    )
}

// Out of Lives Screen - Shows when player has no lives left
export const OutOfLivesScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    useMusic(config.gameOverSound, true, false)


    return (
        <div className="h-full w-full" data-editor-is-background={true} data-editor-selectable-key="outOfLivesBackground" >
            <div className="h-full w-full flex flex-col items-center justify-center">
                <GameContainer
                    config={config.outOfLivesOverlay}
                    dataConfigKey="outOfLivesOverlay"
                    resolveAssetUrl={resolveAssetUrl}
                >
                    <GameText config={config.outOfLivesTitle} dataConfigKey="outOfLivesTitle" className="mb-4" />

                    <GameButton
                        config={config.outOfLivesContinueButton}
                        dataConfigKey="outOfLivesContinueButton"
                        onClick={(e) => {
                            e.stopPropagation()
                            if (onButtonClick) {
                                onButtonClick()
                            }
                        }}
                    />
                </GameContainer>
            </div>
        </div>
    )
}

// Picking Reward Screen - Shows when the lootbox is being opened and reward is being picked
export const PickingRewardScreen: React.FC = () => {
    const { config, widgetId, isPreview, setCurrentScreenId, resolveAssetUrl } = useGame<ReactGameConfig>()
    const { attemptsTaken } = useGameState()
    const { pickReward } = useGameRewards({ gameWidgetId: widgetId, attempt: attemptsTaken })

    const isPickingRef = useRef(false)

    useMusic(config.pickingRewardSound, true, false)

    const handlePickReward = useCallback(async () => {

        if (!config.gameRewardsHandler?.rewardsEnabled) {
            setCurrentScreenId('try-again')
            return
        }

        const start = Date.now()
        const pickResult = await pickReward()

        //Delay between picking screen animation and next screen
        const delay = 1500 - (Date.now() - start)

        if (delay > 0) {
            await new Promise(resolve => setTimeout(resolve, delay))
        }

        if (pickResult.hasWon) {
            setCurrentScreenId('claim-reward')
        } else {
            setCurrentScreenId('try-again')
        }

    }, [widgetId, config.gameRewardsHandler?.rewardsEnabled, setCurrentScreenId, pickReward]) // Add dependencies


    useEffect(() => {
        if (isPreview) return
        if (isPickingRef.current) return

        isPickingRef.current = true

        handlePickReward()

    }, [isPreview, handlePickReward]) // Update dependencies for useEffect

    return (
        <div className="h-full w-full" data-editor-is-background={true} data-editor-selectable-key="pickingRewardBackground" >
            <div className="h-full w-full flex flex-col items-center justify-center">
            </div>
        </div>
    )
}

export interface PreviewSceneProps {
    config: ReactGameConfig
    widgetId: string
    resolveAssetUrl: (id: AssetUrl) => string
}

export const PreviewScene: React.FC<PreviewSceneProps> = ({ config, widgetId, resolveAssetUrl }) => {
    const [selectedScreen, setSelectedScreen] = useAtom(gamePreviewScreenAtomFamily(widgetId))
    const initialGameScreenChecked = useRef(false)
    const currentScreen = selectedScreen ?? 'main'

    return (
        <MainGame config={config} widgetId={widgetId} isPreview={true} resolveAssetUrl={resolveAssetUrl} currentScreenId={currentScreen as any} initialGameScreenChecked={initialGameScreenChecked} />
    )
}
