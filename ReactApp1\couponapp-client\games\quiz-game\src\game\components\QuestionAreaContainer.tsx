import React from 'react'
import { QuestionAreaContainerStyle } from '../../types/config'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { motion, AnimatePresence } from 'framer-motion'
import { getPixelatedClipPath } from '@repo/shared-game-utils/utils/gameStyleUtils'

interface QuestionAreaContainerProps {
    config?: QuestionAreaContainerStyle
    dataConfigKey?: string
    className?: string
    children: React.ReactNode
    resolveAssetUrl?: (asset: AssetUrl) => string
    onClick?: (e: React.MouseEvent) => void
    style?: React.CSSProperties
    questionKey: string | number // Unique key for the current question to trigger animation
    questionImage?: AssetUrl // Optional question-specific image
}

export const QuestionAreaContainer: React.FC<QuestionAreaContainerProps> = ({
    config,
    dataConfigKey,
    className = '',
    children,
    resolveAssetUrl,
    onClick,
    style = {},
    questionKey,
    questionImage,
}) => {
    if (!config) {
        return <div className={className} style={style}>{children}</div>
    }

    // Determine which image to use: question image takes priority over config asset
    const backgroundImageAsset = questionImage || config.asset
    const hasBackgroundImage = backgroundImageAsset && resolveAssetUrl

    const containerStyle: React.CSSProperties = {
        backgroundColor: config.useBackgroundColor !== false ? (config.fill ?? 'transparent') : 'transparent',
        backgroundImage: hasBackgroundImage ? `url(${resolveAssetUrl(backgroundImageAsset)})` : undefined,
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        borderRadius: config.pixelated ? '0' : (config.borderRadius !== undefined ? `${config.borderRadius}px` : undefined),
        clipPath: config.pixelated ? getPixelatedClipPath(config.borderRadius || 0) : undefined,
        overflow: 'hidden',
        maxWidth: config.maxWidth ? `${config.maxWidth}px` : undefined,
        minHeight: config.minHeight ? `${config.minHeight}px` : undefined,
        padding: config.padding ? `${config.padding.top || 0}px ${config.padding.right || 0}px ${config.padding.bottom || 0}px ${config.padding.left || 0}px` : undefined,
        ...style,
    }

    // Wrapper for text content when background image is present
    const renderChildren = () => {
        if (questionImage) {
            return (
                <div
                    className="bg-black bg-opacity-70 rounded-lg p-1"
                    style={{ backdropFilter: 'blur(2px)' }}
                >
                    {children}
                </div>
            )
        }
        return children
    }

    const animationType = config.animationType || 'none'

    const variants = {
        enter: (direction: number) => ({
            x: direction > 0 ? 100 : -100,
            opacity: 0,
        }),
        center: {
            zIndex: 1,
            x: 0,
            opacity: 1,
        },
        exit: (direction: number) => ({
            zIndex: 0,
            x: direction < 0 ? 100 : -100,
            opacity: 0,
        }),
    }

    // Store previous question index to determine slide direction
    const [prevQuestionKey, setPrevQuestionKey] = React.useState(questionKey);
    const [direction, setDirection] = React.useState(0);

    React.useEffect(() => {
        if (questionKey !== prevQuestionKey) {
            // @ts-ignore
            setDirection(questionKey > prevQuestionKey ? 1 : -1);
            setPrevQuestionKey(questionKey);
        }
    }, [questionKey, prevQuestionKey]);

    if (animationType === 'card-slide') {
        return (
            <AnimatePresence initial={false} custom={direction} mode="wait">
                <motion.div
                    key={questionKey}
                    data-editor-selectable-key={dataConfigKey}
                    data-editor-selectable-is-background={true}
                    className={`flex flex-col items-center justify-center ${className}`}
                    style={containerStyle}
                    onClick={onClick}
                    custom={direction}
                    variants={variants}
                    initial="enter"
                    animate="center"
                    exit="exit"
                    transition={{
                        x: { type: 'spring', stiffness: 500, damping: 25 },
                        opacity: { duration: 0.15 },
                    }}
                >
                    {renderChildren()}
                </motion.div>
            </AnimatePresence>
        )
    }

    // Default: no animation
    return (
        <div
            data-editor-selectable-key={dataConfigKey}
            data-editor-selectable-is-background={true}
            className={`flex flex-col items-center justify-center ${className}`}
            style={containerStyle}
            onClick={onClick}
        >
            {renderChildren()}
        </div>
    )
}