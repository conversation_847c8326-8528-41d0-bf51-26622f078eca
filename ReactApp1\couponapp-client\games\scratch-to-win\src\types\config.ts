import { GameConfig, gameConfigKey } from '@repo/shared/lib/game/gameConfig'
import { AssetUrl, SoundAssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { GameTextSettings, GameButtonStyle, BackgroundStyle, ContainerStyle, GameEndHandler, GameRewardsHandler, GameSoundSwitchStyle, RewardComponentStyle, ScoreElementStyle, CounterElementStyle } from '@repo/shared-game-utils/types/uiStyles'

export type ScratchOverlayStyle = {
    asset?: AssetUrl
    borderRadius?: number
}

export type ScratchUnderlayStyle = {
    asset?: AssetUrl
    borderRadius?: number
}

export class ReactGameConfig extends GameConfig {
    @gameConfigKey({ name: 'Lives Handler', configEditorType: 'lives-handler' })
    gameEndHandler?: GameEndHandler

    @gameConfigKey({ name: '<PERSON><PERSON><PERSON> Handler', configEditorType: 'rewards-handler' })
    gameRewardsHandler?: GameRewardsHandler

    @gameConfigKey({ name: 'Background Music', configEditorType: 'audio' })
    backgroundMusic?: SoundAssetUrl

    @gameConfigKey({ name: 'Win Sound', configEditorType: 'audio' })
    winSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Game Over Sound', configEditorType: 'audio' })
    gameOverSound?: SoundAssetUrl

    @gameConfigKey({
        name: 'Main Background',
        configEditorType: 'background',
        width: 800,
        height: 600,
    })
    mainBackground: BackgroundStyle

    @gameConfigKey({ name: 'Scratch Overlay', configEditorType: 'scratch-overlay' })
    scratchOverlay?: ScratchOverlayStyle

    @gameConfigKey({ name: 'Scratch Underlay', configEditorType: 'scratch-overlay' })
    scratchUnderlay?: ScratchUnderlayStyle

    @gameConfigKey({
        name: 'Game Over Text',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    gameOverText: GameTextSettings

    @gameConfigKey({
        name: 'Out of Lives Try Again Button',
        configEditorType: 'game-button',
        editorSettings: { toggleableVisibility: true },
    })
    outOfLivesContinueButton: GameButtonStyle

    @gameConfigKey({ name: 'Lose Life Overlay', configEditorType: 'container' })
    loseLifeOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Try again', configEditorType: 'game-button' })
    tryAgainButton?: GameButtonStyle

    @gameConfigKey({ name: 'Game Over Overlay', configEditorType: 'container' })
    gameOverOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Game Over Title', configEditorType: 'text' })
    gameOverTitle?: GameTextSettings

    @gameConfigKey({ name: 'Game Over Message', configEditorType: 'text' })
    gameOverMessage?: GameTextSettings

    @gameConfigKey({ name: 'Game Over Continue Button', configEditorType: 'game-button' })
    gameOverContinueButton?: GameButtonStyle

    @gameConfigKey({ name: 'Reward Claim Button', configEditorType: 'game-button', editorSettings: { toggleableVisibility: true } })
    rewardClaimButton?: GameButtonStyle

    @gameConfigKey({ name: 'Out of Lives Overlay', configEditorType: 'container' })
    outOfLivesOverlay?: ContainerStyle

    @gameConfigKey({
        name: 'Out of Lives Title',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    outOfLivesTitle?: GameTextSettings

    @gameConfigKey({
        name: 'Out of Lives Description',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    outOfLivesDescription?: GameTextSettings

    @gameConfigKey({ name: 'Reward Overlay', configEditorType: 'container' })
    rewardOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Reward Title', configEditorType: 'text' })
    rewardTitle?: GameTextSettings

    @gameConfigKey({ name: 'Claim Reward Overlay', configEditorType: 'container' })
    claimRewardOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Claim Reward Title', configEditorType: 'text' })
    claimRewardTitle?: GameTextSettings

    @gameConfigKey({ name: 'Reward Component', configEditorType: 'reward-component-style' })
    rewardComponent?: RewardComponentStyle

    @gameConfigKey({ name: 'Main Screen Reward Component', configEditorType: 'reward-component-style' })
    mainScreenRewardComponent?: RewardComponentStyle

    @gameConfigKey({ name: 'Lives Style', configEditorType: 'counter' })
    livesStyle: CounterElementStyle

    @gameConfigKey({ name: 'Sound Switch', configEditorType: 'sound-switch', editorSettings: { toggleableVisibility: true } })
    gameSoundSwitch?: GameSoundSwitchStyle
}

export const defaultGameConfig: ReactGameConfig = {
    gameEndHandler: {
        useLives: false,
        livesCount: 13,
    },

    gameRewardsHandler: {
        rewardsEnabled: false,
    },

    backgroundMusic: {
        enabled: true,
        absoluteUrl: 'https://example.com/background-music.mp3',
    },

    winSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/win-sound.mp3',
    },

    gameOverSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/game-over-sound.mp3',
    },

    mainBackground: {
        fill: '#faf8ef',
        useBackgroundColor: true,
    },

    gameOverText: {
        text: 'Game Over!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    tryAgainButton: {
        textConfig: {
            text: 'Try again',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
        isVisible: true,
    },

    outOfLivesContinueButton: {
        textConfig: {
            text: 'Ok',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    loseLifeOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 20,
            right: 20,
            bottom: 20,
            left: 20,
        },
        maxWidth: 500,
    },

    gameOverOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 20,
            right: 20,
            bottom: 20,
            left: 20,
        },
        maxWidth: 500,
    },

    gameOverTitle: {
        text: 'Game Over',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverMessage: {
        text: 'Better luck next time!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 22,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverContinueButton: {
        textConfig: {
            text: 'Continue',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    rewardOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 20,
            right: 20,
            bottom: 20,
            left: 20,
        },
        maxWidth: 500,
    },

    rewardTitle: {
        text: 'You Earned a Reward!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    rewardClaimButton: {
        textConfig: {
            text: 'Claim Reward',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    outOfLivesOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 20,
            right: 20,
            bottom: 20,
            left: 20,
        },
        maxWidth: 500,
    },

    outOfLivesTitle: {
        text: 'Out of Lives',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
            fontWeight: 'bold',
        },
    },

    outOfLivesDescription: {
        text: 'You have no more lives left. Come back later to try again!',
        style: {
            fontFamily_tFontFamily: 'Poppins',
            fontSize: 18,
            fill: '#776e65',
            isVisible: true,
            fontWeight: 'normal',
        },
    },

    rewardComponent: {
        imageWidth: 150,
        imageHeight: 150,
        titleFontFamily: 'Londrina Solid',
        titleFontSize: 22,
        titleColor: '#776e65',
        titleTextAlign: 'left',
        descriptionFontFamily: 'Poppins',
        descriptionFontSize: 16,
        descriptionColor: '#776e65',
        descriptionTextAlign: 'left',
        padding: 16,
        maxWidth: 400,
        layout: 'horizontal-left',
        imageBorderRadius: 8,
        imageMargin: 16,
        containerBackgroundColor: 'transparent',
        containerBorderRadius: 12,
        containerShadow: false,
        spacing: 16,
        showImage: true,
        showTitle: true,
        showDescription: true,
    },

    mainScreenRewardComponent: {
        imageWidth: 150,
        imageHeight: 150,
        imageBorderRadius: 8,
        containerBackgroundColor: 'transparent',
        showImage: true,
    },

    livesStyle: {
        textConfig: {
            text: '',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 22,
                fill: '#000000',
                isVisible: true,
            },
        },
        fill: '#f5cb5c',
        width: 100,
        height: 48,
        useBackgroundColor: true,
        borderRadius: 8,
        offsetX: 10,
        offsetY: 10,
    },

    gameSoundSwitch: {
        onAsset: {
            absoluteUrl: 'https://placehold.co/48x48/f5cb5c/000000?text=ON',
        },
        offAsset: {
            absoluteUrl: 'https://placehold.co/48x48/f5cb5c/000000?text=OFF',
        },
        width: 48,
        height: 48,
        offsetX: 0,
        offsetY: 0,
        alignment: 'right',
        isVisible: true,
    },

    scratchOverlay: {
        asset: {
            absoluteUrl: 'https://placehold.co/300x300/f5cb5c/000000?text=SCRATCH',
        },
        borderRadius: 12,
    },

    scratchUnderlay: {
        asset: {
            absoluteUrl: 'https://placehold.co/300x300/f5cb5c/000000?text=YOU+LOST',
        },
        borderRadius: 12,
    },
}
