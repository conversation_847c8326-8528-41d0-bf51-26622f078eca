// Store event listeners in a closure so we can remove them later
const listenerMap = new Map<Element, (e: MouseEvent) => void>()

export const makeElementsInteractive = (onGameAssetSelect: (assetKey: string, bounds: { x: number; y: number; width: number; height: number }) => void) => {
    // Remove all previously attached listeners
    listenerMap.forEach((listener, element) => {
        element.removeEventListener('click', listener)
    })

    // Clear the map
    listenerMap.clear()

    const elements = document.querySelectorAll('[data-editor-selectable-key]')
    elements.forEach((element) => {
        //@ts-ignore
        element.style.cursor = 'pointer'

        // Create a new listener for this element
        const clickHandler = (e: MouseEvent) => {
            const configKey = element.getAttribute('data-editor-selectable-key')
            const isBackground = element.getAttribute('data-editor-selectable-is-background')
            const bounds = element.getBoundingClientRect()

            if (!isBackground) {
                e.stopPropagation()
            }

            console.log('clicked interactive element: ', configKey, 'isBackground: ', isBackground)
            onGameAssetSelect(configKey, bounds)
        }

        // Store reference to the listener
        listenerMap.set(element, clickHandler)

        // Add the listener
        element.addEventListener('click', clickHandler)
    })
}
