import { useGame<PERSON>tom } from '@repo/shared/lib/atoms/atom-extensions'
import { GameSoundSwitchStyle } from '../types/uiStyles'
import { useGame } from '../hooks/useGame'
import { getBackgroundImageStyles } from '../utils/gameStyleUtils'

interface GameSoundSwitchProps {
    config: GameSoundSwitchStyle
    dataConfigKey: string
}

export const GameSoundSwitch: React.FC<GameSoundSwitchProps> = ({ config, dataConfigKey }) => {
    const { isPreview, widgetId, resolveAssetUrl } = useGame()
    const [soundEnabled, setSoundEnabled] = useGameAtom(widgetId, 'soundEnabled', true)

    // Don't render if switch is explicitly set to not visible
    if (config?.isVisible === false) {
        if (isPreview) {
            return (
                <div
                    className="opacity-[0.2]"
                    data-editor-selectable-key={dataConfigKey}
                    style={{
                        transform: `translate(${config?.offsetX || 0}px, ${config?.offsetY || 0}px)`,
                        display: 'flex',
                    }}
                >
                    [hidden]
                </div>
            )
        }
        return null
    }

    // We no longer need alignment styles as they're handled by the parent container

    // Handle toggle sound on/off
    const handleToggleSound = () => {
        setSoundEnabled(!soundEnabled)
    }

    // Get the appropriate asset based on the sound state
    const currentAsset = soundEnabled ? config.onAsset : config.offAsset

    // Get background image styles with pixel art support
    const backgroundStyles = currentAsset && resolveAssetUrl
        ? getBackgroundImageStyles({
            asset: currentAsset,
            backgroundScaleMode: config.backgroundScaleMode
          }, resolveAssetUrl)
        : {}

    return (
        <button
            data-editor-selectable-key={dataConfigKey}
            style={{
                width: config?.width || 48,
                height: config?.height || 48,
                display: 'flex',
                transform: `translate(${config?.offsetX || 0}px, ${config?.offsetY || 0}px)`,
                ...backgroundStyles
            }}
            onClick={handleToggleSound}
            aria-label={soundEnabled ? 'Turn sound off' : 'Turn sound on'}
        />
    )
}
