import React from 'react'
import { GameTimerHandler } from '@repo/shared-game-utils/types/uiStyles'
import { TimerSystemEditor } from '@repo/shared/components/editor/TimerSystemEditor'
import { TimerHandlerConfig } from '@repo/shared/components/editor/TimerSystemEditor'

interface TimerSettingsEditorProps {
    config: any
    onChange: (config: any) => void
}

export const TimerSettingsEditor: React.FC<TimerSettingsEditorProps> = ({ config, onChange }) => {
    const timerHandler = (config.gameTimerHandler || {}) as GameTimerHandler

    const handleTimerConfigChange = (timerConfig: TimerHandlerConfig) => {
        onChange({
            gameTimerHandler: timerConfig,
        })
    }

    return (
        <TimerSystemEditor
            timerHandler={timerHandler}
            onConfigChange={handleTimerConfigChange}
            minDuration={5}
            maxDuration={120}
            step={1}
        />
    )
}
