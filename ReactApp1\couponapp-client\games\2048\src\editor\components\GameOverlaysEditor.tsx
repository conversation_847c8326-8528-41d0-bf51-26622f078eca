import React from 'react'
import { ConfigKeyEditor } from '../ConfigKeyEditor'
import { Card, CardContent, CardHeader, CardTitle } from '@repo/shared/components/ui/card'

interface GameOverlaysEditorProps {
    config: any
    onChange: (config: any) => void
}

export const GameOverlaysEditor: React.FC<GameOverlaysEditorProps> = ({ config, onChange }) => {
    const overlayConfigs = [
        { key: 'loseLifeOverlay', title: 'Lose Life Overlay' },
        { key: 'outOfLivesOverlay', title: 'Out of Lives Overlay' },
        { key: 'gameOverOverlay', title: 'Game Over Overlay' },
        { key: 'rewardOverlay', title: 'Reward Overlay' },
        { key: 'rewardComponent', title: 'Reward Component' },
    ]

    return (
        <div className="space-y-4">
            {overlayConfigs.map(({ key, title }) => (
                <Card key={key}>
                    <CardHeader>
                        <CardTitle className="text-lg">{title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <ConfigKeyEditor
                            configKey={key}
                            config={config}
                            updateConfig={onChange}
                        />
                    </CardContent>
                </Card>
            ))}
        </div>
    )
} 