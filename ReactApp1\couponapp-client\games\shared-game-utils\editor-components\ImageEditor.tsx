import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { ConfigKeyEditorComponentProps } from '@repo/shared/lib/game/game'
import { getConfigKeyDefinition } from '@repo/shared/lib/game/gameConfig'
import { ReactGameConfig } from '../../quiz-game/src/types/config'
import { useConfigKeyDefinition } from '../hooks/useConfigType'
interface ImageEditorProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
}

export function ImageEditor({ configKey, config, onChange }: ImageEditorProps) {
        const definition = useConfigKeyDefinition(configKey)

    return (
        <>
            {definition?.width && definition?.height && (
                <span className="ms-[2px] text-sm text-muted-foreground mb-2">
                    ({definition.width}x{definition.height}px)
                </span>
            )}

            <AssetPicker
                exactSize={definition?.width && definition?.height ? { width: definition.width, height: definition.height } : undefined}
                onSelect={(assetId) => onChange({ [configKey]: { assetId } })}
                assetUrl={config[configKey] as any}
                extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']}
            />
        </>
    )
}
