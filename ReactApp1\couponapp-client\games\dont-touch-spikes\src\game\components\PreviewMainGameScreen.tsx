import React, { useLayoutEffect, useRef, useState } from 'react'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { ReactGameConfig } from '../../types/config'
import { GameSoundSwitch } from '@repo/shared-game-utils/components/GameSoundSwitch'
import { GameText } from '@repo/shared-game-utils/components/GameText'

const useGameAreaScaler = (baseWidth: number) => {
    const gameAreaRef = useRef<HTMLDivElement>(null)
    const [scale, setScale] = useState(1)

    useLayoutEffect(() => {
        const gameAreaEl = gameAreaRef.current
        if (!gameAreaEl) return

        const resizeObserver = new ResizeObserver(entries => {
            for (const entry of entries) {
                const { width } = entry.contentRect
                setScale(width / baseWidth)
            }
        })
        resizeObserver.observe(gameAreaEl)

        return () => resizeObserver.disconnect()
    }, [baseWidth])

    return { gameAreaRef, scale }
}

export function PreviewMainGameScreen() {
    const { config, resolveAssetUrl } = useGame<ReactGameConfig>()
    const { gameAreaRef, scale } = useGameAreaScaler(900)

    const playerImageUrl = resolveAssetUrl(config.player)

    return (
        <div className="game-content-area w-full h-full flex flex-col ">
            <div className="w-full h-full flex items-center justify-center ">
                <div
                    id="game-area"
                    ref={gameAreaRef}
                    className="relative h-full "
                    style={{
                        backgroundImage: config.gameArea
                            ? `url(${resolveAssetUrl(config.gameArea)})`
                            : 'none',
                        aspectRatio: '9 / 16',
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'center',
                        backgroundSize: 'contain',
                    }}
                    data-editor-selectable-key="gameArea"
                >
                    <div className="absolute inset-0 flex items-center justify-center">
                        <div style={{ transform: `scale(${scale})` }}>
                            <GameText
                                config={{ style: config.scoreStyle, text: '0' }}
                                dataConfigKey="scoreStyle"
                            />
                        </div>
                    </div>
                    
                    {/* Player */}
                    {playerImageUrl && (
                        <div className="absolute inset-0 flex items-center justify-center">
                            <img
                                src={playerImageUrl}
                                alt="Player"
                                className="cursor-pointer"
                                style={{
                                    width: '100px',
                                    height: '70px',
                                    objectFit: 'contain',
                                    transform: `scale(${scale})`,
                                    zIndex: 150,
                                }}
                                data-editor-selectable-key="player"
                                onClick={(e) => {
                                    e.stopPropagation()
                                }}
                            />
                        </div>
                    )}

                    {/* Instruction Text */}
                    {config.instructionText?.style?.isVisible !== false && (
                        <div className="absolute inset-0 flex items-center justify-center">
                            <div 
                                style={{ 
                                    transform: `scale(${scale})`,
                                    marginTop: '200px' // Position below the player
                                }}
                            >
                                <GameText
                                    config={{ style: config.instructionText.style, text: config.instructionText.text }}
                                    dataConfigKey="instructionText"
                                />
                            </div>
                        </div>
                    )}
                </div>
            </div>

            <div className="absolute w-full h-full pointer-events-none">
                <div className="w-full flex">
                    <div
                        className={`w-full flex ${config.gameSoundSwitch?.alignment === 'center' ? 'justify-center' : config.gameSoundSwitch?.alignment === 'right' ? 'justify-end' : 'justify-start'} p-2`}
                    >
                        <div className="pointer-events-auto">
                            <GameSoundSwitch
                                config={config.gameSoundSwitch}
                                dataConfigKey="gameSoundSwitch"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
