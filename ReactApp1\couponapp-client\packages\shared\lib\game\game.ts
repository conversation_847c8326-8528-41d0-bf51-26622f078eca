import { AssetUrl } from "../types/widgetSettings";

export interface GameRuntimeComponentProps<T> {
  config: T;
  widgetId: string;
  inputEnabled?: boolean;
  isPreview: boolean;
  resolveAssetUrl: (id: AssetUrl) => string | undefined;
}

export interface ConfigKeyEditorComponentProps {
  configKey: keyof any;
  config: any;
  updateConfig: (config: any) => void;
}

export interface GameEditorComponentProps {
  config: any;
  updateConfig: (config: any) => void;
}

export interface GameModule {
  id: string;
  name: string;
  editorComponent: React.ComponentType<GameEditorComponentProps>;
  configKeyEditor: React.ComponentType<ConfigKeyEditorComponentProps>;
  runtimeComponent: React.ComponentType<GameRuntimeComponentProps<any>>;
  previewScreens?: PreviewScreenDefinition[];
  defaultConfig: any;
  previewScene?: React.ComponentType<any>;
  configType: any
}

export interface PreviewScreenDefinition {
  screenId: string;
  displayName: string;
  visibleCheck?: (config: any) => boolean;
}
