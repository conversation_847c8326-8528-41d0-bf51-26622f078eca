import React, { useEffect, useRef, useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useGameState } from '../../game/GameMain'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { ReactGameConfig } from '../../types/config'

interface ScratchOverlayProps {
    children: React.ReactNode
    isRevealed?: boolean
    setIsRevealed?: (revealed: boolean) => void
    isPreview?: boolean
    onScratchComplete?: () => void
}

export const ScratchOverlay: React.FC<ScratchOverlayProps> = ({ children, isRevealed, setIsRevealed, isPreview, onScratchComplete }) => {
    // Hardcoded values
    const revealPercentage = 50

    // Get game state for lives management
    const { config, resolveAssetUrl } = useGame<ReactGameConfig>()
    const { setScratchProcessed, lives, setLivesCount, isAttemptCounted, isLifeCounted, setIsLifeCounted } = useGameState()
    const useLives = config.gameEndHandler?.useLives

    // If preview mode, render a static overlay image that fades out when revealed
    if (isPreview) {
        const overlayUrl = resolveAssetUrl(config.scratchOverlay.asset)
        return (
            <div
                data-editor-selectable-key="scratchOverlay"
                className="relative overflow-hidden w-[300px] h-[300px]"
                style={{ borderRadius: config.scratchOverlay?.borderRadius ? `${config.scratchOverlay.borderRadius}px` : undefined }}
            >
                {isRevealed && (
                    <div className="w-full h-full" style={{ opacity: 1 }}>
                        {children}
                    </div>
                )}

                {!isRevealed && (
                    <div
                        className="absolute top-0 left-0"
                        style={{
                            width: '100%',
                            height: '100%',
                            backgroundImage: `url(${overlayUrl})`,
                            backgroundSize: 'cover',
                        }}
                    />
                )}
            </div>
        )
    }
    const containerRef = useRef<HTMLDivElement>(null)
    const canvasRef = useRef<HTMLCanvasElement>(null)
    const childrenRef = useRef<HTMLDivElement>(null)

    // Offscreen canvas for mask calculation
    const offscreenCanvasRef = useRef<HTMLCanvasElement | null>(null)
    const offscreenCtxRef = useRef<CanvasRenderingContext2D | null>(null)
    const imageRef = useRef<HTMLImageElement | null>(null) // To store the loaded image
    const isMaskDirtyRef = useRef(false) // Flag to trigger visible canvas update
    const animationFrameRef = useRef<number>()

    // isRevealed and setIsRevealed are now passed as props from parent
    const [isScratchStarted, setIsScratchStarted] = useState(false)
    const [isDrawing, setIsDrawing] = useState(false)
    const [isMouseDown, setIsMouseDown] = useState(false)
    const [lastPosition, setLastPosition] = useState({ x: 0, y: 0 })
    const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 })
    const totalPixels = useRef(0)
    const scratchedPixels = useRef(0)

    // Initialize the canvas when the component mounts or when the children's size changes
    useEffect(() => {
        const updateCanvasSize = () => {
            if (!childrenRef.current) return

            const { width, height } = childrenRef.current.getBoundingClientRect()
            // Check if size actually changed to prevent unnecessary updates
            if (canvasSize.width !== width || canvasSize.height !== height) {
                setCanvasSize({ width, height })
            }
        }

        // Initial size update
        updateCanvasSize()

        // Set up resize observer to detect changes in children's size
        const resizeObserver = new ResizeObserver(updateCanvasSize)
        if (childrenRef.current) {
            resizeObserver.observe(childrenRef.current)
        }

        return () => {
            resizeObserver.disconnect()
        }
    }, [])

    // Update canvas when size changes
    useEffect(() => {
        const canvas = canvasRef.current
        if (!canvas || canvasSize.width === 0 || canvasSize.height === 0) return
        const ctx = canvas.getContext('2d')
        if (!ctx) return

        // Set visible canvas dimensions
        canvas.width = canvasSize.width
        canvas.height = canvasSize.height

        // Initialize or resize offscreen canvas
        if (!offscreenCanvasRef.current) {
            offscreenCanvasRef.current = document.createElement('canvas')
        }
        const offscreenCanvas = offscreenCanvasRef.current
        offscreenCanvas.width = canvasSize.width
        offscreenCanvas.height = canvasSize.height
        offscreenCtxRef.current = offscreenCanvas.getContext('2d', { willReadFrequently: true }) // Optimization hint
        const offscreenCtx = offscreenCtxRef.current
        if (!offscreenCtx) return

        // Fill offscreen canvas with opaque color (mask base)
        offscreenCtx.fillStyle = 'black' // Opaque color
        offscreenCtx.fillRect(0, 0, canvasSize.width, canvasSize.height)

        // Load and draw the image onto the visible canvas
        const imageUrl = resolveAssetUrl(config.scratchOverlay.asset)
        const image = new Image()
        image.crossOrigin = 'anonymous' // Handle potential CORS issues if image is hosted elsewhere
        image.onload = () => {
            imageRef.current = image // Store image for redraws
            // Initial draw of the image on the visible canvas
            ctx.drawImage(image, 0, 0, canvasSize.width, canvasSize.height)
            // Calculate total pixels (using dimensions, assuming opaque image for now)
            totalPixels.current = canvasSize.width * canvasSize.height
            scratchedPixels.current = 0
            isMaskDirtyRef.current = true // Trigger initial render via animation frame if needed (though drawImage does it here)
        }
        image.onerror = () => {
            console.error('Failed to load scratch overlay image:', imageUrl)
            // Fallback: fill visible canvas with solid color?
            ctx.fillStyle = 'grey' // Example fallback
            ctx.fillRect(0, 0, canvasSize.width, canvasSize.height)
            imageRef.current = null // Ensure we don't try to draw a failed image
            totalPixels.current = canvasSize.width * canvasSize.height
            scratchedPixels.current = 0
            isMaskDirtyRef.current = true
        }
        image.src = imageUrl
    }, [canvasSize, config.scratchOverlay.asset, resolveAssetUrl])

    // Function to get position relative to canvas
    const getPosition = useCallback((event: MouseEvent | TouchEvent): { x: number; y: number } => {
        const canvas = canvasRef.current
        if (!canvas) return { x: 0, y: 0 }

        const rect = canvas.getBoundingClientRect()

        if ('touches' in event) {
            // Touch event
            return {
                x: event.touches[0].clientX - rect.left,
                y: event.touches[0].clientY - rect.top,
            }
        } else {
            // Mouse event
            return {
                x: event.clientX - rect.left,
                y: event.clientY - rect.top,
            }
        }
    }, [])

    // Function to draw scratch
    const drawScratch = useCallback(
        (x: number, y: number, lastX: number, lastY: number) => {
            // Draw on the offscreen canvas (mask)
            const offscreenCtx = offscreenCtxRef.current
            if (!offscreenCtx) return

            // Set line properties for mask
            offscreenCtx.globalCompositeOperation = 'destination-out' // Erase mask
            offscreenCtx.lineWidth = 40
            offscreenCtx.lineCap = 'round'
            offscreenCtx.lineJoin = 'round'

            // Draw line on mask
            offscreenCtx.beginPath()
            offscreenCtx.moveTo(lastX, lastY)
            offscreenCtx.lineTo(x, y)
            offscreenCtx.stroke()

            // Calculate scratched pixels using the offscreen mask
            const offscreenCanvas = offscreenCanvasRef.current
            if (!offscreenCanvas) return

            // IMPORTANT: Getting image data frequently can be slow.
            // Consider optimizing this check (e.g., check less often, approximate) if performance issues arise.
            const imageData = offscreenCtx.getImageData(0, 0, offscreenCanvas.width, offscreenCanvas.height)
            const pixels = imageData.data
            let transparentPixels = 0

            // Check alpha channel (every 4th byte) of the mask
            for (let i = 3; i < pixels.length; i += 4) {
                if (pixels[i] === 0) {
                    // Fully transparent in the mask
                    transparentPixels++
                }
            }

            scratchedPixels.current = transparentPixels
            const percentScratched = totalPixels.current > 0 ? (transparentPixels / totalPixels.current) * 100 : 0

            // Check if enough has been scratched
            if (percentScratched >= revealPercentage && !isRevealed) {
                // Mark the scratch as processed to prevent reloading exploit
                setScratchProcessed(true)
                console.log('ScratchOverlay - Setting isRevealed to true')
                setIsRevealed(true)
                console.log('Scratch reveal completed!')
                if (onScratchComplete) {
                    console.log('ScratchOverlay - Calling onScratchComplete callback')
                    onScratchComplete()
                }
            }

            // Signal that the visible canvas needs to be updated
            isMaskDirtyRef.current = true
        },
        [revealPercentage, isRevealed, setScratchProcessed, setIsRevealed, onScratchComplete]
    )

    // Mouse/Touch event handlers
    const handleStart = useCallback(
        (event: MouseEvent | TouchEvent) => {
            setIsDrawing(true)
            setIsMouseDown(true)
            setIsScratchStarted(true)
            const position = getPosition(event)
            setLastPosition(position)

        },
        [getPosition, useLives, isAttemptCounted, lives, setLivesCount]
    )

    const handleMove = useCallback(
        (event: MouseEvent | TouchEvent) => {
            // Only continue drawing if the mouse button is still pressed
            if (!isMouseDown) return

            // If we re-enter the canvas while the mouse is still down, resume drawing
            if (!isDrawing && isMouseDown) {
                setIsDrawing(true)
            }

             if (!isLifeCounted && useLives) {
                if (useLives) {
                    setLivesCount(lives - 1)
                }
                setIsLifeCounted(true)
            }

            const position = getPosition(event)
            drawScratch(position.x, position.y, lastPosition.x, lastPosition.y)
            setLastPosition(position)
        },
        [isDrawing, isMouseDown, drawScratch, getPosition, lastPosition]
    )

    const handleEnd = useCallback(() => {
        
        setIsDrawing(false)
        setIsMouseDown(false)
    }, [])

    // Handle mouse up event on the window to detect when mouse is released outside the canvas
    useEffect(() => {
        const handleWindowMouseUp = () => {
            setIsMouseDown(false)
            setIsDrawing(false)
        }

        window.addEventListener('mouseup', handleWindowMouseUp)
        window.addEventListener('touchend', handleWindowMouseUp)

        return () => {
            window.removeEventListener('mouseup', handleWindowMouseUp)
            window.removeEventListener('touchend', handleWindowMouseUp)
        }
    }, [])

    // Set up event listeners
    useEffect(() => {
        const canvas = canvasRef.current
        if (!canvas) return

        // Mouse events
        canvas.addEventListener('mousedown', handleStart)
        canvas.addEventListener('mousemove', handleMove)
        canvas.addEventListener('mouseup', handleEnd)

        // When mouse leaves the canvas, only stop drawing but keep tracking if mouse is down
        const handleMouseLeave = () => {
            setIsDrawing(false)
        }

        // When mouse enters the canvas and button is still pressed, resume drawing
        const handleMouseEnter = (event: MouseEvent) => {
            if (isMouseDown && event.buttons === 1) {
                // 1 means left mouse button is pressed
                setIsDrawing(true)
                const position = getPosition(event)
                setLastPosition(position)
            }
        }

        canvas.addEventListener('mouseleave', handleMouseLeave)
        canvas.addEventListener('mouseenter', handleMouseEnter)

        // Touch events
        canvas.addEventListener('touchstart', handleStart)
        canvas.addEventListener('touchmove', handleMove)
        canvas.addEventListener('touchend', handleEnd)

        return () => {
            // Clean up event listeners
            canvas.removeEventListener('mousedown', handleStart)
            canvas.removeEventListener('mousemove', handleMove)
            canvas.removeEventListener('mouseup', handleEnd)
            canvas.removeEventListener('mouseleave', handleMouseLeave)
            canvas.removeEventListener('mouseenter', handleMouseEnter)

            canvas.removeEventListener('touchstart', handleStart)
            canvas.removeEventListener('touchmove', handleMove)
            canvas.removeEventListener('touchend', handleEnd)
        }
    }, [handleStart, handleMove, handleEnd, isMouseDown, getPosition])

    // Animation frame loop to update the visible canvas based on the mask
    useEffect(() => {
        const renderVisibleCanvas = () => {
            if (!isMaskDirtyRef.current || !canvasRef.current || !offscreenCanvasRef.current || !imageRef.current) {
                return // No need to render if mask isn't dirty or refs aren't ready
            }

            const ctx = canvasRef.current.getContext('2d')
            const offscreenCanvas = offscreenCanvasRef.current
            const image = imageRef.current

            if (!ctx || !image) {
                return
            }

            // 1. Clear previous frame (optional, depends on desired effect, drawImage should cover)
            // ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);

            // 2. Draw the original image
            ctx.globalCompositeOperation = 'source-over' // Ensure default drawing mode
            ctx.drawImage(image, 0, 0, canvasRef.current.width, canvasRef.current.height)

            // 3. Apply the mask
            ctx.globalCompositeOperation = 'destination-in' // Keep where image and mask overlap
            ctx.drawImage(offscreenCanvas, 0, 0)

            // 4. Reset composite operation for subsequent draws (if any)
            ctx.globalCompositeOperation = 'source-over'

            isMaskDirtyRef.current = false // Reset the dirty flag
        }

        const tick = () => {
            renderVisibleCanvas()
            animationFrameRef.current = requestAnimationFrame(tick)
        }

        // Start the loop
        animationFrameRef.current = requestAnimationFrame(tick)

        // Cleanup function to cancel the loop when the component unmounts
        return () => {
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current)
            }
        }
    }, []) // Empty dependency array ensures this runs once on mount and cleans up on unmount

    return (
        <div
            ref={containerRef}
            className="relative overflow-hidden "
            style={{
                borderRadius: config.scratchOverlay?.borderRadius ? `${config.scratchOverlay.borderRadius}px` : undefined,
            }}
        >
            <div
                ref={childrenRef}
                className=" top-0 left-0 w-100 h-100 p-[1px]"
                style={{
                    zIndex: 1,
                    opacity: isScratchStarted || isRevealed ? 1 : 0,
                    borderRadius: '23px',
                }}
            >
                {children}
            </div>

            <motion.canvas
                ref={canvasRef}
                className="absolute top-0 left-0 w-100 h-100 cursor-pointer overflow-hidden"
                animate={{
                    opacity: isRevealed ? 0 : 1,
                    pointerEvents: isRevealed ? 'none' : 'auto',
                }}
                transition={{ duration: 0.5 }}
                style={{
                    zIndex: 2,
                    touchAction: 'none',
                }}
            />
        </div>
    )
}


