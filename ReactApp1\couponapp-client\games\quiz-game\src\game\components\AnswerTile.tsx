import { ButtonIconStyle } from '@repo/shared-game-utils/types/uiStyles'
import { AnswerTileStyle } from '../../types/config'
import { Icon } from '@repo/shared/components/ui/icon-picker'
import { motion } from 'framer-motion'
import { getPixelatedClipPath, getTextAlignmentStyles } from '@repo/shared-game-utils/utils/gameStyleUtils'
import { GameText } from '@repo/shared-game-utils/components/GameText'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'

// GameButtonIcon component for handling button icons
interface GameButtonIconProps {
    iconStyle?: ButtonIconStyle
}


 const getAnswerTileStyle = (tileConfig?: AnswerTileStyle): React.CSSProperties => {
    if (!tileConfig) return {}

    const { resolveAssetUrl } = useGame()

    // Determine background size and image rendering based on scale mode
    let backgroundSize: string | undefined = undefined;
    let imageRendering: string | undefined = undefined;

    if (tileConfig.asset) {
        if (tileConfig.backgroundScaleMode === 'pixelated') {
            backgroundSize = 'contain';
            imageRendering = 'pixelated';
        } else if (tileConfig.backgroundScaleMode === 'pixelated-cover') {
            backgroundSize = 'cover';
            imageRendering = 'pixelated';
        } else if (tileConfig.backgroundScaleMode === 'pixelated-stretch') {
            backgroundSize = '100% 100%';
            imageRendering = 'pixelated';
        } else if (tileConfig.backgroundScaleMode) {
            backgroundSize = tileConfig.backgroundScaleMode;
        } else {
            backgroundSize = 'cover';
        }
    }

    return {
        // Button styling
        backgroundColor: tileConfig.useBackgroundColor ? tileConfig.fill || '#8f7a66' : undefined,
        backgroundImage: tileConfig?.asset && resolveAssetUrl ? `url(${resolveAssetUrl(tileConfig.asset)})` : undefined,
        backgroundSize,
        backgroundPosition: tileConfig.asset ? 'center' : undefined,

        //@ts-ignore
        imageRendering: imageRendering,

        // Border styling
        borderRadius: tileConfig.pixelated ? '0' : (tileConfig.borderRadius ? `${tileConfig.borderRadius}px` : '4px'),
        clipPath: tileConfig.pixelated ? getPixelatedClipPath(tileConfig.borderRadius || 4) : undefined,

        // Remove padding from button as it will be handled by GameText
        padding: 0,

        // Fixed dimensions for answer tiles
        width: '180px',
        height: '60px',
    }
}



export const GameButtonIcon: React.FC<GameButtonIconProps> = ({ iconStyle }) => {
    if (!iconStyle || !iconStyle.iconName_tIcon || iconStyle.isVisible === false) {
        return null
    }

    const iconSize = iconStyle.size || 24
    const iconColor = iconStyle.color || '#000000'
    const offsetX = iconStyle.offsetX || 0
    const offsetY = iconStyle.offsetY || 0
    const fill = iconStyle.fill ? iconColor : '#00000000'
    const strokeWidth = iconStyle.strokeWidth || undefined

    return (
        <div style={{ transform: `translate(${offsetX}px, ${offsetY}px)` }}>
            <Icon name={iconStyle.iconName_tIcon as any} size={iconSize} color={iconColor} fill={fill} strokeWidth={strokeWidth} />
        </div>
    )
}

// AnswerTile component for quiz answer tiles
interface AnswerTileProps {
    config: AnswerTileStyle
    onClick: (e: React.MouseEvent) => void
    dataConfigKey: string
    state?: 'default' | 'correct' | 'incorrect'
}

// Animation variants for different states
const tileVariants = {
    initial: { scale: 1 },
    hover: { scale: 1 },
    tap: { scale: 0.95 },
    correct: {
        scale: [1, 0.98, 1],
        transition: {
            duration: 0.3,
            repeat: 0
        }
    },
    incorrect: {
        scale: [1, 0.98, 1],
        transition: {
            duration: 0.3,
            repeat: 0
        }
    }
}

export const AnswerTile: React.FC<AnswerTileProps> = ({ config, onClick, dataConfigKey, state = 'default' }) => {
    // Extract text style properties
    const { isPreview } = useGame()

    const textStyle = config?.textConfig?.style || {}
    const textAlign = textStyle.textAlign || 'center'
    const alignmentStyles = getTextAlignmentStyles(textAlign)
    const iconStyle = config?.iconStyle

    if (!isPreview && config.isVisible == false) {
        return null
    }

    const getIconPosition = () => {
        if (!iconStyle || !iconStyle.iconName_tIcon || iconStyle.isVisible === false) {
            return { flexDirection: 'row' as const }
        }

        const position = iconStyle?.position || 'left'
        switch (position) {
            case 'left':
                return { flexDirection: 'row' as const }
            case 'right':
                return { flexDirection: 'row-reverse' as const }
            case 'top':
                return { flexDirection: 'column' as const }
            case 'bottom':
                return { flexDirection: 'column-reverse' as const }
            case 'center':
                return { flexDirection: 'row' as const, position: 'relative' as const }
            default:
                return { flexDirection: 'row' as const }
        }
    }

    // Determine if we should display the icon
    const hasIcon = iconStyle && iconStyle.iconName_tIcon && iconStyle.isVisible !== false

    // Determine which animation variant to use based on the state
    const getAnimationVariant = () => {
        // For correct/incorrect states, we want to play the animation once
        // and then stay at scale: 1 (the final state of the animation)
        if (state === 'correct') {
            return 'correct'
        } else if (state === 'incorrect') {
            return 'incorrect'
        }
        return 'initial'
    }

    return (
        <motion.button
            data-editor-selectable-key={dataConfigKey}
            style={{
                ...getAnswerTileStyle(config),
                display: 'flex',
                ...alignmentStyles,
                opacity: isPreview && config.isVisible == false ? 0.5 : 1,
                ...getIconPosition(),
                alignItems: 'center',
                justifyContent: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',
                gap: iconStyle?.position === 'center' ? 0 : '8px', // Space between icon and text, except for center position
            }}
            onClick={onClick}
            initial="initial"
            // Only apply hover and tap animations when in default state
            whileHover={state === 'default' ? "hover" : undefined}
            whileTap={state === 'default' ? "tap" : undefined}
            animate={getAnimationVariant()}
            variants={tileVariants}
        >
            {isPreview && config.isVisible == false && <div>[hidden]</div>}

            {config.isVisible !== false && (
                <>
                    {hasIcon && iconStyle?.position !== 'center' && <GameButtonIcon iconStyle={iconStyle} />}
                    <div
                        style={{
                            width: !hasIcon || iconStyle?.position === 'center' ? '100%' : 'auto',
                            textAlign: textAlign as 'left' | 'center' | 'right',
                        }}
                    >
                        <GameText config={config?.textConfig} dataConfigKey={null} />
                    </div>
                    {hasIcon && iconStyle?.position === 'center' && (
                        <div
                            style={{
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: `translate(-50%, -50%) translate(${iconStyle?.offsetX || 0}px, ${iconStyle?.offsetY || 0}px)`,
                            }}
                        >
                            <GameButtonIcon iconStyle={iconStyle} />
                        </div>
                    )}
                </>
            )}
        </motion.button>
    )
}
