import React, { useEffect } from 'react'
import { CampaignReward } from '@repo/shared/lib/game/useGameRewards'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { motion, AnimatePresence } from 'framer-motion'
import clsx from 'clsx'
import { RewardComponentStyle } from '../types/uiStyles'
import { useGame } from '../hooks/useGame'
import { getPixelatedClipPath } from '../utils/gameStyleUtils'

/**
 * A component for displaying rewards in-game with customizable layouts
 */
export const RewardComponent: React.FC<{
    dataConfigKey?: string
    config?: RewardComponentStyle
    reward?: CampaignReward
}> = ({ dataConfigKey, config, reward }) => {
    const { config: gameConfig, resolveAssetUrl, isPreview } = useGame<any>()
    const rewardConfig = config 

    useEffect(() => {
        console.log('reward changed', reward)
    }, [reward])


    if (isPreview) {
        reward = {
             id: 'preview-reward',
            name: 'Reward Name',
            description: 'Reward description',
            image: {
                absoluteUrl: 'https://placehold.co/150x150',
            },
        }
    }

    // Determine if we should use a horizontal layout
    const isHorizontal = rewardConfig?.layout === 'horizontal-left' || rewardConfig?.layout === 'horizontal-right'
    const imageOnRight = rewardConfig?.layout === 'horizontal-right'
    const showImage = rewardConfig?.showImage !== false
    const showTitle = rewardConfig?.showTitle !== false
    const showDescription = rewardConfig?.showDescription !== false

    // Animation variants
    const fadeInOut = {
        hidden: { opacity: 0 },
        visible: { opacity: 1 },
    }

    // Default placeholder values if reward has no content but is not null
    const title = reward?.name
    const description = reward?.description

    const renderPlaceholderImage = () => {
        return <img src="https://placehold.co/150x150" alt="Reward" className="w-full h-full object-cover" />
    }

    // Function to render the image based on AssetUrl type
    const renderImage = (image?: AssetUrl) => {
        if (!image) return renderPlaceholderImage()

        // If we have an assetId and resolveAssetUrl function, use it
        if (image.assetId && resolveAssetUrl) {
            return <img src={resolveAssetUrl(image) ?? 'https://placehold.co/150x150'} alt="Reward" className="w-full h-full object-cover" />
        }

        // If we have an absoluteUrl, use it directly
        if (image.absoluteUrl) {
            return <img src={image.absoluteUrl} alt="Reward" className="w-full h-full object-cover" />
        }

        return renderPlaceholderImage()
    }

    // Generate appropriate shadow class
    const shadowClass = rewardConfig?.containerShadow ? 'shadow-md' : ''

    // Get padding value - try to map to Tailwind if it's a standard value
    const getPaddingClass = () => {
        const padding = rewardConfig?.padding || 16
        switch (padding) {
            case 0:
                return 'p-0'
            case 4:
                return 'p-1'
            case 8:
                return 'p-2'
            case 12:
                return 'p-3'
            case 16:
                return 'p-4'
            case 20:
                return 'p-5'
            case 24:
                return 'p-6'
            case 32:
                return 'p-8'
            case 40:
                return 'p-10'
            case 48:
                return 'p-12'
            case 64:
                return 'p-16'
            default:
                return '' // Will use inline style if not a standard value
        }
    }

    const paddingClass = getPaddingClass()
    const hasCustomPadding = !paddingClass && rewardConfig?.padding !== undefined

    // Try to map maxWidth to Tailwind class if possible
    const getMaxWidthClass = () => {
        const maxWidth = rewardConfig?.maxWidth || 400
        switch (maxWidth) {
            case 256:
                return 'max-w-xs' // 20rem = 320px
            case 320:
                return 'max-w-xs'
            case 384:
                return 'max-w-sm' // 24rem = 384px
            case 400:
            case 448:
                return 'max-w-md' // 28rem = 448px
            case 512:
                return 'max-w-lg' // 32rem = 512px
            case 640:
                return 'max-w-xl' // 36rem = 576px
            case 768:
                return 'max-w-2xl' // 42rem = 672px
            default:
                return '' // Will use inline style if not a standard value
        }
    }

    const maxWidthClass = getMaxWidthClass()
    const hasCustomMaxWidth = !maxWidthClass && rewardConfig?.maxWidth !== undefined

    return (
        <div
            className={clsx('flex m-auto relative ', isHorizontal ? 'flex-row items-center justify-start' : 'flex-col items-center justify-center', shadowClass, paddingClass, maxWidthClass)}
            style={{
                ...(hasCustomPadding && { padding: `${rewardConfig?.padding}px` }),
                ...(hasCustomMaxWidth && { maxWidth: `${rewardConfig?.maxWidth}px` }),
                backgroundColor: rewardConfig?.containerBackgroundColor || 'transparent',
                borderRadius: rewardConfig?.containerPixelated ? '0' : `${rewardConfig?.containerBorderRadius || 0}px`,
                clipPath: rewardConfig?.containerPixelated ? getPixelatedClipPath(rewardConfig?.containerBorderRadius || 0) : undefined,
                ...(rewardConfig?.containerShadow && !shadowClass && { boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' }),
            }}
            data-editor-selectable-key={dataConfigKey}
        >
            {/* Reward image */}
            {showImage && (
                <div
                    className="overflow-hidden relative"
                    style={{
                        width: `${rewardConfig?.imageWidth || 150}px`,
                        height: `${rewardConfig?.imageHeight || 150}px`,
                        borderRadius: rewardConfig?.imagePixelated ? '0' : `${rewardConfig?.imageBorderRadius || 8}px`,
                        clipPath: rewardConfig?.imagePixelated ? getPixelatedClipPath(rewardConfig?.imageBorderRadius || 8) : undefined,
                        margin: isHorizontal ? `0 ${rewardConfig?.imageMargin || 16}px 0 0` : `0 0 ${rewardConfig?.imageMargin || 16}px 0`,
                        order: imageOnRight ? 2 : 0,
                    }}
                >
                    <AnimatePresence mode="popLayout">
                        {reward === null ? (
                            <motion.div
                                key="image-placeholder"
                                initial="hidden"
                                animate="visible"
                                exit="hidden"
                                variants={fadeInOut}
                                transition={{ duration: 0.3 }}
                                className="w-full h-full bg-gray-300 rounded absolute top-0 left-0  animate-pulse"
                            />
                        ) : (
                            <motion.div
                                key="image-content"
                                initial="hidden"
                                animate="visible"
                                exit="hidden"
                                variants={fadeInOut}
                                transition={{ duration: 0.3 }}
                                className="w-full h-full absolute top-0 left-0"
                            >
                                {renderImage(reward?.image)}
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>
            )}

            {/* Text content container */}
            <div
                className={clsx('flex flex-col ', imageOnRight ? 'order-1' : 'order-0')}
                style={{
                    flex: isHorizontal ? 1 : 'auto',
                    gap: `${rewardConfig?.spacing || 16}px`,
                }}
            >
                {/* Reward title */}
                {showTitle && (
                    <AnimatePresence mode="popLayout">
                        {reward === null ? (
                            <motion.div
                                key="title-placeholder"
                                initial="hidden"
                                animate="visible"
                                exit="hidden"
                                variants={fadeInOut}
                                transition={{ duration: 0.3 }}
                                className={clsx('font-bold min-h-[1.5em]', isHorizontal ? 'text-left' : 'text-center')}
                            >
                                <span
                                    className="rounded-md  animate-pulse"
                                    style={{
                                        fontSize: `${rewardConfig?.titleFontSize || 22}px`,
                                        fontFamily: rewardConfig?.titleFontFamily || 'Londrina Solid',
                                        ...(rewardConfig?.titleTextAlign && { textAlign: rewardConfig.titleTextAlign }),
                                        color: 'transparent',
                                        backgroundColor: '#e2e2e2',
                                    }}
                                >
                                    Loading...
                                </span>
                            </motion.div>
                        ) : (
                            <motion.div
                                key="title-content"
                                initial="hidden"
                                animate="visible"
                                exit="hidden"
                                variants={fadeInOut}
                                transition={{ duration: 0.3 }}
                                className={clsx('w-full font-bold min-h-[1.5em]', isHorizontal ? 'text-left' : 'text-center')}
                                style={{
                                    fontSize: `${rewardConfig?.titleFontSize || 22}px`,
                                    fontFamily: rewardConfig?.titleFontFamily || 'Londrina Solid',
                                    ...(rewardConfig?.titleTextAlign && { textAlign: rewardConfig.titleTextAlign }),
                                    color: rewardConfig?.titleColor || '#776e65',
                                }}
                            >
                                {title}
                            </motion.div>
                        )}
                    </AnimatePresence>
                )}

                {/* Reward description */}
                {showDescription && (
                    <AnimatePresence mode="popLayout">
                        {reward === null ? (
                            <motion.div
                                key="description-placeholder"
                                initial="hidden"
                                animate="visible"
                                exit="hidden"
                                variants={fadeInOut}
                                transition={{ duration: 0.3 }}
                                className={clsx('min-h-[3em]', isHorizontal ? 'text-left' : 'text-center')}
                            >
                                <span
                                    className="rounded-md animate-pulse"
                                    style={{
                                        ...(rewardConfig?.descriptionTextAlign && { textAlign: rewardConfig.descriptionTextAlign }),
                                        fontFamily: rewardConfig?.descriptionFontFamily || 'Poppins',
                                        fontSize: `${rewardConfig?.descriptionFontSize || 16}px`,
                                        color: 'transparent',
                                        backgroundColor: '#e2e2e2',
                                    }}
                                >
                                    Rolling a reward for you, this might take a while...
                                </span>
                            </motion.div>
                        ) : (
                            <motion.div
                                key="description-content"
                                initial="hidden"
                                animate="visible"
                                exit="hidden"
                                variants={fadeInOut}
                                transition={{ duration: 0.3 }}
                                className={clsx('w-full min-h-[3em]', isHorizontal ? 'text-left' : 'text-center')}
                                style={{
                                    ...(rewardConfig?.descriptionTextAlign && { textAlign: rewardConfig.descriptionTextAlign }),
                                    fontFamily: rewardConfig?.descriptionFontFamily || 'Poppins',
                                    fontSize: `${rewardConfig?.descriptionFontSize || 16}px`,
                                    color: rewardConfig?.descriptionColor || '#776e65',
                                }}
                            >
                                {description}
                            </motion.div>
                        )}
                    </AnimatePresence>
                )}
            </div>
        </div>
    )
}
