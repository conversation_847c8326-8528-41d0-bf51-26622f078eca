import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { getConfigKeyDefinition } from '@repo/shared/lib/game/gameConfig'
import { ReactGameConfig } from '../../quiz-game/src/types/config'
import { useConfigKeyDefinition } from '../hooks/useConfigType'
interface BackgroundEditorProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
}

export function BackgroundEditor({ configKey, config, onChange }: BackgroundEditorProps) {
        const definition = useConfigKeyDefinition(configKey)
    
    return (
        <div className="space-y-4">
            <div>
                <AssetPicker
                    exactSize={definition?.width && definition?.height ? { width: definition.width, height: definition.height } : undefined}
                    onSelect={(assetId) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                asset: { assetId },
                            },
                        })
                    }
                    assetUrl={config[configKey] as any}
                    extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']}
                />
            </div>

            <div>
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Background Color</Label>
                    <Switch
                        checked={(config[configKey] as any)?.useBackgroundColor !== false}
                        onCheckedChange={(useBackgroundColor) =>
                            onChange({
                                [configKey]: { ...config[configKey], useBackgroundColor },
                            })
                        }
                    />
                </div>
                <div className={`transition-opacity ${(config[configKey] as any)?.useBackgroundColor !== false ? 'opacity-100' : 'opacity-40'}`}>
                    <ColorPicker
                        color={(config[configKey] as any)?.fill || '#ffffff'}
                        onChange={(backgroundColor) =>
                            onChange({
                                [configKey]: {
                                    ...config[configKey],
                                    fill: backgroundColor,
                                },
                            })
                        }
                    />
                </div>
            </div>
        </div>
    )
}
