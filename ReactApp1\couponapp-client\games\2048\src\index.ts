import Runtime from "./Game2048Runtime";
import Editor from "./Game2048Editor";
import {
  GameModule,
  PreviewScreenDefinition,
} from "@repo/shared/lib/game/game";
import { Game2048ConfigEditor } from "./Game2048ConfigEditor";
import { PreviewScene } from "./PreviewScene";
import { Game2048Config, game2048DefaultConfig } from "./types/game2048Config";

// Preview screen definitions for the game
const previewScreens: PreviewScreenDefinition[] = [
  {
    screenId: "previewScreen_Main",
    displayName: "Game Board",
  },
  {
    screenId: "previewScreen_OutOfLives",
    displayName: "Game over (out of lives)",
    visibleCheck: (config) => config.gameEndHandler?.useLives === true,
  },
  {
    screenId: "previewScreen_TryAgain",
    displayName: "Try again",
  },
  {
    screenId: "previewScreen_Reward",
    displayName: "Reward Screen",
    visibleCheck: (config) => config.gameEndHandler?.rewardsEnabled === true,
  },
];

const Game2048: GameModule = {
  id: "2048",
  name: "2048 Puzzle",
  runtimeComponent: Runtime,
  editorComponent: Editor,
  configKeyEditor: Game2048ConfigEditor,
  defaultConfig: game2048DefaultConfig,
  previewScene: PreviewScene,
  previewScreens: previewScreens,
  configType: Game2048Config,
};

export default Game2048;

export { Runtime, Editor };
