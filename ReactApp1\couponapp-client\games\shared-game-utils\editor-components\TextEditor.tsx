import { Label } from '@repo/shared/components/ui/label'
import { Input } from '@repo/shared/components/ui/input'
import { useEffect, useState } from 'react'
import { TextStyleEditor } from './TextStyleEditor'
import { Switch } from '@repo/shared/components/ui/switch'
import { GameTextSettings, TextStyle } from '../types/uiStyles'
import { useConfigKeyDefinition } from '../hooks/useConfigType'

interface TextEditorProps {
    label?: string
    text: GameTextSettings
    onChange: (text: GameTextSettings) => void
    configKey?: string
}

export function TextEditor({ label, text, onChange, configKey }: TextEditorProps) {
    const [localText, setLocalText] = useState<GameTextSettings>(text)

    // Update local state when props change
    useEffect(() => {
        setLocalText(text)
    }, [text])

     const definition = useConfigKeyDefinition(configKey)
    const hasVisibilityToggle = definition?.editorSettings?.toggleableVisibility === true

    const updateStyle = (updatedStyle: TextStyle) => {
        const newText = {
            ...localText,
            style: updatedStyle,
        }
        setLocalText(newText)
        onChange(newText)
    }

    const updateText = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newText = {
            ...localText,
            text: e.target.value,
        }
        setLocalText(newText)
        onChange(newText)
    }

    const toggleVisibility = (visible: boolean) => {
        const newText = {
            ...localText,
            style: {
                ...localText.style,
                isVisible: visible,
            },
        }
        setLocalText(newText)
        onChange(newText)
    }

    return (
        <div className="space-y-4" key={configKey}>
            <div className="flex items-center justify-between">
                <Label>{label}</Label>
                {hasVisibilityToggle && (
                    <div className="flex items-center gap-2">
                        <Label htmlFor={`visible-toggle-${configKey}`} className="text-sm">
                            Visible
                        </Label>
                        <Switch id={`visible-toggle-${configKey}`} checked={localText?.style?.isVisible !== false} onCheckedChange={toggleVisibility} />
                    </div>
                )}
            </div>
            <div className="space-y-4">
                <div>
                    <Label>Text Content</Label>
                    <Input type="text" value={localText?.text || ''} onChange={updateText} placeholder="Enter text..." />
                </div>
                <TextStyleEditor style={localText?.style} onChange={updateStyle} configKey={configKey} />
            </div>
        </div>
    )
}
