import React from 'react'
import { ConfigKeyEditor } from '../ConfigKeyEditor'
import { Card, CardContent, CardHeader, CardTitle } from '@repo/shared/components/ui/card'

interface GameTextsEditorProps {
    config: any
    onChange: (config: any) => void
}

export const GameTextsEditor: React.FC<GameTextsEditorProps> = ({ config, onChange }) => {
    const textConfigs = [
        { key: 'gameOverText', title: 'Game Over Text' },
        { key: 'tryAgainText', title: 'Try Again Text' },
        { key: 'newGameText', title: 'New Game Text' },
        { key: 'gameEndFinalText', title: 'Game End Final Text' },
        { key: 'loseLifeTitle', title: 'Lose Life Title' },
        { key: 'outOfLivesTitle', title: 'Out of Lives Title' },
        { key: 'gameOverTitle', title: 'Game Over Title' },
        { key: 'gameOverMessage', title: 'Game Over Message' },
        { key: 'rewardTitle', title: 'Reward Title' },
        { key: 'continueButtonText', title: 'Continue Button' },
        { key: 'outOfLivesContinueButton', title: 'Out of Lives Continue Button' },
        { key: 'gameOverContinueButton', title: 'Game Over Continue Button' },
        { key: 'rewardClaimButton', title: 'Reward Claim Button' },
    ]

    return (
        <div className="space-y-4">
            {textConfigs.map(({ key, title }) => (
                <Card key={key}>
                    <CardHeader>
                        <CardTitle className="text-lg">{title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <ConfigKeyEditor
                            configKey={key}
                            config={config}
                            updateConfig={onChange}
                        />
                    </CardContent>
                </Card>
            ))}
        </div>
    )
} 