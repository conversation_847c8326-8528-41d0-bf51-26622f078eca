import { Label } from '@repo/shared/components/ui/label'
import { FontPicker } from '@repo/shared/components/editor/fontPicker'
import { useEffect, useState } from 'react'
import CustomColorPicker from '@repo/shared/components/ui/color-picker'
import { Slider } from '@repo/shared/components/ui/slider'
import { Separator } from '@repo/shared/components/ui/separator'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@repo/shared/components/ui/accordion'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { TextStyle } from '../types/uiStyles'

interface TextStyleEditorProps {
    style: TextStyle
    onChange: (style: TextStyle) => void
    configKey?: string
}

export function TextStyleEditor({ style, onChange, configKey }: TextStyleEditorProps) {
    const [localStyle, setLocalStyle] = useState<TextStyle>(style || {})

    // Update local state when props change
    useEffect(() => {
        setLocalStyle(style || {})
    }, [style])

    const updateStyle = (updater: (current: TextStyle) => TextStyle) => {
        const newStyle = updater(localStyle)
        setLocalStyle(newStyle)
        onChange(newStyle)
    }

    const updateFont = (font: string) => {
        updateStyle((current) => ({
            ...current,
            fontFamily_tFontFamily: font,
        }))
    }

    const updateFontSize = (value: number[]) => {
        updateStyle((current) => ({
            ...current,
            fontSize: value[0],
        }))
    }

    const updateFill = (color: string) => {
        updateStyle((current) => ({
            ...current,
            fill: color,
        }))
    }

    const updateShadowColor = (color: string) => {
        updateStyle((current) => ({
            ...current,
            shadow: {
                ...(current.shadow || {}),
                color,
            },
        }))
    }

    const updateShadowOffsetX = (value: number[]) => {
        updateStyle((current) => ({
            ...current,
            shadow: {
                ...(current.shadow || {}),
                offsetX: value[0],
            },
        }))
    }

    const updateShadowOffsetY = (value: number[]) => {
        updateStyle((current) => ({
            ...current,
            shadow: {
                ...(current.shadow || {}),
                offsetY: value[0],
            },
        }))
    }

    const updateShadowBlur = (value: number[]) => {
        updateStyle((current) => ({
            ...current,
            shadow: {
                ...(current.shadow || {}),
                blur: value[0],
            },
        }))
    }

    const updateTextAlign = (value: string) => {
        updateStyle((current) => ({
            ...current,
            textAlign: value as TextStyle['textAlign'],
        }))
    }

    const updateFontWeight = (weight: string) => {
        updateStyle((current) => ({
            ...current,
            fontWeight: weight as TextStyle['fontWeight'],
        }))
    }

    const updateOffset = (axis: 'offsetX' | 'offsetY', value: number[]) => {
        updateStyle((current) => ({
            ...current,
            [axis]: value[0],
        }))
    }

    return (
        <div className="space-y-4" key={configKey}>
            <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="font">
                    <AccordionTrigger>Font Settings</AccordionTrigger>
                    <AccordionContent>
                        <div className="space-y-4 p-2">
                            <div>
                                <Label>Font Family</Label>
                                <FontPicker font={localStyle?.fontFamily_tFontFamily || ''} onChange={updateFont} />
                            </div>
                            <div>
                                <Label>Font Size</Label>
                                <Slider value={[localStyle?.fontSize || 16]} min={8} max={72} step={1} onValueChange={updateFontSize} />
                                <div className="mt-1 text-xs text-gray-500">{localStyle?.fontSize || 16}px</div>
                            </div>
                            <div>
                                <Label>Font Weight</Label>
                                <Select value={localStyle?.fontWeight || 'bold'} onValueChange={updateFontWeight}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select font weight" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="normal">Normal</SelectItem>
                                        <SelectItem value="bold">Bold</SelectItem>
                                        <SelectItem value="100">Thin (100)</SelectItem>
                                        <SelectItem value="200">Extra Light (200)</SelectItem>
                                        <SelectItem value="300">Light (300)</SelectItem>
                                        <SelectItem value="400">Regular (400)</SelectItem>
                                        <SelectItem value="500">Medium (500)</SelectItem>
                                        <SelectItem value="600">Semi Bold (600)</SelectItem>
                                        <SelectItem value="700">Bold (700)</SelectItem>
                                        <SelectItem value="800">Extra Bold (800)</SelectItem>
                                        <SelectItem value="900">Black (900)</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label>Text Color</Label>
                                <CustomColorPicker color={localStyle?.fill || '#000000'} onChange={updateFill} />
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="shadow">
                    <AccordionTrigger>Shadow</AccordionTrigger>
                    <AccordionContent>
                        <div className="space-y-4 p-2">
                            <div>
                                <Label>Shadow Color</Label>
                                <CustomColorPicker color={localStyle?.shadow?.color || '#000000'} onChange={updateShadowColor} />
                            </div>
                            <div>
                                <Label>Offset X</Label>
                                <Slider value={[localStyle?.shadow?.offsetX || 0]} min={-10} max={10} step={1} onValueChange={updateShadowOffsetX} />
                                <div className="mt-1 text-xs text-gray-500">{localStyle?.shadow?.offsetX || 0}px</div>
                            </div>
                            <div>
                                <Label>Offset Y</Label>
                                <Slider value={[localStyle?.shadow?.offsetY || 0]} min={-10} max={10} step={1} onValueChange={updateShadowOffsetY} />
                                <div className="mt-1 text-xs text-gray-500">{localStyle?.shadow?.offsetY || 0}px</div>
                            </div>
                            <div>
                                <Label>Blur</Label>
                                <Slider value={[localStyle?.shadow?.blur || 0]} min={0} max={10} step={1} onValueChange={updateShadowBlur} />
                                <div className="mt-1 text-xs text-gray-500">{localStyle?.shadow?.blur || 0}px</div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                <AccordionItem value="alignment">
                    <AccordionTrigger>Text Alignment & Offset</AccordionTrigger>
                    <AccordionContent>
                        <div className="space-y-4 p-2">
                            <div>
                                <Label>Text Alignment</Label>
                                <Select value={localStyle?.textAlign || 'center'} onValueChange={updateTextAlign}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select alignment" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="left">Left</SelectItem>
                                        <SelectItem value="center">Center</SelectItem>
                                        <SelectItem value="right">Right</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <Separator />

                            <div className="space-y-4">
                                <div>
                                    <Label>Offset X</Label>
                                    <Slider value={[localStyle?.offsetX || 0]} min={-50} max={50} step={1} onValueChange={(value) => updateOffset('offsetX', value)} />
                                    <div className="mt-1 text-xs text-gray-500">{localStyle?.offsetX || 0}px</div>
                                </div>
                                <div>
                                    <Label>Offset Y</Label>
                                    <Slider value={[localStyle?.offsetY || 0]} min={-50} max={50} step={1} onValueChange={(value) => updateOffset('offsetY', value)} />
                                    <div className="mt-1 text-xs text-gray-500">{localStyle?.offsetY || 0}px</div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>
            </Accordion>
        </div>
    )
}
