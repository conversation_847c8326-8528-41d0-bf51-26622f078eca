import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { Slider } from '@repo/shared/components/ui/slider'
import { Input } from '@repo/shared/components/ui/input'
import { useConfigKeyDefinition } from '../hooks/useConfigType'
import { GameSoundSwitchStyle } from '../types/uiStyles'

interface SoundSwitchEditorProps {
    configKey: string
    config: GameSoundSwitchStyle
    onChange: (changes: any) => void
}

export function SoundSwitchEditor({ configKey, config, onChange }: SoundSwitchEditorProps) {
    // Get the definition to check if visibility is toggleable
        const definition = useConfigKeyDefinition(configKey)
    const isVisibilityToggleable = definition?.editorSettings?.toggleableVisibility

    return (
        <div className="space-y-4">
            {isVisibilityToggleable && (
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Sound Switch Visibility</Label>
                    <Switch checked={config?.isVisible !== false} onCheckedChange={(isVisible) => onChange({ isVisible })} />
                </div>
            )}

            <div className="space-y-4">
                <div>
                    <Label className="mb-2 block">Sound On Image</Label>
                    <AssetPicker onSelect={(assetId) => onChange({ onAsset: { assetId } })} assetUrl={config?.onAsset} extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']} />

                    {config?.onAsset && (
                        <div className="mt-2">
                            <div className="flex items-center space-x-2 mb-2">
                                <Label className="flex-grow">Pixel Art Mode</Label>
                                <Switch
                                    checked={config?.backgroundScaleMode === 'pixelated-cover'}
                                    onCheckedChange={(isPixelArt) =>
                                        onChange({
                                            backgroundScaleMode: isPixelArt ? 'pixelated-cover' : 'cover',
                                        })
                                    }
                                />
                            </div>
                        </div>
                    )}
                </div>

                <div>
                    <Label className="mb-2 block">Sound Off Image</Label>
                    <AssetPicker onSelect={(assetId) => onChange({ offAsset: { assetId } })} assetUrl={config?.offAsset} extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']} />
                </div>
            </div>

            <div>
                <Label className="mb-2 block">Alignment (Legacy)</Label>
                <Select value={config?.alignment || 'right'} onValueChange={(alignment) => onChange({ alignment })}>
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select alignment" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            <div>
                <Label className="mb-2 block">Position</Label>
                <Select value={config?.position || config?.alignment || 'right'} onValueChange={(position) => onChange({ position })}>
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select position" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            <div>
                <Label className="mb-2 block">Offset X</Label>
                <div className="flex justify-between items-center mb-2">
                    <span className="text-sm">{config?.offsetX || 0}px</span>
                </div>
                <Slider
                    value={[config?.offsetX || 0]}
                    min={-250}
                    max={250}
                    step={1}
                    onValueChange={(values) =>
                        onChange({
                            offsetX: values[0],
                        })
                    }
                />
            </div>
            <div>
                <Label className="mb-2 block">Offset Y</Label>
                <div className="flex justify-between items-center mb-2">
                    <span className="text-sm">{config?.offsetY || 0}px</span>
                </div>
                <Slider
                    value={[config?.offsetY || 0]}
                    min={-250}
                    max={250}
                    step={1}
                    onValueChange={(values) =>
                        onChange({
                            offsetY: values[0],
                        })
                    }
                />
            </div>

            <div>
                <Label className="mb-2 block">Size</Label>
                <div className="grid grid-cols-2 gap-2">
                    <div>
                        <Label className="text-xs">Width</Label>
                        <Input type="number" value={config?.width || 48} onChange={(e) => onChange({ width: parseInt(e.target.value, 10) })} />
                    </div>

                    <div>
                        <Label className="text-xs">Height</Label>
                        <Input type="number" value={config?.height || 48} onChange={(e) => onChange({ height: parseInt(e.target.value, 10) })} />
                    </div>
                </div>
            </div>
        </div>
    )
}
