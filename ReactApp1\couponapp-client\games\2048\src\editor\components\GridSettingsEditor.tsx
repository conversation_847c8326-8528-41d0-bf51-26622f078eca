import React from 'react'
import { ConfigKeyEditor } from '../ConfigKeyEditor'
import { Card, CardContent, CardHeader, CardTitle } from '@repo/shared/components/ui/card'

interface GridSettingsEditorProps {
    config: any
    onChange: (config: any) => void
}

export const GridSettingsEditor: React.FC<GridSettingsEditorProps> = ({ config, onChange }) => {
    const gridConfigs = [
        { key: 'gridBackground', title: 'Grid Background' },
        { key: 'tileSpacing', title: 'Tile Spacing' },
        { key: 'gridMargin', title: 'Grid Margin' },
    ]

    return (
        <div className="space-y-4">
            {gridConfigs.map(({ key, title }) => (
                <Card key={key}>
                    <CardHeader>
                        <CardTitle className="text-lg">{title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <ConfigKeyEditor
                            configKey={key}
                            config={config}
                            updateConfig={onChange}
                        />
                    </CardContent>
                </Card>
            ))}
        </div>
    )
} 