import { AnimatePresence } from 'framer-motion'
import { RewardScreen, OutOfLivesScreen } from './Scenes'
import { GameRuntimeComponentProps } from '@repo/shared/lib/game/game'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { gameEvents } from '@repo/shared/lib/campaign/eventEmitter'
import { useGameAtom } from '@repo/shared/lib/atoms/atom-extensions'
import { BackgroundStyle, ReactGameConfig } from '../types/config'
import { GameScreenId } from '../types/screen'
import { motion } from 'framer-motion'
import { useGameRewards } from '@repo/shared/lib/game/useGameRewards'

// Import shared game utilities
import { GameContext, GameRuntimeProps, useGame } from '@repo/shared-game-utils/hooks/useGame'
import { getBackgroundStyle } from '@repo/shared-game-utils/utils/gameStyleUtils'
import { CounterElement } from '@repo/shared-game-utils/components/CounterElement'
import { GameSoundSwitch } from '@repo/shared-game-utils/components/GameSoundSwitch'
import { GameButton } from '@repo/shared-game-utils/components/GameButton'
import { usePreloadImage } from '@repo/shared-game-utils/hooks/usePreloadImage'
import { useMusic, useSoundEffect } from '@repo/shared-game-utils/hooks/useSounds'
import { makeElementsInteractive } from '@repo/shared-game-utils/utils/previewInteractive'

export default function MainGame(props: GameRuntimeProps) {
    const [currentScreenId, setCurrentScreenId] = useState<GameScreenId>(props.currentScreenId || 'main')
    const initialGameScreenChecked = useRef(false)

    // Create an enhanced context with both props and screen state
    const contextValue: GameRuntimeProps = {
        ...props,
        currentScreenId,
        setCurrentScreenId,
        initialGameScreenChecked,
    }

    useEffect(() => {
        if (!props.isPreview) return
        setCurrentScreenId(props.currentScreenId || 'main')
    }, [props.currentScreenId, props.isPreview])

    return (
        <GameContext.Provider value={contextValue}>
            <GameContent />
        </GameContext.Provider>
    )
}

const useGameState = () => {
    const { config, widgetId } = useGame<ReactGameConfig>()
    const initialLivesCount = config.gameEndHandler?.useLives ? config.gameEndHandler?.livesCount || 3 : Number.MAX_SAFE_INTEGER

    const [lives, setLivesCount] = useGameAtom(widgetId, 'livesCount', initialLivesCount)
    const [spinAttempts, setSpinAttempts] = useGameAtom(widgetId, 'spinAttempts', 0)

    return {
        lives,
        setLivesCount,
        spinAttempts,
        setSpinAttempts,
    }
}

function GameContent() {
    const { config, widgetId, resolveAssetUrl, isPreview, currentScreenId, setCurrentScreenId, onGameAssetSelect } = useGame<ReactGameConfig>()

    // Create a ref for the container
    const containerRef = useRef<HTMLDivElement>(null)

    // // Function to handle try again button click
    const handleTryAgainClick = useCallback(() => {
        if (isPreview) return

        setCurrentScreenId('main')
    }, [isPreview, setCurrentScreenId])

    // Function to handle reward button click
    const handleRewardButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinishedWithReward', {
            score: 1,
            widgetId: widgetId,
        })
    }, [isPreview, widgetId])

    // // Function to handle out of lives button click
    const handleOutOfLivesButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinishedNoReward', {
            score: 1,
            widgetId: widgetId,
        })
    }, [isPreview, widgetId, setCurrentScreenId])

    useEffect(() => {
        if (!isPreview) return
        makeElementsInteractive(onGameAssetSelect)
    }, [currentScreenId, isPreview, config, onGameAssetSelect])

    return (
        <div
            data-game-widget-id={widgetId}
            ref={containerRef}
            className="flex flex-col items-center justify-center h-full w-full touch-none select-none overscroll-none "
            style={getBackgroundStyle(config.mainBackground, resolveAssetUrl)}
        >
            <div className="relative w-full h-full text-black">
                {currentScreenId === 'main' && <MainGameScreen />}
                {currentScreenId === 'claim-reward' && <RewardScreen onButtonClick={handleRewardButtonClick} />}
                {currentScreenId === 'out-of-lives' && <OutOfLivesScreen onButtonClick={handleOutOfLivesButtonClick} />}
            </div>
        </div>
    )
}

const RotatingReelPreview = ({ symbolConfigKeys, reelIndex }: { symbolConfigKeys: string[]; reelIndex: number }) => {
    const { config, resolveAssetUrl } = useGame<ReactGameConfig>()

    const symbolsContainerHeight = config.slotsBackground.height
    const symbolHeight = config.slotsBackground.height / 3
    const reelSymbols = symbolConfigKeys

    return (
        <div
            key={reelIndex}
            className="relative w-1/3 overflow-hidden child-with-spacing flex-shrink-0"
            style={{
                height: symbolsContainerHeight,
                WebkitMaskImage: 'linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%)',
                maskImage: 'linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%)',
            }}
        >
            <div className="absolute w-full">
                {reelSymbols.map((symbolConfigKey, index) => (
                    <div key={index} data-editor-selectable-key={symbolConfigKey} className="w-100 flex items-center  justify-center" style={{ height: symbolHeight }}>
                        <img src={resolveAssetUrl(config[symbolConfigKey])} alt={symbolConfigKey} className="w-20 h-20 object-contain " />
                    </div>
                ))}
            </div>
        </div>
    )
}

// Add this new component for the rotating reel
const RotatingReel = ({
    symbolConfigKeys: symbols,
    isSpinning,
    delay,
    finalSymbolIndex,
    isPreview,
}: {
    symbolConfigKeys: string[]
    isSpinning: boolean
    delay: number
    finalSymbolIndex: number
    isPreview?: boolean
}) => {
    const { config, resolveAssetUrl } = useGame<ReactGameConfig>()

    const symbolsContainerHeight = config.slotsBackground.height

    const symbolHeight = config.slotsBackground.height / 3
    const reelSymbols = symbols
    const finalPosition = -(symbolHeight * (reelLength - 2)) + symbolHeight // Center index 25 in the visible area
    const initialPosition = 0

    return (
        <div
            className="relative w-1/3 overflow-hidden child-with-spacing flex-shrink-0"
            style={{
                height: symbolsContainerHeight,
                WebkitMaskImage: 'linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%)',
                maskImage: 'linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%)',
            }}
        >
            <motion.div
                className="absolute w-full"
                initial={{ y: isSpinning ? initialPosition : finalPosition }}
                animate={{
                    y: isSpinning ? [initialPosition, finalPosition] : finalPosition,
                }}
                transition={{
                    duration: isSpinning ? 3 : 0,
                    delay,
                    ease: [0.2, 0.65, 0.3, 0.9],
                    times: isSpinning ? [0, 1] : [0, 0],
                }}
            >
                {reelSymbols.map((symbolConfigKey, index) => (
                    <div key={index} data-editor-selectable-key={symbolConfigKey} className="w-100 flex items-center justify-center" style={{ height: symbolHeight }}>
                        <img src={resolveAssetUrl(config[symbolConfigKey])} alt={symbolConfigKey} className="w-20 h-20 object-contain" />
                    </div>
                ))}
            </motion.div>
        </div>
    )
}
const reelLength = 35


function usePrepareAssets() {
    const { config } = useGame<ReactGameConfig>()

    // Preload sounds
    useMusic(config.gameOverSound, false)
    useMusic(config.winSound, false)
    useMusic(config.spinSound, false)

    usePreloadImage(config.gameOverOverlay?.asset)
    usePreloadImage(config.rewardOverlay?.asset)
    usePreloadImage(config.outOfLivesOverlay?.asset)
    usePreloadImage(config.gameSoundSwitch?.onAsset)
    usePreloadImage(config.gameSoundSwitch?.offAsset)

    console.log(config)
}

function MainGameScreen() {
    const { initialGameScreenChecked, config, widgetId, resolveAssetUrl, setCurrentScreenId, currentScreenId, isPreview } = useGame<ReactGameConfig>()
    const { lives, setLivesCount, spinAttempts, setSpinAttempts } = useGameState()
    const { reward, pickReward, hasWonReward } = useGameRewards({ gameWidgetId: widgetId, attempt: spinAttempts })

    useMusic(config.backgroundMusic, true, true)

    usePrepareAssets()

    const rewardsEnabled = config.gameRewardsHandler?.rewardsEnabled || false
    const useLives = config.gameEndHandler?.useLives

    // Slots game state
    const [isSpinning, setIsSpinning] = useState(false)
    const [slotSymbols] = useState(() => {
        // Use the configured slot symbol images
        return ['slotSymbol1', 'slotSymbol2', 'slotSymbol3', 'slotSymbol4', 'slotSymbol5', 'slotSymbol6', 'slotSymbol7', 'slotSymbol8', 'slotSymbol9']
    })

    // Sound effects
    const spinSoundEffect = useSoundEffect(config.spinSound)

    useEffect(() => {
        console.log('reward changed', reward)
    }, [reward])

    // For preview mode, create a predefined set of symbols showing 1-9 in order
    const previewReels = useMemo(() => {
        if (!isPreview) return null

        return [
            // First reel shows 1, 4, 7
            ['slotSymbol1', 'slotSymbol4', 'slotSymbol7'],
            // Second reel shows 2, 5, 8
            ['slotSymbol2', 'slotSymbol5', 'slotSymbol8'],
            // Third reel shows 3, 6, 9
            ['slotSymbol3', 'slotSymbol6', 'slotSymbol9'],
        ]
    }, [isPreview])

    const [reels, setReels] = useState(() => {
        // For actual gameplay, create random reels
        return Array(3)
            .fill(0)
            .map(() =>
                Array(reelLength)
                    .fill(0)
                    .map(() => slotSymbols[Math.floor(Math.random() * slotSymbols.length)])
            )
    })

    // Final symbol indices - all set to 25 as requested
    const [finalIndices] = useState([reelLength - 2, reelLength - 2, reelLength - 2])

    // Handle spin button click
    const handleSpin = useCallback(async () => {
        if (isSpinning) return


        // Decrease lives count when spinning
        if (useLives) {
            setLivesCount(lives - 1)
        }

        setSpinAttempts(old => (old ?? 0) + 1)

        

        const pickResult = await pickReward()
        const isWinning = pickResult.hasWon

        spinSoundEffect?.play()

        // Copy visible symbols (24, 25, 26) to positions (0, 1, 2) for a seamless loop
        setReels((prevReels) => {
            // Create a new reels array
            const newReels = prevReels.map((reel) => {
                const newReel = [...reel]
                // Copy the currently visible symbols to the start positions
                newReel[0] = newReel[reelLength - 3]
                newReel[1] = newReel[reelLength - 2]
                newReel[2] = newReel[reelLength - 1]
                return newReel
            })

            // If this is a winning spin, set all reels to have the same symbol at the winning index
            if (isWinning) {
                // Select a random symbol to be the winning one
                const winningSymbol = slotSymbols[Math.floor(Math.random() * slotSymbols.length)]

                // Set this symbol at the winning position (reelLength - 2) in all reels
                newReels.forEach((reel) => {
                    reel[reelLength - 2] = winningSymbol
                })
            } else {
                // For a non-winning pattern, ensure all three reels have different symbols
                const usedSymbols = new Set<string>()

                // For each reel, assign a random symbol that hasn't been used yet
                newReels.forEach((reel) => {
                    let randomSymbol: string
                    do {
                        randomSymbol = slotSymbols[Math.floor(Math.random() * slotSymbols.length)]
                    } while (usedSymbols.has(randomSymbol) && usedSymbols.size < slotSymbols.length)

                    usedSymbols.add(randomSymbol)
                    reel[reelLength - 2] = randomSymbol
                })

                // Final check - if we somehow ended up with all the same symbols (could happen if slotSymbols array is too small)
                // Just change one of them to ensure no win
                if (newReels[0][reelLength - 2] === newReels[1][reelLength - 2] && newReels[1][reelLength - 2] === newReels[2][reelLength - 2] && slotSymbols.length > 1) {
                    let differentSymbol: string
                    do {
                        differentSymbol = slotSymbols[Math.floor(Math.random() * slotSymbols.length)]
                    } while (differentSymbol === newReels[0][reelLength - 2])

                    newReels[2][reelLength - 2] = differentSymbol
                }
            }

            return newReels
        })

        setIsSpinning(true)

        // After animation completes, check for win and update score
        setTimeout(() => {
            setIsSpinning(false)

            if (rewardsEnabled && isWinning) {
                setCurrentScreenId('claim-reward')
                setSpinAttempts(0)
                return
            }

            // Check if player is out of lives
            if (useLives && lives <= 1) {
                setCurrentScreenId('out-of-lives')
            }
        }, 3700) // Give enough time for all reels to stop
        
    }, [isSpinning, reels, finalIndices, lives, setLivesCount, useLives, pickReward, slotSymbols, spinAttempts, setSpinAttempts, rewardsEnabled, spinSoundEffect])

    useEffect(() => {
        if (isPreview) return
        if (initialGameScreenChecked.current) return

        console.log('checking initial game screen')
        initialGameScreenChecked.current = true

        // If rewards are enabled and player has won a reward, show reward screen
        if (rewardsEnabled && hasWonReward) {
            setCurrentScreenId('claim-reward')
            return
        }

        // If using lives and lives count is 0 or less, show out of lives screen
        if (useLives && lives <= 0) {
            setCurrentScreenId('out-of-lives')
            return
        }
    }, [lives, hasWonReward, rewardsEnabled, useLives, setCurrentScreenId, isPreview])

    return (
        <div className="game-content-area w-full h-full flex flex-col items-center justify-center">
            {/* Slots game box */}
            <div
                className="slots-game-container relative mx-auto rounded-lg flex flex-col"
                style={{
                    ...getSlotsBackgroundStyle(config.slotsBackground, resolveAssetUrl),
                    padding: `${config.slotsBackground.gridSettings?.containerPadding}px`,
                }}
                data-editor-selectable-key="slotsBackground"
            >
                {/* Sound Switch with alignment based on its configuration */}
                <div
                    className={`absolute w-full flex flex-grow ${config.gameSoundSwitch?.alignment === 'center' ? 'justify-center' : config.gameSoundSwitch?.alignment === 'right' ? 'justify-end' : 'justify-start'}`}
                    style={{
                        marginTop: config.gameSoundSwitch?.offsetY ?? 0,
                        marginLeft: config.gameSoundSwitch?.alignment !== 'right' ? (config.gameSoundSwitch?.offsetX ?? 0) : 0,
                        marginRight: config.gameSoundSwitch?.alignment === 'right' ? (config.gameSoundSwitch?.offsetX ?? 0) : 0,
                    }}
                >
                    <GameSoundSwitch config={config.gameSoundSwitch} dataConfigKey="gameSoundSwitch" />
                </div>

                <div
                    className={`flex items-center justify-center flex-grow${isPreview ? '' : ' pointer-events-none'}`}
                    style={{ '--space-x': `${config.slotsBackground.gridSettings?.tileSpacing}px` } as React.CSSProperties}
                >
                    {isPreview
                        ? previewReels.map((reelSymbols, reelIndex) => <RotatingReelPreview key={reelIndex} symbolConfigKeys={reelSymbols} reelIndex={reelIndex} />)
                        : // Normal animated reels for gameplay
                          reels.map((reelSymbols, reelIndex) => (
                              <RotatingReel
                                  key={reelIndex}
                                  symbolConfigKeys={reelSymbols}
                                  isSpinning={isSpinning}
                                  delay={reelIndex * 0.2} // Stagger the spinning
                                  finalSymbolIndex={finalIndices[reelIndex]}
                              />
                          ))}
                </div>

                <div className="relative w-full h-full">
                    {/* CounterElement for lives with alignment based on its configuration */}
                    {useLives && (
                          <CounterElement configKey="livesStyle" style={config.livesStyle}>
                          {lives}
                      </CounterElement>
                    )}

                   
<div
                        className="absolute"
                        style={{
                            marginTop: config.spinButton?.offsetY ?? 0,
                            ...(config.spinButton.alignment === 'right' ? { marginRight: -config.spinButton?.offsetX } : { marginLeft: config.spinButton?.offsetX }),
                            left: config.spinButton?.alignment === 'center' ? '50%' : config.spinButton?.alignment === 'right' ? 'auto' : 0,
                            right: config.spinButton?.alignment === 'right' ? 0 : 'auto',
                            transform: config.spinButton?.alignment === 'center' ? 'translateX(-50%)' : 'none',
                        }}
                    >
                        <GameButton config={config.spinButton} dataConfigKey="spinButton" onClick={isSpinning ? undefined : handleSpin} />
                    </div>
                </div>
            </div>
        </div>
    )
}


const getSlotsBackgroundStyle = (background?: BackgroundStyle, resolveAssetUrl?: (id: AssetUrl) => string): React.CSSProperties => {
    if (!background) return {}

    return {
        backgroundColor: background.useBackgroundColor !== false ? (background.fill ?? 'transparent') : 'transparent',
        backgroundImage: background.asset && resolveAssetUrl ? `url(${resolveAssetUrl(background.asset)})` : undefined,
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'contain',
        width: background.width ? `${background.width}px` : undefined,
        height: background.height ? `${background.height}px` : undefined,
    }
}