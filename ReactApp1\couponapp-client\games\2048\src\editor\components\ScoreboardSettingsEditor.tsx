import React from 'react'
import { ConfigKeyEditor } from '../ConfigKeyEditor'
import { Card, CardContent, CardHeader, CardTitle } from '@repo/shared/components/ui/card'

interface ScoreboardSettingsEditorProps {
    config: any
    onChange: (config: any) => void
}

export const ScoreboardSettingsEditor: React.FC<ScoreboardSettingsEditorProps> = ({ config, onChange }) => {
    const scoreboardConfigs = [
        { key: 'scoreStyle', title: 'Score Style' },
        { key: 'bestScoreStyle', title: 'Best Score Style' },
        { key: 'livesStyle', title: 'Lives Style' },
    ]

    return (
        <div className="space-y-4">
            {scoreboardConfigs.map(({ key, title }) => (
                <Card key={key}>
                    <CardHeader>
                        <CardTitle className="text-lg">{title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <ConfigKeyEditor
                            configKey={key}
                            config={config}
                            updateConfig={onChange}
                        />
                    </CardContent>
                </Card>
            ))}
        </div>
    )
} 