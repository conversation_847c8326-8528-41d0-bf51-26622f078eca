/**
 * Traverses an object recursively and collects values that match the specified criteria
 *
 * @param obj The object to traverse
 * @param predicate A function that returns true for values that should be collected
 * @param keyPath Optional path prefix for nested objects (used internally)
 * @returns An array of matches with the value and its path
 */
export function traverseObject<T>(obj: any, predicate: (key: string, value: any, path: string) => boolean, keyPath: string = ''): Array<{ value: T; path: string }> {
    if (!obj || typeof obj !== 'object') {
        return []
    }

    let results: Array<{ value: T; path: string }> = []

    Object.entries(obj).forEach(([key, value]) => {
        const currentPath = keyPath ? `${keyPath}.${key}` : key

        // Check if the current property matches our criteria
        if (predicate(key, value, currentPath)) {
            results.push({ value: value as T, path: currentPath })
        }

        // Recursively traverse objects and arrays
        if (value && typeof value === 'object') {
            // For arrays, traverse each item
            if (Array.isArray(value)) {
                value.forEach((item, index) => {
                    if (item && typeof item === 'object') {
                        const arrayItemPath = `${currentPath}[${index}]`
                        results = results.concat(traverseObject<T>(item, predicate, arrayItemPath))
                    }
                })
            } else {
                // For objects, traverse recursively
                results = results.concat(traverseObject<T>(value, predicate, currentPath))
            }
        }
    })

    return results
}

/**
 * Utility to find all icon names in a configuration object
 *
 * @param config The configuration object to search
 * @param iconNameKey The property name to search for (default: "iconName_tIcon")
 * @returns An array of icon names with their paths
 */
export function findAllIconNames(config: any): string[] {
    return traverseObject<string>(config, (key, value) => {
        return key.includes('_tIcon') && typeof value === 'string'
    })?.map((icon) => {
        return icon.value
    })
}
