import { ConfigKeyEditorComponentProps } from '@repo/shared/lib/game/game'
import { ReactGameConfig, defaultGameConfig } from '../types/config'

import { Button } from '@repo/shared/components/ui/button'
import React, { ReactNode } from 'react'
import { RefreshCw } from 'lucide-react'
import { AudioEditor } from '@repo/shared-game-utils/editor-components/AudioEditor'
import { BackgroundEditor } from '@repo/shared-game-utils/editor-components/BackgroundEditor'
import { ContainerEditor } from '@repo/shared-game-utils/editor-components/ContainerEditor'
import { GameButtonEditor } from '@repo/shared-game-utils/editor-components/GameButtonEditor'
import { ImageEditor } from '@repo/shared-game-utils/editor-components/ImageEditor'
import { RewardComponentStyleEditor } from '@repo/shared-game-utils/editor-components/RewardComponentStyleEditor'
import { ScoreboardNumbersStyleEditor } from '@repo/shared-game-utils/editor-components/ScoreboardNumbersStyleEditor'
import { SoundSwitchEditor } from '@repo/shared-game-utils/editor-components/SoundSwitchEditor'
import { TextEditor } from '@repo/shared-game-utils/editor-components/TextEditor'
import { useConfigKeyDefinition } from '@repo/shared-game-utils/hooks/useConfigType'
import { ScratchOverlayEditor } from './components/ScratchOverlayEditor'
import { CounterElementStyleEditor } from '@repo/shared-game-utils/editor-components/CounterElementStyleEditor'

export const editorComponentMapping: ComponentMapping = {
    'image': ImageEditor,
    'audio': AudioEditor,
    'background': BackgroundEditor,
    'container': ContainerEditor,
    'scoreboard-numbers-style': ScoreboardNumbersStyleEditor,
    'text': TextEditor,
    'game-button': GameButtonEditor,
    'sound-switch': SoundSwitchEditor,
    'reward-component-style': RewardComponentStyleEditor,
    'scratch-overlay': ScratchOverlayEditor,
    'counter': CounterElementStyleEditor,
}

export function ConfigKeyEditor({ configKey, config: gameConfig, updateConfig }: ConfigKeyEditorComponentProps) {
    const config = gameConfig ?? {}

    const handleConfigChange = (changes: Partial<ReactGameConfig>) => {
        updateConfig({ ...config, ...changes })
    }

    const handleResetToDefault = () => {
        // Use defaultGameConfig which contains the actual default values
        const defaultValue = defaultGameConfig[configKey]
        if (defaultValue !== undefined) {
            handleConfigChange({ [configKey]: defaultValue })
        }
    }

    const definition = useConfigKeyDefinition(configKey)

    if (!definition) {
        return <div>No definition found for: {configKey?.toString()}</div>
    }

    const { configEditorType: type } = definition

    // If the type is not supported, show an error message
    if (!type) {
        return <div>No editor type defined for: {configKey?.toString()}</div>
    }

    return (
        <EditorComponentWrapper onReset={handleResetToDefault}>
            {renderEditorComponent(type, configKey as string, config, handleConfigChange)}
        </EditorComponentWrapper>
    )
}

interface EditorComponentWrapperProps {
    children: ReactNode
    onReset: () => void
    showResetButton?: boolean
}

export function EditorComponentWrapper({
    children,
    onReset,
    showResetButton = true
}: EditorComponentWrapperProps) {
    return (
        <div className="space-y-4">
            {showResetButton && (
                <div className="flex justify-end mb-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={onReset}
                        className="flex items-center gap-1"
                    >
                        <RefreshCw size={14} />
                        Reset
                    </Button>
                </div>
            )}
            {children}
        </div>
    )
}

// Function to render the appropriate editor component
export function renderEditorComponent(
    type: string,
    configKey: string,
    config: any,
    handleConfigChange: (changes: Partial<ReactGameConfig>) => void
): ReactNode {
    const Component = editorComponentMapping[type]

    if (!Component) {
        return <div>Unsupported config type: {type}</div>
    }

    const props = getComponentProps(type, configKey, config, handleConfigChange)
    return <Component {...props} />
}

// Define a type for the editor component props
export interface EditorComponentProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
}

// Define a type for the component mapping
export type ComponentMapping = {
    [key: string]: React.ComponentType<any>
}

// Helper function to get the appropriate props for a component based on its type
export function getComponentProps(
    type: string,
    configKey: string,
    config: any,
    handleConfigChange: (changes: Partial<ReactGameConfig>) => void
): any {
    // Special case for text editor which has a different prop structure
    if (type === 'text') {
        return {
            text: (config[configKey] as any) || {},
            onChange: (text: any) => handleConfigChange({ [configKey]: text }),
            configKey: configKey,
        }
    }

    // Special case for components that need a different onChange handler
    if (type === 'sound-switch' || type === 'reward-component-style' || type === 'container' || type === 'scratch-overlay') {
        return {
            config: config[configKey] as any,
            configKey: configKey,
            onChange: (changes: any) => handleConfigChange({
                [configKey]: {
                    ...config[configKey],
                    ...changes,
                },
            }),
        }
    }

    // Default case for most components
    return {
        configKey: configKey,
        config: config,
        onChange: handleConfigChange,
    } as EditorComponentProps
}

