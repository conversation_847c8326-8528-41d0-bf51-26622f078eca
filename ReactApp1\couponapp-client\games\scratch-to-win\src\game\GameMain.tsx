import { TryAgainScreen, OutOfLivesScreen, RewardScreen } from './Scenes'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { gameEvents } from '@repo/shared/lib/campaign/eventEmitter'
import { useGameAtom } from '@repo/shared/lib/atoms/atom-extensions'
import { ReactGameConfig } from '../types/config'
import { GameScreenId } from '../types/screen'
import { getBackgroundStyle } from '@repo/shared-game-utils/utils/gameStyleUtils'
import { MainScreenRewardComponent } from './components/MainScreenRewardComponent'
import { ScratchOverlay } from './components/ScratchOverlay'
import { ScratchUnderlay } from './components/ScratchUnderlay'
import { makeElementsInteractive } from '@repo/shared-game-utils/utils/previewInteractive'
import { useGameRewards } from '@repo/shared/lib/game/useGameRewards'
import { motion, AnimatePresence } from 'framer-motion'
import { findAllIconNames } from '@repo/shared-game-utils/utils/traverseObjectUtils'
import { PreloadIcon } from './components/IconsPreloader'
import { GameContext, GameRuntimeProps, useGame } from '@repo/shared-game-utils/hooks/useGame'
import { GameSoundSwitch } from '@repo/shared-game-utils/components/GameSoundSwitch'
import { usePreloadImage } from '@repo/shared-game-utils/hooks/usePreloadImage'
import { useMusic, useSoundEffect } from '@repo/shared-game-utils/hooks/useSounds'
import { CounterElement } from '@repo/shared-game-utils/components/CounterElement'

export type ScratchGameRuntimeProps = GameRuntimeProps & {
    currentScreenId?: GameScreenId
    initialGameScreenChecked: React.MutableRefObject<boolean>
}

export default function MainGame(props: ScratchGameRuntimeProps) {
    const [currentScreenId, setCurrentScreenId] = useState<GameScreenId>(props.currentScreenId || 'main-before-scratch')
    const initialGameScreenChecked = useRef(false)

    // Create an enhanced context with both props and screen state
    const contextValue = {
        ...props,
        currentScreenId,
        setCurrentScreenId,
        initialGameScreenChecked,
    }

    // Only update currentScreenId from props if it's different from current state
    useEffect(() => {
        if (props.currentScreenId && props.currentScreenId !== currentScreenId) {
            console.log('Updating currentScreenId from props:', props.currentScreenId)
            setCurrentScreenId(props.currentScreenId)
        }
    }, [props.currentScreenId, currentScreenId])

    return (
        <GameContext.Provider value={contextValue}>
            <GameContent />
        </GameContext.Provider>
    )
}

export const useGameState = () => {
    const { config, widgetId } = useGame<ReactGameConfig>()
    const initialLivesCount = config.gameEndHandler?.useLives ? config.gameEndHandler?.livesCount || 3 : Number.MAX_SAFE_INTEGER

    const [lives, setLivesCount] = useGameAtom(widgetId, 'livesCount', initialLivesCount)
    const [attemptsTaken, setAttemptsTaken] = useGameAtom(widgetId, 'attemptsTaken', 1)
    const [scratchProcessed, setScratchProcessed] = useGameAtom(widgetId, 'scratchProcessed', false)
    const [isAttemptCounted, setIsAttemptCounted] = useGameAtom(widgetId, 'attemptCounted', false)
    const [isLifeCounted, setIsLifeCounted] = useGameAtom(widgetId, 'lifeCounted', false)

    return {
        lives,
        setLivesCount,
        attemptsTaken,
        setAttemptsTaken,
        scratchProcessed,
        setScratchProcessed,
        isAttemptCounted,
        setIsAttemptCounted,
        isLifeCounted,
        setIsLifeCounted,
    }
}

function PrepareAssets() {
    const { config } = useGame<ReactGameConfig>()

    // Preload sounds
    useMusic(config.gameOverSound, false)
    useMusic(config.winSound, false)

    // Preload images
    usePreloadImage(config.gameOverOverlay?.asset)
    usePreloadImage(config.outOfLivesOverlay?.asset)
    usePreloadImage(config.gameSoundSwitch?.onAsset)
    usePreloadImage(config.gameSoundSwitch?.offAsset)
    usePreloadImage(config.scratchOverlay?.asset)
    usePreloadImage(config.scratchUnderlay?.asset)

    // Find and preload all icons in the config
    const allIcons = findAllIconNames(config)

    return (
        <>
            {allIcons.map((icon, index) => (
                <PreloadIcon key={`icon-${index}`} name={icon} />
            ))}
        </>
    )
}

function GameContent() {
    const { config, widgetId, resolveAssetUrl, isPreview, currentScreenId, setCurrentScreenId, onGameAssetSelect } = useGame<ReactGameConfig>()
    
    useMusic(config.backgroundMusic, true)

    // Create a ref for the container
    const containerRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        if (!isPreview) return

        makeElementsInteractive(onGameAssetSelect)
    }, [currentScreenId, isPreview, onGameAssetSelect])

    useEffect(() => {
        return () => {
            console.log('unmounting')
        }
    }, [])

    return (
        <div
            data-game-widget-id={widgetId}
            ref={containerRef}
            className="flex flex-col items-center justify-center h-full w-full touch-none select-none overscroll-none p-4"
            style={getBackgroundStyle(config.mainBackground, resolveAssetUrl)}
        >
            <div className="relative w-full h-full text-black">
                <PrepareAssets />
                <MainGameScreen />
            </div>
        </div>
    )
}

function MainGameScreen() {
    const [scratchKey, setScratchKey] = useState(0)
    const [isRevealed, setIsRevealed] = useState(false)
    const [isWinningAttempt, setIsWinningAttempt] = useState(false)
    const { initialGameScreenChecked, config, widgetId, currentScreenId, setCurrentScreenId, isPreview, resolveAssetUrl } = useGame<ReactGameConfig>()
    const {
        lives,
        setLivesCount,
        attemptsTaken,
        setAttemptsTaken,
        scratchProcessed,
        setScratchProcessed,
        isAttemptCounted,
        setIsAttemptCounted,
    } = useGameState()
    
    const { pickReward, reward, hasWonReward } = useGameRewards({ gameWidgetId: widgetId, attempt: attemptsTaken })
    const rewardsEnabled = config.gameRewardsHandler?.rewardsEnabled || false
    const useLives = config.gameEndHandler?.useLives

    // Debug: Log when currentScreenId changes
    useEffect(() => {
        console.log('MainGameScreen - currentScreenId changed:', currentScreenId)
    }, [currentScreenId])

    // Debug: Log when component mounts/unmounts
    useEffect(() => {
        console.log('MainGameScreen - mounted')
        return () => {
            console.log('MainGameScreen - unmounted')
        }
    }, [])


    useEffect(() => {
        if (isAttemptCounted) {
            return
        }

        setAttemptsTaken((prev: number) => (prev ?? 0) + 1)
        setIsAttemptCounted(true)
    }, [isAttemptCounted])

    // Pick a reward as soon as the game loads
    useEffect(() => {
        if (isPreview) return
        if (!rewardsEnabled) return
        if(!isAttemptCounted) return
        
        pickReward().then(result => {
            setIsWinningAttempt(result.hasWon)
        })

    }, [isPreview, rewardsEnabled, isAttemptCounted])

    // Function to handle try again button click
    const handleTryAgainClick = useCallback(() => {
        if (isPreview) return

        console.log('handleTryAgainClick - resetting game state')

        // Reset all relevant state in a specific order
        // First reset the screen ID to prevent any conditional rendering issues
        setCurrentScreenId('main-before-scratch')

        // Then reset all other state variables
        setIsRevealed(false)
        setScratchProcessed(false) // Reset the scratch processed state
        setIsAttemptCounted(false)
        setIsWinningAttempt(false)

        // Finally increment the scratch key to force a new scratch overlay
        setScratchKey((prev) => prev + 1) // This will reset ScratchOverlay

        console.log('Game state reset complete')
    }, [isPreview, setCurrentScreenId, setScratchProcessed, setIsAttemptCounted, setScratchKey, setIsRevealed])

    // Function to handle out of lives button click
    const handleOutOfLivesButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinishedNoReward', {
            score: 0,
            widgetId: widgetId,
        })
    }, [isPreview, widgetId])

    // Function to handle scratch completion
    const handleScratchComplete = useCallback(() => {
        if (isPreview) return

        console.log('handleScratchComplete - lives:', lives)

        // First set isRevealed to true to ensure the scratch overlay is removed
        setIsRevealed(true)

        if (rewardsEnabled) {
            // Use the already picked reward result
            if (isWinningAttempt) {
                console.log('Setting screen to claim-reward')
                setCurrentScreenId('claim-reward')
            } else if (useLives) {
                if (lives > 0) {
                    console.log('Setting screen to try-again')
                    setCurrentScreenId('try-again')
                } else {
                    console.log('Setting screen to out-of-lives')
                    setTimeout(() => {
                        setCurrentScreenId('out-of-lives')
                    }, 1200)
                }
            } else {
                console.log('Setting screen to try-again (no lives)')
                setCurrentScreenId('try-again')
            }
        } else {
            // If rewards are not enabled, just show try again or out of lives
            if (useLives) {
                if (lives > 0) {
                    setCurrentScreenId('try-again')
                } else {
                    setTimeout(() => {
                        setCurrentScreenId('out-of-lives')
                    }, 1200)
                }
            } else {
                setCurrentScreenId('try-again')
            }
        }
    }, [isPreview, setCurrentScreenId, useLives, lives, setIsRevealed, isWinningAttempt, rewardsEnabled])

    useEffect(() => {
        if (isPreview) return
        if (initialGameScreenChecked.current) return

        console.log('checking initial game screen', useLives, lives)
        initialGameScreenChecked.current = true

        if (useLives && lives <= 0) {
            console.log('out of lives, switching screen')
            setCurrentScreenId('out-of-lives')
            setIsRevealed(true)
            setScratchProcessed(true)
            return
        }
    }, [lives, useLives, setCurrentScreenId, isPreview, setIsRevealed, setScratchProcessed])

    // Components for separate preview and production rendering of main reward content
    const GameContentProductionMode = useMemo(() => {
        console.log('Rendering GameContentProductionMode, currentScreenId:', currentScreenId)

        //FIRST CASE
        if (currentScreenId === 'out-of-lives') {
            return (
                <motion.div
                    key="out-of-lives-screen"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3, ease: 'easeOut' }}
                    className="flex-grow flex flex-col items-center justify-center"
                >
                    <OutOfLivesScreen onButtonClick={handleOutOfLivesButtonClick} />
                </motion.div>
            )
        }

        //SECOND CASE
        return (
            <motion.div
                key="main-game-screen"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3, ease: 'easeOut' }}
                className="flex-grow flex flex-col items-center justify-center"
            >
                <ScratchOverlay key={scratchKey} isRevealed={isRevealed} setIsRevealed={setIsRevealed} onScratchComplete={handleScratchComplete}>
                    {isWinningAttempt ? <MainScreenRewardComponent dataConfigKey="mainScreenRewardComponent" reward={reward} hasWonReward={true} /> : <ScratchUnderlay />}
                </ScratchOverlay>

                <div className="relative w-full flex flex-col items-center">
                    {isRevealed && currentScreenId === 'try-again' && (
                        <div key="try-again-screen" className="absolute">
                            <TryAgainScreen onButtonClick={handleTryAgainClick} />
                        </div>
                    )}
                    {isRevealed && currentScreenId === 'claim-reward' && (
                        <div key="claim-reward-screen" className="absolute">
                            <RewardScreen onButtonClick={handleOutOfLivesButtonClick} />
                        </div>
                    )}
                </div>
            </motion.div>
        )
    }, [scratchKey, isRevealed, reward, isWinningAttempt, handleTryAgainClick, handleOutOfLivesButtonClick, currentScreenId, handleScratchComplete])

    const GameContentPreviewMode = useMemo(() => {
        console.log('Rendering GameContentPreviewMode, currentScreenId:', currentScreenId)
        if (currentScreenId === 'out-of-lives') {
            return (
                <div className="flex-grow flex flex-col items-center justify-center text-white">
                    <OutOfLivesScreen onButtonClick={handleOutOfLivesButtonClick} />
                </div>
            )
        }
        return (
            <div className="flex-grow flex flex-col items-center justify-center text-white">
                <ScratchOverlay isPreview={true} isRevealed={currentScreenId !== 'main-before-scratch'}>
                    {currentScreenId === 'claim-reward' || currentScreenId === 'main-after-scratch' ? (
                        <MainScreenRewardComponent dataConfigKey="mainScreenRewardComponent" reward={null} hasWonReward={true} />
                    ) : (
                        <ScratchUnderlay isPreview={true} />
                    )}
                </ScratchOverlay>

                <div className="relative w-full flex flex-col items-center">
                    {currentScreenId === 'try-again' && (
                        <div key="preview-try-again-screen" className="absolute">
                            <TryAgainScreen onButtonClick={handleTryAgainClick} />
                        </div>
                    )}
                    {currentScreenId === 'claim-reward' && (
                        <div key="preview-claim-reward-screen" className="absolute">
                            <RewardScreen onButtonClick={handleOutOfLivesButtonClick} />
                        </div>
                    )}
                </div>
            </div>
        )
    }, [currentScreenId, handleTryAgainClick, handleOutOfLivesButtonClick])

    return (
        <div className="game-content-area w-full h-full flex flex-col">
            {/* Top section with sound switch and lives indicator */}
            <div className="w-full flex">
                {/* Lives indicator */}

                <div className="absolute  w-full h-full flex flex-grow items-center justify-center">
                    <AnimatePresence mode="wait">{isPreview ? GameContentPreviewMode : GameContentProductionMode}</AnimatePresence>
                </div>

                <div
                    className={`w-full flex ${config.gameSoundSwitch?.alignment === 'center' ? 'justify-center' : config.gameSoundSwitch?.alignment === 'right' ? 'justify-end' : 'justify-start'} p-2`}
                >
                    <GameSoundSwitch config={config.gameSoundSwitch} dataConfigKey="gameSoundSwitch" />
                </div>

                {useLives && (
                        <CounterElement configKey="livesStyle" style={config.livesStyle}>
                            {lives}
                        </CounterElement>
                )}
            </div>
        </div>
    )
}
