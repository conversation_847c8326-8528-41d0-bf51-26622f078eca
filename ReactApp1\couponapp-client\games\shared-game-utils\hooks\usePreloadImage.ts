import { useEffect } from 'react'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'

export const usePreloadImage = (asset?: AssetUrl, onLoad?: () => void) => {
    const { resolveAssetUrl } = useGame()

    useEffect(() => {
        if (!asset || !resolveAssetUrl) {
            onLoad?.()
            return
        }

        const imageUrl = resolveAssetUrl(asset)
        if (!imageUrl) {
            onLoad?.()
            return
        }

        const img = new Image()
        img.src = imageUrl
        img.onload = () => {
            onLoad?.()
        }

    }, [asset, resolveAssetUrl])
}
