{"name": "@repo/shared-game-utils", "private": true, "version": "0.0.0", "type": "module", "main": "./index.ts", "exports": {"./components/*": {"types": "./components/*.tsx", "default": "./components/*.js"}, "./editor-components/*": {"types": "./editor-components/*.tsx", "default": "./editor-components/*.js"}, "./hooks/*": {"types": "./hooks/*.tsx", "default": "./hooks/*.js"}, "./types/*": {"types": "./types/*.tsx", "default": "./types/*.js"}, "./utils/*": {"types": "./utils/*.tsx", "default": "./utils/*.js"}}, "scripts": {"build": "tsc", "dev": "tsc -w"}, "dependencies": {"@repo/shared": "*"}, "devDependencies": {"phaser": "^4.0.0-beta.7"}}