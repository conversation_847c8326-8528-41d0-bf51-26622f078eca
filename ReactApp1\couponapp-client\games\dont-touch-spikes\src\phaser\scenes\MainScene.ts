import Phaser from "phaser";
import { defaultGameConfig, ReactGameConfig } from "../../types/config";
import { toPhaserTextStyle } from "@repo/shared-game-utils/utils/gameStyleUtils";
import { BaseScene } from "./BaseScene";
import { AssetUrl } from "@repo/shared/lib/types/widgetSettings";

export class MainScene extends BaseScene {
  private player?: Phaser.Physics.Arcade.Sprite;
  private spikes?: Phaser.Physics.Arcade.StaticGroup;
  private collectibles?: Phaser.Physics.Arcade.StaticGroup;
  private leftWall?: Phaser.Physics.Arcade.Sprite;
  private rightWall?: Phaser.Physics.Arcade.Sprite;
  private score = 0;
  private scoreText?: Phaser.GameObjects.Text;
  private instructionText?: Phaser.GameObjects.Text;
  private gameOver = false;
  private gameStarted = false;
  private gameArea?: Phaser.GameObjects.Image;
  private gameAreaBounds: { x: number; y: number; width: number; height: number } = { x: 0, y: 0, width: 0, height: 0 };
  private wallBounceTime = 0; // Add cooldown for wall bounces
  private firstJump = true; // Track if this is the first jump
  private gameAreaMask?: Phaser.Display.Masks.GeometryMask;
  private particleEmitter?: Phaser.GameObjects.Particles.ParticleEmitter;
  private lastJumpTime = 0; // Track when the player last clicked

  private jumpSound: Phaser.Sound.NoAudioSound | Phaser.Sound.HTML5AudioSound | Phaser.Sound.WebAudioSound;
  private spikeHitSound: Phaser.Sound.NoAudioSound | Phaser.Sound.HTML5AudioSound | Phaser.Sound.WebAudioSound;
  private correctWallHitSound: Phaser.Sound.NoAudioSound | Phaser.Sound.HTML5AudioSound | Phaser.Sound.WebAudioSound;
  private collectItemSound: Phaser.Sound.NoAudioSound | Phaser.Sound.HTML5AudioSound | Phaser.Sound.WebAudioSound;

  constructor(
    config: ReactGameConfig, 
    resolveAssetUrl: (id: AssetUrl) => string | undefined
  ) {
    super(config, resolveAssetUrl);
  }

  preload() {
    this.loadAssets();
  }

  create() {
    // Create centered game area
    this.createGameArea();
    
    // Create spikes around the game area
    this.createSpikes();
    
    // Create collectibles group
    this.createCollectibles();
    
    // Create score display
    this.createScoreText();
    
    // Create instruction text
    this.createInstructionText();

     // Create player in center
     this.createPlayer();
     
     // Create particle system
     this.createParticleSystem();
    
    // Setup physics
    this.setupPhysics();
    
    // Setup controls
    this.setupControls();
    
    // Setup sounds
    this.setupSounds();
    
  }

  private loadAssets() {
    this.loadAsset('image', 'gameArea');
    
    this.load.image('spikeLeft', 'https://dev-minio.beakbyte.com/couponapp/organization-f1caad67-1c08-4515-b292-62e10320bcca/02ef60c5-9d9a-4c35-8128-5690c271c546.png');
    this.load.image('spikeRight', 'https://dev-minio.beakbyte.com/couponapp/organization-f1caad67-1c08-4515-b292-62e10320bcca/22a74315-e1f6-4033-a2d2-4ee67a181c63.png');
    this.load.image('bottomSpikes', 'https://dev-minio.beakbyte.com/couponapp/organization-f1caad67-1c08-4515-b292-62e10320bcca/49501fce-699d-4426-8a7a-6aa0ed3c7f75.png');
    this.load.image('topSpikes', 'https://dev-minio.beakbyte.com/couponapp/organization-f1caad67-1c08-4515-b292-62e10320bcca/49501fce-699d-4426-8a7a-6aa0ed3c7f75.png');
    this.load.image('particle', 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f6/Eo_circle_red_blank.svg/2048px-Eo_circle_red_blank.svg.png');
    
    // Collectible items
    this.load.image('collectible1', 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f0/Eo_circle_yellow.svg/2048px-Eo_circle_yellow.svg.png');
    this.load.image('collectible2', 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/88/Eo_circle_blue.svg/2048px-Eo_circle_blue.svg.png');
    this.load.image('collectible3', 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/cc/Eo_circle_purple.svg/2048px-Eo_circle_purple.svg.png');
    
    this.loadSpriteSheet('player', 2);
    this.loadAsset('audio', 'jumpSound')
    this.loadAsset('audio', 'spikeHitSound')
    this.loadAsset('audio', 'correctWallHitSound')
    this.loadAsset('audio', 'collectItemSound')

  }

  private setupSounds() {
    this.jumpSound = this.setupSoundEffect("jumpSound");
    this.spikeHitSound = this.setupSoundEffect("spikeHitSound");
    this.correctWallHitSound = this.setupSoundEffect("correctWallHitSound");
    this.collectItemSound = this.setupSoundEffect("collectItemSound");
  }



  private createGameArea() {
    const screenWidth = 900
    const screenHeight = 1600
    let gameWidth = 900
    let gameHeight = 1600
    
    const gameX = (screenWidth - gameWidth) / 2;
    const gameY = (screenHeight - gameHeight) / 2;

    this.gameAreaBounds = {
      x: gameX,
      y: gameY,
      width: gameWidth,
      height: gameHeight
    };

    // Create game area using image instead of rectangle
    this.gameArea = this.add.image(
      gameX + gameWidth / 2,
      gameY + gameHeight / 2,
      'gameArea'
    );
    
    // Scale the image to match the desired size
    this.gameArea.setDisplaySize(gameWidth, gameHeight);
    
    this.createWalls();
  }

  private createWalls() {
    const { x, y, width, height } = this.gameAreaBounds;
    const wallThickness = 20;


    // Create left wall and assign to class member
    this.leftWall = this.physics.add.sprite(x - wallThickness / 2, y + height / 2, null);
    this.leftWall.setImmovable(true);
    (this.leftWall.body as Phaser.Physics.Arcade.Body).setAllowGravity(false);
    this.leftWall.body.setSize(wallThickness, height);
    this.leftWall.setVisible(false);
    this.leftWall.setName('leftWall');

    this.rightWall = this.physics.add.sprite(x + width + wallThickness / 2, y + height / 2, null);
    this.rightWall.setImmovable(true);
    (this.rightWall.body as Phaser.Physics.Arcade.Body).setAllowGravity(false);
    this.rightWall.body.setSize(wallThickness, height);
    this.rightWall.setVisible(false);
    this.rightWall.setName('rightWall');
  }

  private createSpikes() {
    this.spikes = this.physics.add.staticGroup();
    
    // Create a mask to clip spikes to game area
    const { x, y, width, height } = this.gameAreaBounds;
    const maskShape = this.add.graphics();
    maskShape.fillStyle(0xffffff);
    maskShape.fillRect(x, y, width, height);
    
    // Create the geometry mask
    this.gameAreaMask = maskShape.createGeometryMask();
    
    // Hide the mask shape since we only need it for clipping
    maskShape.setVisible(false);
  }

  private createCollectibles() {
    this.collectibles = this.physics.add.staticGroup();
  }

  private createSpikesOnSide(side: 'left' | 'right') {
    const spikeSize = 80;
    const { x: gameX, y: gameY, width: gameWidth, height: gameHeight } = this.gameAreaBounds;
    
    const minGroups = 2;
    const maxGroups = 7;
    const numGroups = Phaser.Math.Between(minGroups, maxGroups);
    
    // Calculate available space for groups (leave some padding at top and bottom)
    const padding = spikeSize * 2;
    const availableHeight = gameHeight - (2 * padding);
    
    // Player size is 120px, so we need at least that much space between groups + some extra margin
    const playerSize = 120;
    const minSpaceBetweenGroups = playerSize ; 
    
    // Store occupied regions to prevent overlaps
    const occupiedRegions: Array<{ start: number; end: number }> = [];
    
    // Calculate spike positions - start position (outside game area) and final position
    const finalSpikeX = side === 'left' 
      ? gameX + spikeSize / 2 
      : gameX + gameWidth - spikeSize / 2;
    
    const startSpikeX = side === 'left'
      ? gameX - spikeSize + 30  // Start further left
      : gameX + gameWidth + spikeSize - 30 ; // Start further right
    
    for (let group = 0; group < numGroups; group++) {
      // Random number of spikes per group (1-3)
      const spikesInGroup = Phaser.Math.Between(1, 6);
      const groupHeight = spikesInGroup * spikeSize;
      
      // Find a valid position for this group
      let groupStartY: number;
      let attempts = 0;
      const maxAttempts = 50;
      
      do {
        const maxGroupY = availableHeight - groupHeight;
        groupStartY = gameY + padding + Phaser.Math.Between(0, maxGroupY);
        attempts++;
      } while (
        attempts < maxAttempts && 
        this.isPositionOccupied(groupStartY, groupStartY + groupHeight, occupiedRegions, minSpaceBetweenGroups)
      );
      
      // If we found a valid position, create the group
      if (attempts < maxAttempts) {
        // Add this region to occupied regions
        occupiedRegions.push({
          start: groupStartY - minSpaceBetweenGroups / 2,
          end: groupStartY + groupHeight + minSpaceBetweenGroups / 2
        });
        
        // Create spikes in this group
        for (let i = 0; i < spikesInGroup; i++) {
          const yPos = groupStartY + (i * spikeSize) 
          const spikeTexture = side === 'left' ? 'spikeLeft' : 'spikeRight';
          const spike = this.spikes?.create(startSpikeX, yPos, spikeTexture);
          
          spike.setDisplaySize(spikeSize, spikeSize); 
          spike.setMask(this.gameAreaMask);
          
          const body = spike.body as Phaser.Physics.Arcade.StaticBody;
          body.updateFromGameObject();
          body.setSize(190, 25); // 70% of visual size
          
          this.tweens.add({
            targets: [spike],
            x: finalSpikeX,
            duration: 200,
            delay: 0,
            ease: 'Linear',
          
          });
        }
      }
    }
  }

  private isPositionOccupied(
    startY: number, 
    endY: number, 
    occupiedRegions: Array<{ start: number; end: number }>, 
    minSpace: number
  ): boolean {
    for (const region of occupiedRegions) {
      // Check if the new group would overlap or be too close to an existing group
      if (!(endY + minSpace / 2 < region.start || startY - minSpace / 2 > region.end)) {
        return true; // Position is occupied or too close
      }
    }
    return false; // Position is free
  }

  private spawnCollectibles(side: 'left' | 'right') {
    if (!this.collectibles) return;

    const { x: gameX, y: gameY, width: gameWidth, height: gameHeight } = this.gameAreaBounds;
    const itemSize = 30;
    
    // Spawn 1-2 collectibles randomly in the game area
    const numItems = Phaser.Math.Between(1, 2);
    
    for (let i = 0; i < numItems; i++) {
      // Random position in the center area of the game (avoiding edges where spikes might be)
      const horizontalPadding = 80; // Stay away from side edges
      const verticalPadding = gameHeight * 0.25; // 25% padding from top and bottom
      const itemX = gameX + horizontalPadding + Math.random() * (gameWidth - 2 * horizontalPadding);
      const itemY = gameY + verticalPadding + Math.random() * (gameHeight - 2 * verticalPadding);
      
             // Randomly choose item type
       const itemTypes = [
         { texture: 'collectible1', points: 1 }, // 1 point
         { texture: 'collectible2', points: 3 }, // 3 points  
         { texture: 'collectible3', points: 5 } // 5 points
       ];
      
      const weights = [0.6, 0.3, 0.1]; // 60% coin, 30% gem, 10% star
      const randomValue = Math.random();
      let selectedType = itemTypes[0]; // default to coin
      
      let cumulativeWeight = 0;
      for (let j = 0; j < itemTypes.length; j++) {
        cumulativeWeight += weights[j];
        if (randomValue <= cumulativeWeight) {
          selectedType = itemTypes[j];
          break;
        }
      }
      
             const collectible = this.collectibles.create(itemX, itemY, selectedType.texture);
       collectible.setDisplaySize(itemSize, itemSize);
       collectible.setData('points', selectedType.points);
       collectible.setMask(this.gameAreaMask);
       collectible.setDepth(120); // Above score text (100) but below player (150)
       
       // Add up-down floating animation
       this.tweens.add({
         targets: collectible,
         y: itemY - 15,
         duration: 1500,
         yoyo: true,
         repeat: -1,
         ease: 'Sine.easeInOut'
       });
      
      const body = collectible.body as Phaser.Physics.Arcade.StaticBody;
      body.updateFromGameObject();
      body.setSize(itemSize * 0.8, itemSize * 0.8); // Slightly smaller hitbox
    }
  }

  private createPlayer() {
    const centerX = this.gameAreaBounds.x + this.gameAreaBounds.width / 2;
    const centerY = this.gameAreaBounds.y + this.gameAreaBounds.height / 2;

    this.player = this.physics.add.sprite(centerX, centerY, 'player');
    this.player.setCollideWorldBounds(false); 
    this.player.setBounce(1, 0.3);
    this.player.setDisplaySize(150, 100);
    this.player.setDepth(150); // On top of score text
    
    // Set physics body to 70% of visual size for more fair gameplay
    const body = this.player.body as Phaser.Physics.Arcade.Body;
    const colliderWidth = this.player.displayWidth * 0.9; // 70% of visual width
    const colliderHeight = 120 * 0.9; // 70% of visual height
    body.setSize(colliderWidth, colliderHeight);
    
    this.player.setMask(this.gameAreaMask);
    
    // Create flying animation with the 2 frames
    this.anims.create({
      key: 'fly',
      frames: this.anims.generateFrameNumbers('player', { start: 0, end: 1 }),
      frameRate: 8, // Adjust speed as needed
      repeat: -1 // Loop indefinitely
    });
    
    // Start the flying animation
    this.player.play('fly');
    
    // COMPLETELY disable physics body until game starts
    body.enable = false;
  }

  private createParticleSystem() {
    if (!this.player) return;

    // Create particle emitter
    this.particleEmitter = this.add.particles(0, 0, 'particle', {
      speed: { min: 0, max: 3 },
      scale: { start: 0.02, end: 0 }, // 50px / 2048px = ~0.024 for 50x50 initial size
      alpha: { start: 1, end: 0 },
      lifespan: 700,
      quantity: 1,
      emitting: false // Keep emitting disabled, we'll manually emit
    });

    // Set particle emitter depth below the player but above the game area
    this.particleEmitter.setDepth(140);
    
    // Apply mask to particles so they don't appear outside game area
    if (this.gameAreaMask) {
      this.particleEmitter.setMask(this.gameAreaMask);
    }

    // Create a timer to emit particles every 100ms, but only if clicked within last 500ms
    this.time.addEvent({
      delay: 100, // 100ms interval
      callback: () => {
        if (this.gameStarted && !this.gameOver && this.player && this.particleEmitter) {
          const currentTime = this.time.now;
          if (currentTime - this.lastJumpTime < 400) {
            // Emit particle at current player position
            this.particleEmitter.emitParticleAt(this.player.x, this.player.y, 1);
          }
        }
      },
      loop: true
    });
  }

  private createScoreText() {
    const { x, y, width, height } = this.gameAreaBounds;
    
    // Use the scoreStyle from config and convert it to Phaser text style
    // const textStyle = toPhaserTextStyle(this.config.scoreStyle.style);
    const textStyle = toPhaserTextStyle(this.config.scoreStyle);
    
    this.scoreText = this.add.text(
      x + width / 2, 
      y + height / 2, 
      this.score.toString(),
      {
        ...textStyle,
        align: 'center',
        
      }
    );
    
    this.scoreText.setOrigin(0.5, 0.5);
    this.scoreText.setDepth(100); // Behind bird but on top of game area
    
    // Hide score text initially (game is in idle mode)
    this.scoreText.setVisible(false);
  }

  private createInstructionText() {
    const { x, y, width, height } = this.gameAreaBounds;
    
    // Use the instructionText from config and convert it to Phaser text style
    const textStyle = toPhaserTextStyle(this.config.instructionText.style);
    
    this.instructionText = this.add.text(
      x + width / 2, 
      y + height / 2 + 120, 
      this.config.instructionText.text,
      {
        ...textStyle,
        align: 'center',
      }
    );
    
    this.instructionText.setOrigin(0.5, 0.5);
    this.instructionText.setDepth(100); // Same depth as score text
    
    // Apply mask to keep text within game area
    this.instructionText.setMask(this.gameAreaMask);
    
    // Show instruction text initially (game is in idle mode)
    this.instructionText.setVisible(true);
  }

  private setupPhysics() {
    if (this.player && this.spikes) {
      this.physics.add.collider(this.player, this.spikes, this.handleSpikeCollision, undefined, this);
    }

    if (this.player && this.collectibles) {
      this.physics.add.overlap(this.player, this.collectibles, this.handleCollectibleCollision, undefined, this);
    }

    if (this.player && this.leftWall) {
      this.physics.add.collider(this.player, this.leftWall, this.handleLeftWallCollision, undefined, this);
    }

    if (this.player && this.rightWall) {
      this.physics.add.collider(this.player, this.rightWall, this.handleRightWallCollision, undefined, this);
    }
  }

  private setupControls() {
    this.input.on('pointerdown', this.handleInput, this);
    
    this.input.keyboard?.on('keydown-SPACE', this.handleInput, this);
    this.input.keyboard?.on('keydown-UP', this.handleInput, this);
    this.input.keyboard?.on('keydown-W', this.handleInput, this);
  }

 

  private handleInput() {
    if (this.gameOver) {
      this.restartGame();
      return;
    }

    this.handleJump()
  }

  private handleJump() {
     // Update last click time
     this.lastJumpTime = this.time.now;

     if (!this.gameStarted) {
       // Start the game on first input
       this.gameStarted = true;
       if (this.player) {
         const body = this.player.body as Phaser.Physics.Arcade.Body;
         body.enable = true; // Re-enable physics body
         body.setGravityY(800); // Enable gravity
         body.setMaxVelocity(10000, 10000); // Set reasonable max velocities
       }
       
       // Start particle trail when game starts
       if (this.particleEmitter) {
         this.particleEmitter.start();
       }
       
       // Show score text when game starts
       if (this.scoreText) {
         this.scoreText.setVisible(true);
       }
       
       // Hide instruction text when game starts
       if (this.instructionText) {
         this.instructionText.setVisible(false);
       }
     }
 
     const speed = 650;
 
     if (this.player && this.gameStarted) {
       // Apply upward force
       this.player.setVelocityY(-800);
       
       // Emit burst of particles on jump
       if (this.particleEmitter) {
         this.particleEmitter.explode(8); // Burst of 8 particles on jump
       }
       
       // Only set rightward movement on first jump, after that maintain constant speed
       if (this.firstJump) {
         this.player.setVelocityX(speed);
         this.firstJump = false;
       } else {
         // Maintain constant horizontal speed in current direction
         const currentVelocityX = this.player.body.velocity.x;
         this.player.setVelocityX(currentVelocityX > 0 ? speed : -speed);
        }
        
        if(this.jumpSound) {
          this.jumpSound.play();
        }
     }
  }

  private handleCollectibleCollision(player: Phaser.Physics.Arcade.Sprite, collectible: Phaser.Physics.Arcade.Sprite) {
    
    // Get points from the collectible
    const points = collectible.getData('points') || 1;
    
    // Add points to score
    this.score += points;
    this.updateScoreText();
    
    // Play collect sound
    if (this.collectItemSound) {
      this.collectItemSound.play();
    }
    
    // Create a floating score text effect
    const floatingText = this.add.text(collectible.x, collectible.y, `+${points}`, {
      fontSize: '24px',
      color: '#FFD700',
      fontStyle: 'bold'
    });
    floatingText.setOrigin(0.5);
    floatingText.setDepth(200);
    
    // Animate floating text
    this.tweens.add({
      targets: floatingText,
      y: floatingText.y - 50,
      alpha: 0,
      duration: 800,
      ease: 'Power2',
      onComplete: () => {
        floatingText.destroy();
      }
    });
    
    // Remove the collectible
    collectible.destroy();
  }

  private handleSpikeCollision() {
    // Don't apply any forces if game is already over
    if (this.gameOver) {
      return;
    }

    if (this.spikeHitSound) {
      this.spikeHitSound.play();
    }
    this.gameOver = true; 

    if (this.player) {
      // Add downward force and rotation when hitting spike
      this.player.setVelocityY(1600); // Strong downward force
      this.player.setVelocityX(this.player.body.velocity.x * 2.5); // Reduce horizontal velocity
      this.player.setAngularVelocity(600); // Add rotation
    }

    // Wait 1 second before calling onGameOver and restarting
    this.time.delayedCall(600, () => {
      this.restartGame();
    });
  }

  private handleLeftWallCollision() {
    // Don't increment score if game is already over
    if (this.gameOver) {
      return;
    }
    
    console.log('Collided with left wall');
    
    if (this.correctWallHitSound) {
      this.correctWallHitSound.play();
    }

    // Increment score and update display
    this.score++;
    this.updateScoreText();
    
    // Flip bird to face right (normal direction)
    if (this.player) {
      this.player.setFlipX(false);
    }
    
    this.spikes.getChildren().forEach(child => {
      child.destroy()
    })
    this.createSpikesOnSide('right')
    
    if (this.collectibles.getLength() == 0) {
      this.spawnCollectibles('right')
    }
  }

  private handleRightWallCollision() {
    // Don't increment score if game is already over
    if (this.gameOver) {
      return;
    }
    
    console.log('Collided with right wall');
    
    if (this.correctWallHitSound) {
      this.correctWallHitSound.play();
    }

    // Increment score and update display
    this.score++;
    this.updateScoreText();
    
    // Flip bird to face left
    if (this.player) {
      this.player.setFlipX(true);
    }
    
    this.spikes.getChildren().forEach(child => {
      child.destroy()
    })

    this.createSpikesOnSide('left')
    
    if (this.collectibles.getLength() == 0) {
      this.spawnCollectibles('left')
    }
  }

  private updateScoreText() {
    if (this.scoreText) {
      this.scoreText.setText(this.score.toString());
    }
  }

  private restartGame() {
    this.gameOver = false;
    this.gameStarted = false;
    this.score = 0;
    this.firstJump = true; // Reset first jump flag
    
    // Update score display and hide it (back to idle mode)
    this.updateScoreText();
    if (this.scoreText) {
      this.scoreText.setVisible(false);
    }
    
    // Show instruction text again (back to idle mode)
    if (this.instructionText) {
      this.instructionText.setVisible(true);
    }
    
    // Stop particle emitter
    if (this.particleEmitter) {
      this.particleEmitter.stop();
    }
    
    // Clear all existing spikes and collectibles
    if (this.spikes) {
      this.spikes.getChildren().forEach(child => {
        child.destroy();
      });
    }
    
    if (this.collectibles) {
      this.collectibles.getChildren().forEach(child => {
        child.destroy();
      });
    }
    
    if (this.player) {
      const centerX = this.gameAreaBounds.x + this.gameAreaBounds.width / 2;
      const centerY = this.gameAreaBounds.y + this.gameAreaBounds.height / 2;
      this.player.setPosition(centerX, centerY);
      this.player.setVelocity(0, 0);
      this.player.setAngularVelocity(0); // Reset rotation
      this.player.setRotation(0); // Reset rotation angle
      this.player.setFlipX(false);
      
      // COMPLETELY disable physics body until game starts again
      const body = this.player.body as Phaser.Physics.Arcade.Body;
      body.enable = false;
    }
  }

  update() {
    if (this.gameOver || !this.player || !this.gameStarted) {
      return;
    }

    // Only check top and bottom boundaries for game over (spikes handle side collisions)
    const playerBounds = this.player.getBounds();
    const { y, height } = this.gameAreaBounds;

    if (playerBounds.top <= y || playerBounds.bottom >= y + height) {
      this.handleSpikeCollision();
    }
  }
} 