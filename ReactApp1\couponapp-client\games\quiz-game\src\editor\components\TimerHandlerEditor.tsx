import React from 'react'
import { Label } from '@repo/shared/components/ui/label'
import { Slider } from '@repo/shared/components/ui/slider'
import { GameTimerHandler } from '@repo/shared-game-utils/types/uiStyles'
interface TimerHandlerEditorProps {
    config: any
    configKey: string
    onChange: (config: any) => void
}

export const TimerHandlerEditor: React.FC<TimerHandlerEditorProps> = ({ config, configKey, onChange }) => {
    const timerHandler = (config[configKey] || {}) as GameTimerHandler
    const useTimer = timerHandler.useTimer ?? false
    const timerDuration = timerHandler.timerDuration ?? 20

    const handleTimerDurationChange = (value: number[]) => {
        onChange({
            [configKey]: {
                ...timerHandler,
                timerDuration: value[0],
                loseLifeOnTimeout: true, // Always true
            },
        })
    }

    if (!useTimer) {
        return (
            <div className="text-sm text-muted-foreground">
                Timer is disabled. Enable it using the toggle above to configure timer settings.
            </div>
        )
    }

    return (
        <div className="space-y-4">
            <div className="space-y-2">
                <div className="flex items-center justify-between">
                    <Label>Question Time Limit (seconds)</Label>
                    <span className="text-sm">{timerDuration}</span>
                </div>
                <Slider
                    value={[timerDuration]}
                    min={5}
                    max={120}
                    step={5}
                    onValueChange={handleTimerDurationChange}
                />
            </div>
        </div>
    )
}
