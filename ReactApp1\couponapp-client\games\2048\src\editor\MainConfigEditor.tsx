import { useState } from 'react'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@repo/shared/components/ui/card'
import { Label } from '@repo/shared/components/ui/label'
import { GameEditorComponentProps } from '@repo/shared/lib/game/game'
import { Switch } from '@repo/shared/components/ui/switch'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { Button } from '@repo/shared/components/ui/button'
import { RefreshCw, Settings } from 'lucide-react'
import { Game2048Config, game2048DefaultConfig } from '../types/game2048Config'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@repo/shared/components/ui/dialog'
import DialogConfigEditor from './DialogConfigEditor'


export default function MainConfigEditor({ config, updateConfig }: GameEditorComponentProps) {
    if (!config) config = {}

    const [isGameSettingsDialogOpen, setIsGameSettingsDialogOpen] = useState(false)

    const handleConfigChange = (key: keyof Game2048Config, value: any) => {
        updateConfig({ ...config, [key]: value })
    }

    const handleResetToDefault = (key: keyof Game2048Config) => {
        const defaultValue = game2048DefaultConfig[key]
        if (defaultValue !== undefined) {
            handleConfigChange(key, defaultValue)
        }
    }

    return (
        <div className="space-y-6">
            {/* Game Settings Dialog */}
            <Card>
                <CardHeader>
                    <CardTitle>Game Settings</CardTitle>
                    <CardDescription>Configure advanced game settings like tiles, grid, and style</CardDescription>
                </CardHeader>
                <CardContent>
                    <Dialog open={isGameSettingsDialogOpen} onOpenChange={setIsGameSettingsDialogOpen}>
                        <DialogTrigger asChild>
                            <Button variant="outline" className="w-full">
                                <Settings className="h-4 w-4 mr-2" />
                                Open Game Settings
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                                <DialogTitle>Game Settings</DialogTitle>
                            </DialogHeader>
                            <DialogConfigEditor config={config} updateConfig={updateConfig} />
                        </DialogContent>
                    </Dialog>
                </CardContent>
            </Card>

            {/* Main Background Card */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>Main Background</CardTitle>
                        <CardDescription>Set the main background for your 2048 game</CardDescription>
                    </div>
                    <Button variant="outline" size="sm" onClick={() => handleResetToDefault('mainBackground')} className="flex items-center gap-1">
                        <RefreshCw size={14} />
                        Reset
                    </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <Label className="mb-2 block">Background Image (800x600px)</Label>
                        <AssetPicker
                            exactSize={{ width: 800, height: 600 }}
                            onSelect={(assetId) =>
                                handleConfigChange('mainBackground', {
                                    ...config.mainBackground,
                                    asset: { assetId },
                                })
                            }
                            assetUrl={config.mainBackground?.asset}
                            extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']}
                        />
                    </div>

                    <div>
                        <div className="flex items-center space-x-2 mb-2">
                            <Label className="flex-grow">Background Color</Label>
                            <Switch
                                checked={config.mainBackground?.useBackgroundColor !== false}
                                onCheckedChange={(useBackgroundColor) =>
                                    handleConfigChange('mainBackground', {
                                        ...config.mainBackground,
                                        useBackgroundColor,
                                    })
                                }
                            />
                        </div>
                        <div className={`transition-opacity ${config.mainBackground?.useBackgroundColor !== false ? 'opacity-100' : 'opacity-40'}`}>
                            <ColorPicker
                                color={config.mainBackground?.fill || '#faf8ef'}
                                onChange={(fill) =>
                                    handleConfigChange('mainBackground', {
                                        ...config.mainBackground,
                                        fill,
                                    })
                                }
                            />
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
} 