import React, { useCallback, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useIsFirstRender } from '@uidotdev/usehooks'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { useAtom } from 'jotai'
import { gamePreviewScreenAtomFamily, selectedEditorItemAtom } from '@repo/shared/lib/atoms/editor-atoms'
import { defaultGameConfig, ReactGameConfig } from '../types/config'
import { useGameRewards } from '@repo/shared/lib/game/useGameRewards'
import MainGame from './GameMain'

// Import shared game utilities
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { GameButton } from '@repo/shared-game-utils/components/GameButton'
import { GameText } from '@repo/shared-game-utils/components/GameText'
import { useSoundEffect } from '@repo/shared-game-utils/hooks/useSounds'
import { RewardComponent } from '@repo/shared-game-utils/components/RewardComponent'


// Reward Screen - Shows the reward screen when rewards are enabled
export const RewardScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    const { reward } = useGameRewards({ gameWidgetId: widgetId })
    const isFirstRender = useIsFirstRender()

    const winSoundEffect = useSoundEffect(config.winSound)

    useEffect(() => {
        if (!isFirstRender) {
            return
        }
        winSoundEffect?.play()
    }, [isFirstRender, winSoundEffect])

    // Animation variants for the container - quick fade in
    const containerVariants = {
        hidden: {
            opacity: 0,
        },
        visible: {
            opacity: 1,
            transition: {
                duration: 0.3,
                ease: 'easeOut',
            },
        },
    }

    return (
        <motion.div
            className="h-full w-full flex flex-col items-center justify-center"
            data-editor-selectable-key="rewardOverlay"
            data-editor-selectable-is-background={true}
            style={{
                padding: '2rem',
                backgroundColor: config.rewardOverlay?.useBackgroundColor ? config.rewardOverlay?.fill || 'rgba(204, 153, 0, 0.9)' : undefined,
                backgroundImage: config.rewardOverlay?.asset && resolveAssetUrl ? `url(${resolveAssetUrl(config.rewardOverlay.asset)})` : undefined,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
            }}
            onClick={(e) => {
                e.stopPropagation()
            }}
            variants={containerVariants}
            initial="hidden"
            animate="visible"
        >
            <motion.div initial={{ y: 30, opacity: 0 }} animate={{ y: 0, opacity: 1 }} transition={{ duration: 0.4, delay: 0.1 }}>
                <GameText
                    config={config.rewardTitle}
                    dataConfigKey="rewardTitle"
                    className="mb-4"
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                />
            </motion.div>

           
                <motion.div className="mb-4" initial={{ y: 30, opacity: 0 }} animate={{ y: 0, opacity: 1 }} transition={{ duration: 0.4, delay: 0.2 }}>
                    <RewardComponent dataConfigKey="rewardComponent" reward={reward} />
                </motion.div>

                <motion.div initial={{ y: 30, opacity: 0 }} animate={{ y: 0, opacity: 1 }} transition={{ duration: 0.4, delay: 0.3 }}>
                    <GameButton
                        config={config.rewardClaimButton || config.continueButton}
                        dataConfigKey="rewardClaimButton"
                        onClick={(e) => {
                            e.stopPropagation()
                            if (onButtonClick) {
                                onButtonClick()
                            }
                        }}
                    />
                </motion.div>
        </motion.div>
    )
}

// Out of Lives Screen - Shows when player has no lives left
export const OutOfLivesScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    const isFirstRender = useIsFirstRender()
    const gameOverSoundEffect = useSoundEffect(config.gameOverSound)

    useEffect(() => {
        console.log('isFirstRender', isFirstRender)
        if (!isFirstRender) {
            return
        }
        console.log('playing game over sound')
        gameOverSoundEffect?.play()
    }, [isFirstRender, gameOverSoundEffect])

    return (
        <div
            className="h-full w-full flex flex-col items-center justify-center "
            data-editor-selectable-key="outOfLivesOverlay"
            style={{
                padding: '2rem',
                backgroundColor: config.outOfLivesOverlay?.useBackgroundColor ? config.outOfLivesOverlay?.fill : undefined,
                backgroundImage: config.outOfLivesOverlay?.asset && resolveAssetUrl ? `url(${resolveAssetUrl(config.outOfLivesOverlay.asset)})` : undefined,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
            }}
        >
            <GameText config={config.outOfLivesTitle} dataConfigKey="outOfLivesTitle" className="mb-4" />

            <GameButton
                config={config.outOfLivesContinueButton}
                dataConfigKey="outOfLivesContinueButton"
                onClick={(e) => {
                    e.stopPropagation()
                    if (onButtonClick) {
                        onButtonClick()
                    }
                }}
            />
        </div>
    )
}

export interface PreviewSceneProps {
    config: ReactGameConfig
    widgetId: string
    resolveAssetUrl: (id: AssetUrl) => string
}

export const PreviewScene: React.FC<PreviewSceneProps> = ({ config, widgetId, resolveAssetUrl }) => {
    const [selectedScreen, setSelectedScreen] = useAtom(gamePreviewScreenAtomFamily(widgetId))
    const [editorSelection, setEditorSelection] = useAtom(selectedEditorItemAtom)
    const initialGameScreenChecked = useRef(false)

    const currentScreen = selectedScreen ?? 'main'

    const handleAssetSelect = useCallback(
        (assetKey: string, bounds: { x: number; y: number; width: number; height: number }) => {
            if (!widgetId) return

            setEditorSelection({
                widgetId,
                assetKey,
                bounds: {
                    x: bounds.x,
                    y: bounds.y,
                    width: bounds.width,
                    height: bounds.height,
                    isAbsolute: true,
                },
                type: 'game-asset',
                time: Date.now(),
            })
        },
        [widgetId, setEditorSelection]
    )

    return (
        <MainGame config={config} widgetId={widgetId} isPreview={true} resolveAssetUrl={resolveAssetUrl}
        currentScreenId={currentScreen as any} initialGameScreenChecked={initialGameScreenChecked} setCurrentScreenId={() => { } } 
        defaultConfig={defaultGameConfig} />
    )
}
