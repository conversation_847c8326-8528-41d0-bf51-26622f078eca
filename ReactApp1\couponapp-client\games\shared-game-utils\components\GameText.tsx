import React from 'react'
import { GameTextSettings } from '../types/uiStyles'

// GameText component for consistent text styling
interface GameTextProps {
    config?: GameTextSettings
    onClick?: (e: React.MouseEvent) => void
    dataConfigKey?: string
    className?: string
    children?: React.ReactNode
}

export const GameText: React.FC<GameTextProps> = ({ config, onClick, dataConfigKey, className = '', children }) => {
    // Extract style from config or use empty object as fallback
    const textStyle = config?.style || {}

    // Convert textAlign to valid CSS properties
    const getTextAlignmentStyles = (textAlign: string | undefined) => {
        if (!textAlign) return { textAlign: 'center' as const }

        // Handle horizontal alignment part
        let horizontalAlign: 'left' | 'right' | 'center' = 'center'
        if (textAlign.includes('left')) horizontalAlign = 'left'
        if (textAlign.includes('right')) horizontalAlign = 'right'

        return { textAlign: horizontalAlign }
    }

    const alignmentStyles = getTextAlignmentStyles(textStyle.textAlign)

    // Create combined style object
    const combinedStyle = {
        color: textStyle.fill || '#000000',
        fontFamily: textStyle.fontFamily_tFontFamily || 'Londrina Solid',
        fontSize: textStyle.fontSize ? `${textStyle.fontSize}px` : '18px',
        fontWeight: textStyle.fontWeight || 'bold',
        textAlign: alignmentStyles.textAlign,
        // Apply offset if specified
        transform: (textStyle.offsetX || textStyle.offsetY) ?
            `translate(${textStyle.offsetX || 0}px, ${textStyle.offsetY || 0}px)` : undefined,
        // Apply shadow if specified and visible
        textShadow: textStyle.shadow?.isVisible
            ? `${textStyle.shadow.offsetX || 0}px ${textStyle.shadow.offsetY || 0}px ${textStyle.shadow.blur || 0}px ${textStyle.shadow.color || '#000000'}`
            : undefined,
    }

    return (
        <div data-editor-selectable-key={dataConfigKey} className={className} style={combinedStyle} onClick={onClick}>
            {config?.text || children}
        </div>
    )
}
