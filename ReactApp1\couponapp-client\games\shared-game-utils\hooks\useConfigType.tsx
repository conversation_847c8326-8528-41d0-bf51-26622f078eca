import React, { createContext, useContext, ReactNode } from 'react'
import { getConfigKeyDefinition } from '@repo/shared/lib/game/gameConfig'
import { ConfigTypeContext } from '@repo/shared/lib/game/configTypeContext'



export function useConfigKeyDefinition(configKey?: string | number | symbol) {
     const context = useContext(ConfigTypeContext)
    
    if (!configKey) {
        return undefined
    }
    
    return getConfigKeyDefinition(context.configType, configKey)
}