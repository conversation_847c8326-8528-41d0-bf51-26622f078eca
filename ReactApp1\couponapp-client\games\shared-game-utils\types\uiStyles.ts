import { AssetUrl } from "@repo/shared/lib/types/widgetSettings"

export type ScoreElementStyle = {
    asset?: AssetUrl
    fill?: string
    fontFamily_tFontFamily?: string
    fontSize?: number
    textColor?: string
    textAlign?: 'left' | 'center' | 'right' | 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
    padding?: {
        top?: number
        right?: number
        bottom?: number
        left?: number
    }
    marginBottom?: number
    width?: number
    height?: number
    useBackgroundColor?: boolean
    borderRadius?: number
}





export type ContainerStyle = BackgroundStyle & {
    borderRadius?: number
    padding?: {
        top?: number
        right?: number
        bottom?: number
        left?: number
    }
    maxWidth?: number
    minHeight?: number
    pixelated?: boolean
}







export type TextStyle = {
    fontFamily_tFontFamily?: string
    fontSize?: number
    fontWeight?: 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900'
    fill?: string
    shadow?: {
        offsetX?: number
        offsetY?: number
        color?: string
        blur?: number
        isVisible?: boolean
    }
    offsetX?: number
    offsetY?: number
    textAlign?: 'left' | 'center' | 'right'
    isVisible?: boolean
}

export type GameTextSettings = {
    text: string
    style: TextStyle
}

export type BackgroundStyle = {
    asset?: AssetUrl
    fill?: string
    useBackgroundColor?: boolean
    backgroundScaleMode?: 'cover' | 'contain' | 'pixelated' | 'pixelated-stretch' | 'pixelated-cover'
}



export type ButtonIconStyle = {
    iconName_tIcon?: string
    color?: string
    size?: number
    position?: 'left' | 'right' | 'top' | 'bottom' | 'center'
    offsetX?: number
    offsetY?: number
    isVisible?: boolean
    fill?: boolean
    strokeWidth?: number
}

export type CounterElementStyle = {
    textConfig: GameTextSettings
    asset?: AssetUrl
    fill?: string
    width?: number
    height?: number
    useBackgroundColor?: boolean
    borderRadius?: number
    offsetY?: number
    offsetX?: number
    isVisible?: boolean
    iconStyle?: ButtonIconStyle
    // Position settings
    position?: 'left' | 'center' | 'right'
    // Keep these for backward compatibility
    marginBottom?: number
    pixelated?: boolean
}

export type GameButtonStyle = {
    textConfig: GameTextSettings
    asset?: AssetUrl
    fill?: string
    width?: number
    height?: number
    useBackgroundColor?: boolean
    borderRadius?: number
    offsetY?: number
    offsetX?: number
    isVisible?: boolean
    iconStyle?: ButtonIconStyle
    backgroundScaleMode?: 'cover' | 'contain' | 'pixelated' | 'pixelated-stretch' | 'pixelated-cover'
    pixelated?: boolean
}

export type GameSoundSwitchStyle = {
    isVisible?: boolean
    offsetY?: number
    offsetX?: number
    onAsset?: AssetUrl
    offAsset?: AssetUrl
    alignment?: 'left' | 'center' | 'right'
    position?: 'left' | 'center' | 'right'
    width?: number
    height?: number
    backgroundScaleMode?: 'cover' | 'contain' | 'pixelated' | 'pixelated-stretch' | 'pixelated-cover'
}

export type RewardComponentStyle = {
    imageWidth?: number
    imageHeight?: number
    titleFontFamily?: string
    titleFontSize?: number
    titleColor?: string
    titleTextAlign?: 'left' | 'center' | 'right'
    descriptionFontFamily?: string
    descriptionFontSize?: number
    descriptionColor?: string
    descriptionTextAlign?: 'left' | 'center' | 'right'
    padding?: number
    maxWidth?: number
    layout?: 'vertical' | 'horizontal-left' | 'horizontal-right'
    imageBorderRadius?: number
    imageMargin?: number
    containerBackgroundColor?: string
    containerBorderRadius?: number
    containerShadow?: boolean
    spacing?: number
    showImage?: boolean
    showTitle?: boolean
    showDescription?: boolean
    imagePixelated?: boolean
    containerPixelated?: boolean
}

export type GameEndHandler = {
    useLives?: boolean
    livesCount?: number
}

export type GameTimerHandler = {
    useTimer?: boolean
    timerDuration?: number
    loseLifeOnTimeout?: boolean
}

export type GameTimerStyle = {
    progressColor?: string
    backgroundColor?: string
}

export type GameRewardsHandler = {
    rewardsEnabled: boolean
}