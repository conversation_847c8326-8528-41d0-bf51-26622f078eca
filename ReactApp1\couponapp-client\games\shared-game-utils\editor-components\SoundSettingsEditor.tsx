import React from 'react'
import { Button } from '@repo/shared/components/ui/button'
import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import { Slider } from '@repo/shared/components/ui/slider'
import { RefreshCw } from 'lucide-react'
import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { ReactGameConfig, defaultGameConfig } from '../../quiz-game/src/types/config'
import { SoundAssetUrl } from '@repo/shared/lib/types/widgetSettings'

// Sound Asset Picker component
interface SoundAssetPickerProps {
    label: string
    assetUrl: SoundAssetUrl
    onSelect: (assetId: string | null) => void
    onToggle: (enabled: boolean) => void
    onVolumeChange?: (volume: number) => void
    extensions?: string[]
}

function SoundAssetPicker({ label, assetUrl, onSelect, onToggle, onVolumeChange, extensions = ['mp3', 'wav', 'ogg'] }: SoundAssetPickerProps) {
    const enabled = assetUrl?.enabled !== false
    const volume = assetUrl?.volume !== undefined ? assetUrl.volume : 0.5

    return (
        <div className="space-y-3 p-1">
            <div className="flex justify-between items-center">
                <Label className="font-medium">{label}</Label>
                <div className="flex items-center gap-2">
                    <Label htmlFor={`sound-toggle-${label}`} className="text-sm text-muted-foreground cursor-pointer">
                        {enabled ? 'Enabled' : 'Disabled'}
                    </Label>
                    <Switch id={`sound-toggle-${label}`} checked={enabled} onCheckedChange={onToggle} />
                </div>
            </div>
            {enabled ? (
                <>
                    <AssetPicker onSelect={onSelect} assetUrl={assetUrl} extensions={extensions} />

                    <div className="mt-3">
                        <div className="flex justify-between items-center mb-1">
                            <Label className="text-sm">Volume</Label>
                            <span className="text-xs text-muted-foreground">{Math.round(volume * 100)}%</span>
                        </div>
                        <Slider
                            value={[volume * 100]}
                            min={0}
                            max={100}
                            step={1}
                            onValueChange={(values) => onVolumeChange && onVolumeChange(values[0] / 100)}
                        />
                    </div>
                </>
            ) : (
                <div className="p-3 bg-muted/50 rounded-md border border-dashed border-muted-foreground/40 text-center">
                    <p className="text-sm text-muted-foreground">Sound disabled. Enable to select an audio file.</p>
                </div>
            )}
        </div>
    )
}

interface SoundSettingsEditorProps {
    config: any
    onChange: (config: any) => void
}

export const SoundSettingsEditor: React.FC<SoundSettingsEditorProps> = ({ config, onChange }) => {
    // Sound handlers
    const handleSoundAssetChange = (soundKey: keyof ReactGameConfig, assetId: string | null) => {
        const currentAsset = (config[soundKey] as SoundAssetUrl) || { enabled: true }
        onChange({ [soundKey]: assetId != null ? { ...currentAsset, assetId } : null })
    }

    const handleSoundToggle = (soundKey: keyof ReactGameConfig, enabled: boolean) => {
        const currentAsset = (config[soundKey] as SoundAssetUrl) || {}
        onChange({ [soundKey]: { ...currentAsset, enabled } })
    }

    const handleSoundVolumeChange = (soundKey: keyof ReactGameConfig, volume: number) => {
        const currentAsset = (config[soundKey] as SoundAssetUrl) || { enabled: true }
        onChange({ [soundKey]: { ...currentAsset, volume } })
    }

    const handleResetAllSounds = () => {
        onChange({
            backgroundMusic: defaultGameConfig.backgroundMusic,
            winSound: defaultGameConfig.winSound,
            gameOverSound: defaultGameConfig.gameOverSound,
            correctAnswerSound: defaultGameConfig.correctAnswerSound,
            incorrectAnswerSound: defaultGameConfig.incorrectAnswerSound,
            continueToNextQuestionSound: defaultGameConfig.continueToNextQuestionSound,
        })
    }

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <div>
                    <h3 className="text-lg font-medium">Sound Settings</h3>
                    <p className="text-sm text-muted-foreground">Configure the game's sound effects and music</p>
                </div>
                <Button variant="outline" size="sm" onClick={handleResetAllSounds} className="flex items-center gap-1">
                    <RefreshCw size={14} />
                    Reset All Sounds
                </Button>
            </div>

            <div className="space-y-4">
                <SoundAssetPicker
                    label="Background Music"
                    assetUrl={(config.backgroundMusic as SoundAssetUrl) || defaultGameConfig.backgroundMusic}
                    onSelect={(assetId) => handleSoundAssetChange('backgroundMusic', assetId)}
                    onToggle={(enabled) => handleSoundToggle('backgroundMusic', enabled)}
                    onVolumeChange={(volume) => handleSoundVolumeChange('backgroundMusic', volume)}
                />

                <SoundAssetPicker
                    label="Win Sound"
                    assetUrl={(config.winSound as SoundAssetUrl) || defaultGameConfig.winSound}
                    onSelect={(assetId) => handleSoundAssetChange('winSound', assetId)}
                    onToggle={(enabled) => handleSoundToggle('winSound', enabled)}
                    onVolumeChange={(volume) => handleSoundVolumeChange('winSound', volume)}
                />

                <SoundAssetPicker
                    label="Game Over Sound"
                    assetUrl={(config.gameOverSound as SoundAssetUrl) || defaultGameConfig.gameOverSound}
                    onSelect={(assetId) => handleSoundAssetChange('gameOverSound', assetId)}
                    onToggle={(enabled) => handleSoundToggle('gameOverSound', enabled)}
                    onVolumeChange={(volume) => handleSoundVolumeChange('gameOverSound', volume)}
                />

                <SoundAssetPicker
                    label="Correct Answer Sound"
                    assetUrl={(config.correctAnswerSound as SoundAssetUrl) || defaultGameConfig.correctAnswerSound}
                    onSelect={(assetId) => handleSoundAssetChange('correctAnswerSound', assetId)}
                    onToggle={(enabled) => handleSoundToggle('correctAnswerSound', enabled)}
                    onVolumeChange={(volume) => handleSoundVolumeChange('correctAnswerSound', volume)}
                />

                <SoundAssetPicker
                    label="Incorrect Answer Sound"
                    assetUrl={(config.incorrectAnswerSound as SoundAssetUrl) || defaultGameConfig.incorrectAnswerSound}
                    onSelect={(assetId) => handleSoundAssetChange('incorrectAnswerSound', assetId)}
                    onToggle={(enabled) => handleSoundToggle('incorrectAnswerSound', enabled)}
                    onVolumeChange={(volume) => handleSoundVolumeChange('incorrectAnswerSound', volume)}
                />

                <SoundAssetPicker
                    label="Continue to Next Question Sound"
                    assetUrl={(config.continueToNextQuestionSound as SoundAssetUrl) || defaultGameConfig.continueToNextQuestionSound}
                    onSelect={(assetId) => handleSoundAssetChange('continueToNextQuestionSound', assetId)}
                    onToggle={(enabled) => handleSoundToggle('continueToNextQuestionSound', enabled)}
                    onVolumeChange={(volume) => handleSoundVolumeChange('continueToNextQuestionSound', volume)}
                />
            </div>
        </div>
    )
}
