import React, { useCallback } from 'react'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { useAtom } from 'jotai'
import { gamePreviewScreenAtomFamily, selectedEditorItem<PERSON>tom } from '@repo/shared/lib/atoms/editor-atoms'
import Runtime from './Game2048Runtime'
import { useGameRewards } from '@repo/shared/lib/game/useGameRewards'
import { Game2048Config } from './types/game2048Config'
import { getTextStyle } from '@repo/shared-game-utils/utils/gameStyleUtils'
import { GameButton } from '@repo/shared-game-utils/components/GameButton'
import { RewardComponent } from '@repo/shared-game-utils/components/RewardComponent'


// Base interface for all preview screens
export interface PreviewScreenProps {
    config: Game2048Config
    widgetId: string
    resolveAssetUrl: (id: AssetUrl) => string
    onAssetSelect?: (assetKey: string, bounds: { x: number; y: number; width: number; height: number }) => void
    onButtonClick?: () => void
}

// Lose Life Screen - Shows the message when player loses a life
export const TryAgainScreen: React.FC<PreviewScreenProps> = ({ config, widgetId, resolveAssetUrl, onAssetSelect, onButtonClick }) => {
    return (
        <div
            className="h-full w-full flex flex-col items-center justify-center rounded-xl"
            data-editor-selectable-key="loseLifeOverlay"
            style={{
                padding: '2rem',
                backgroundColor: config.loseLifeOverlay?.useBackgroundColor ? config.loseLifeOverlay?.fill || 'rgba(238, 228, 218, 0.73)' : undefined,
                backgroundImage: config.loseLifeOverlay?.asset && resolveAssetUrl ? `url(${resolveAssetUrl(config.loseLifeOverlay.asset)})` : undefined,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
            }}
            onClick={(e) => {
                e.stopPropagation()
                onAssetSelect && onAssetSelect('loseLifeOverlay', e.currentTarget.getBoundingClientRect())
            }}
        >
            <h2
                className="text-center mb-4"
                data-editor-selectable-key="loseLifeTitle"
                style={getTextStyle(config.loseLifeTitle)}
                onClick={(e) => {
                    e.stopPropagation()
                    onAssetSelect && onAssetSelect('loseLifeTitle', e.currentTarget.getBoundingClientRect())
                }}
            >
                {config.loseLifeTitle?.text || "You've lost a life!"}
            </h2>
            <GameButton
                config={config.loseLifeContinueButton}
                dataConfigKey="loseLifeContinueButton"
                onClick={(e) => {
                    e.stopPropagation()
                    if (onAssetSelect) {
                        onAssetSelect('loseLifeContinueButton', e.currentTarget.getBoundingClientRect())
                    }
                    if (onButtonClick) {
                        onButtonClick()
                    }
                }}
            />
        </div>
    )
}

// Game Over Screen - Shows when player reached the target score
export const GameOverScreen: React.FC<PreviewScreenProps> = ({ config, widgetId, resolveAssetUrl, onAssetSelect, onButtonClick }) => {
    return (
        <div
            className="h-full w-full flex flex-col items-center justify-center rounded-xl"
            data-editor-selectable-key="gameOverOverlay"
            style={{
                padding: '2rem',
                backgroundColor: config.gameOverOverlay?.useBackgroundColor ? config.gameOverOverlay?.fill || 'rgba(0, 128, 0, 0.9)' : undefined,
                backgroundImage: config.gameOverOverlay?.asset && resolveAssetUrl ? `url(${resolveAssetUrl(config.gameOverOverlay.asset)})` : undefined,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
            }}
            onClick={(e) => {
                e.stopPropagation()
                onAssetSelect && onAssetSelect('gameOverOverlay', e.currentTarget.getBoundingClientRect())
            }}
        >
            <h2
                className="text-center mb-4"
                data-editor-selectable-key="gameOverTitle"
                style={getTextStyle(config.gameOverTitle)}
                onClick={(e) => {
                    e.stopPropagation()
                    onAssetSelect && onAssetSelect('gameOverTitle', e.currentTarget.getBoundingClientRect())
                }}
            >
                {config.gameOverTitle?.text || 'Congratulations!'}
            </h2>
            <p
                className="text-center mb-4"
                data-editor-selectable-key="gameOverMessage"
                style={getTextStyle(config.gameOverMessage)}
                onClick={(e) => {
                    e.stopPropagation()
                    onAssetSelect && onAssetSelect('gameOverMessage', e.currentTarget.getBoundingClientRect())
                }}
            >
                {config.gameOverMessage?.text || `You've reached a high score!`}
            </p>
            <GameButton
                config={config.gameOverContinueButton }
                dataConfigKey="gameOverContinueButton"
                onClick={(e) => {
                    e.stopPropagation()
                    if (onAssetSelect) {
                        onAssetSelect('gameOverContinueButton', e.currentTarget.getBoundingClientRect())
                    }
                    if (onButtonClick) {
                        onButtonClick()
                    }
                }}
            />
        </div>
    )
}

// Reward Screen - Shows the reward screen when rewards are enabled
export const RewardScreen: React.FC<PreviewScreenProps> = ({ config, widgetId, resolveAssetUrl, onAssetSelect, onButtonClick }) => {
    // Import useGameRewards hook for preview mode
    const { reward } = useGameRewards({ gameWidgetId: widgetId, attempt: 0 })

    return (
        <div
            className="h-full w-full flex flex-col items-center justify-center rounded-xl"
            data-editor-selectable-key="rewardOverlay"
            style={{
                padding: '2rem',
                backgroundColor: config.rewardOverlay?.useBackgroundColor ? config.rewardOverlay?.fill || 'rgba(204, 153, 0, 0.9)' : undefined,
                backgroundImage: config.rewardOverlay?.asset && resolveAssetUrl ? `url(${resolveAssetUrl(config.rewardOverlay.asset)})` : undefined,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
            }}
            onClick={(e) => {
                e.stopPropagation()
                onAssetSelect && onAssetSelect('rewardOverlay', e.currentTarget.getBoundingClientRect())
            }}
        >
            <h2
                className="text-center mb-4"
                data-editor-selectable-key="rewardTitle"
                style={getTextStyle(config.rewardTitle)}
                onClick={(e) => {
                    e.stopPropagation()
                    onAssetSelect && onAssetSelect('rewardTitle', e.currentTarget.getBoundingClientRect())
                }}
            >
                {config.rewardTitle?.text || 'Claim Your Reward!'}
            </h2>

            <div className="mb-4" onClick={(e) => {
                e.stopPropagation()
                onAssetSelect && onAssetSelect('rewardComponent', e.currentTarget.getBoundingClientRect())
            }}>
                <RewardComponent
                    dataConfigKey="rewardComponent"
                    config={config.rewardComponent}
                    reward={reward}
                />
            </div>

            <GameButton
                config={config.rewardClaimButton }
                dataConfigKey="rewardClaimButton"
                onClick={(e) => {
                    e.stopPropagation()
                    if (onAssetSelect) {
                        onAssetSelect('rewardClaimButton', e.currentTarget.getBoundingClientRect())
                    }
                    if (onButtonClick) {
                        onButtonClick()
                    }
                }}
            />
        </div>
    )
}

// Out of Lives Screen - Shows when player has no lives left
export const OutOfLivesScreen: React.FC<PreviewScreenProps> = ({ config, widgetId, resolveAssetUrl, onAssetSelect, onButtonClick }) => {
    return (
        <div
            className="h-full w-full flex flex-col items-center justify-center rounded-xl"
            data-editor-selectable-key="outOfLivesOverlay"
            style={{
                padding: '2rem',
                backgroundColor: config.outOfLivesOverlay?.useBackgroundColor ? config.outOfLivesOverlay?.fill || '#1e1e1e' : undefined,
                backgroundImage: config.outOfLivesOverlay?.asset && resolveAssetUrl ? `url(${resolveAssetUrl(config.outOfLivesOverlay.asset)})` : undefined,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
            }}
            onClick={(e) => {
                e.stopPropagation()
                onAssetSelect && onAssetSelect('outOfLivesOverlay', e.currentTarget.getBoundingClientRect())
            }}
        >
            <h2
                className="text-center mb-4"
                data-editor-selectable-key="outOfLivesTitle"
                style={getTextStyle(config.outOfLivesTitle)}
                onClick={(e) => {
                    e.stopPropagation()
                    onAssetSelect && onAssetSelect('outOfLivesTitle', e.currentTarget.getBoundingClientRect())
                }}
            >
                {config.outOfLivesTitle?.text || "You've lost all lives!"}
            </h2>

            <GameButton
                config={config.outOfLivesContinueButton}
                dataConfigKey="outOfLivesContinueButton"
                onClick={(e) => {
                    e.stopPropagation()
                    if (onAssetSelect) {
                        onAssetSelect('outOfLivesContinueButton', e.currentTarget.getBoundingClientRect())
                    }
                    if (onButtonClick) {
                        onButtonClick()
                    }
                }}
            />
        </div>
    )
}

export interface PreviewSceneProps {
    config: Game2048Config
    widgetId: string
    resolveAssetUrl: (id: AssetUrl) => string
}

export const PreviewScene: React.FC<PreviewSceneProps> = ({ config, widgetId, resolveAssetUrl }) => {
    const [selectedScreen, setSelectedScreen] = useAtom(gamePreviewScreenAtomFamily(widgetId))
    const [editorSelection, setEditorSelection] = useAtom(selectedEditorItemAtom)

    const currentScreen = selectedScreen ?? 'previewScreen_Main'

    const handleAssetSelect = useCallback(
        (assetKey: string, bounds: { x: number; y: number; width: number; height: number }) => {
            if (!widgetId) return

            setEditorSelection({
                widgetId,
                assetKey,
                bounds: {
                    x: bounds.x,
                    y: bounds.y,
                    width: bounds.width,
                    height: bounds.height,
                    isAbsolute: true,
                },
                type: 'game-asset',
                time: Date.now(),
            })
        },
        [widgetId, setEditorSelection]
    )

    return <Runtime config={config} widgetId={widgetId} isPreview={true} resolveAssetUrl={resolveAssetUrl} currentScreenId={currentScreen as any} />
}
