import React from 'react'
import { Label } from '@repo/shared/components/ui/label'
import { Input } from '@repo/shared/components/ui/input'
import { GameEndHandler } from '@repo/shared-game-utils/types/uiStyles'

interface LivesHandlerEditorProps {
    config: any
    configKey: string
    onChange: (config: any) => void
}

export const LivesHandlerEditor: React.FC<LivesHandlerEditorProps> = ({ config, configKey, onChange }) => {
    const livesHandler = (config[configKey] || {}) as GameEndHandler
    const useLives = livesHandler.useLives ?? false
    const livesCount = livesHandler.livesCount ?? 3

    const handleLivesCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        onChange({
            [configKey]: {
                ...livesHandler,
                livesCount: Number(e.target.value),
            },
        })
    }

    if (!useLives) {
        return (
            <div className="text-sm text-muted-foreground">
                Lives system is disabled. Enable it using the toggle above to configure lives settings.
            </div>
        )
    }

    return (
        <div className="space-y-4">
            <div className="space-y-2">
                <Label htmlFor="lives-count">Number of Lives</Label>
                <Input
                    id="lives-count"
                    type="number"
                    value={livesCount}
                    onChange={handleLivesCountChange}
                    min={1}
                    max={10}
                />
            </div>
        </div>
    )
}
