import '@repo/shared/components/react-templates/widgetsImport'
import './widget-settings/widget-settings-registry'


// Import necessary components and utilities
import {ConfigTypeProvider} from '@repo/shared/lib/game/configTypeContext'

import { Button } from '@repo/shared/components/ui/button' // Adjust the import path as needed
import { Popover, PopoverContent, PopoverTrigger } from '@repo/shared/components/ui/popover' // Adjust the import path based on your project structure
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@repo/shared/components/ui/dialog'
import { cn, createShortUuid } from '@repo/shared/lib/utils' // Utility function to create unique IDs
import { PopoverClose } from '@radix-ui/react-popover'
import {
    Box,
    ChevronRight,
    CopyIcon,
    DotSquareIcon,
    ImageIcon,
    LayoutGrid,
    MoreVerticalIcon,
    Play,
    Plus,
    PlusIcon,
    Rows,
    ShareIcon,
    TrashIcon,
    Type,
    ArrowUpIcon,
    ArrowDownIcon,
    ArrowLeftIcon,
} from 'lucide-react'
import React, { createContext, createRef, PropsWithChildren, ReactNode, Suspense, useCallback, useContext, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import { atom, useAtom } from 'jotai'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { CampaignSceneType } from '@repo/shared/lib/types/campaign'
import { Label } from '@repo/shared/components/ui/label'
import { CSS } from '@dnd-kit/utilities'
import { useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { useOrganizationAssets } from '@repo/shared/lib/hooks/useCampaignAssets'
import { useCurrentOrganization } from '@repo/shared/lib/hooks/useCurrentOrganization'
// import { SettingField } from './settingsField'
import { sectionTemplates } from './sectionTemplates'
import { Updater } from 'use-immer'
import { ScrollArea } from '@repo/shared/components/ui/scroll-area'
import { Preset, PresetBlock, Widget, WidgetRenderingContext, WidgetTemplateSchema } from '@repo/shared/lib/types/editor'
import { EditorProvider, useEditor } from '@/lib/hooks/useEditor'
import { interpolate } from 'framer-motion'
import { VariablesEditor } from '@/components/editor/VariablesEditor'
import { useCampaignEditor } from '@/lib/hooks/useCampaignEditor'
import AddWidgetPopover from '@/Pages/campaign/editor/_components/addWidgetPopover'
import { getPropertyControls, PropertyControlsRegistry } from '../property-controls'
import { getWidgetMetadata, WidgetMetadataRegistry } from '@repo/shared/lib/widget-metadata'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { CursorProps, NodeApi, Tree } from 'react-arborist'
import { GripVertical } from 'lucide-react'
import { Input } from '@repo/shared/components/ui/input'
import { Separator } from '@repo/shared/components/ui/separator'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@repo/shared/components/ui/accordion'
import { getGameModule, loadGameModule } from '@repo/shared/lib/game/gameRegistry'
import { GameWidgetSettings } from '@repo/shared/components/react-templates/blocks/game'
import { GameAssetSelection } from '@repo/shared/lib/types/editorSelection'

// Import the atom family for storing game preview screen selection per widget
import { gamePreviewScreenAtomFamily, selectedEditorItemAtom } from '@repo/shared/lib/atoms/editor-atoms'
import { GameModule, PreviewScreenDefinition } from '@repo/shared/lib/game/game'
import { useWidgetSettings } from '@/lib/hooks/useWidgetSettings'

interface Template {
    id: string
    name: string
    template: string
    presets: Preset[]
    mainGroup: 'containers' | 'basic_elements'
}

interface GroupedTemplate {
    name: string
    templates: Template[]
}

interface AddWidgetPopoverProps {
    parentId: string
}

const syncRootWidgetWithNewItems = (root: Widget, newItems: TreeItemWidget[]): Widget => {
    // Create a map of all widgets in the current tree
    const widgetMap: Record<string, Widget> = {}
    const buildWidgetMap = (widget: Widget) => {
        widgetMap[widget.id] = widget
        widget.children.forEach(buildWidgetMap)
    }
    buildWidgetMap(root)

    // Recursive function to build the new tree structure
    const buildNewChildren = (items: TreeItemWidget[]): Widget[] => {
        return items
            .map((item) => {
                const correspondingWidget = widgetMap[item.id]
                if (!correspondingWidget) {
                    console.warn(`Widget with id ${item.id} not found in rootWidget.`)
                    return null
                }

                return {
                    ...correspondingWidget,
                    children: buildNewChildren(item.children),
                }
            })
            .filter((widget): widget is Widget => widget !== null)
    }

    // Update the root widget with the new structure
    return {
        ...root,
        children: buildNewChildren(newItems),
    }
}

// Helper function to find all parent IDs of a widget in the tree
const findParentIds = (items: TreeItemWidget[], targetId: string, currentPath: string[] = []): string[] => {
    for (const item of items) {
        if (item.id === targetId) {
            return currentPath
        }
        if (item.children.length > 0) {
            const found = findParentIds(item.children, targetId, [...currentPath, item.id])
            if (found.length > 0) {
                return found
            }
        }
    }
    return []
}

// Define the shape of our context
interface SceneEditorContextType {
    selectedWidget: string | null
    setSelectedWidget: (widget: string | null) => void
    currentSceneId: string
}

// Create a context for the SceneEditor with a default value
const SceneEditorContext = createContext<SceneEditorContextType>({
    currentSceneId: null,
    selectedWidget: null,
    setSelectedWidget: () => {},
})

export const useSceneEditor = () => {
    const context = useContext(SceneEditorContext)
    if (!context) {
        throw new Error('useSceneEditor must be used within an SceneEditor')
    }
    return context
}

// Props type for the main SceneEditor component
interface SceneEditorProps {
    rootWidget?: Widget
    setRootWidget?: (rootWidget: Widget) => void
    currentSceneId: string
    children: React.ReactNode
}

// Main SceneEditor component
const SceneEditor: React.FC<SceneEditorProps> & {
    EditorView: React.FC
    WidgetTree: React.FC
    WidgetSettings: React.FC
} = ({ children, currentSceneId }) => {
    const [selectedWidget, setSelectedWidget] = useState<string | null>(null)

    const value: SceneEditorContextType = {
        currentSceneId,
        selectedWidget,
        setSelectedWidget,
    }

    return <SceneEditorContext.Provider value={value}>{children}</SceneEditorContext.Provider>
}

interface TreeItemWidget {
    id: string
    name: string
    children: TreeItemWidget[]
}

SceneEditor.WidgetTree = () => {
    const { rootWidget, setRootWidget, findWidgetById, editorSelection } = useEditor()
    const [treeItems, setTreeItems] = useState<TreeItemWidget[]>([])

    const mapWidgetToTreeItem = useCallback((widget: Widget, previousItems: TreeItemWidget[]): TreeItemWidget => {
        const previousTreeItem = previousItems.find((t) => t.id === widget.id)
        return {
            id: widget.id,
            name: widget.componentName,
            children: widget.children.map((o) => mapWidgetToTreeItem(o, previousItems)),
        }
    }, [])

    useEffect(() => {
        if (!rootWidget?.children) {
            return
        }
        const mappedItems = rootWidget.children.map((o) =>
            mapWidgetToTreeItem(
                o,
                treeItems.flatMap((t) => [t, ...t.children])
            )
        )
        setTreeItems(mappedItems)
    }, [rootWidget, rootWidget?.children, mapWidgetToTreeItem])

    const updateRootWidgetStructure = (newTreeItems: TreeItemWidget[]) => {
        const updatedRoot = syncRootWidgetWithNewItems(rootWidget, newTreeItems)
        setRootWidget(updatedRoot)
    }

    const handleMove = ({ dragIds, parentId, index }) => {
        setTreeItems((items) => {
            const newItems = [...items]
            const draggedItems = dragIds.map((id) => findTreeItemById(items, id)).filter(Boolean)

            // Remove items from their current position
            draggedItems.forEach((item) => {
                const { parentItems, index } = findItemAndParent(newItems, item.id)
                if (parentItems && index !== -1) {
                    parentItems.splice(index, 1)
                }
            })

            // Add items to their new position
            if (parentId === null) {
                // Add to root level
                newItems.splice(index, 0, ...draggedItems)
            } else {
                // Add to parent
                const parent = findTreeItemById(newItems, parentId)
                if (parent) {
                    parent.children.splice(index, 0, ...draggedItems)
                }
            }

            updateRootWidgetStructure(newItems)
            return newItems
        })
    }

    const findItemAndParent = (items: TreeItemWidget[], id: string): { parentItems: TreeItemWidget[] | null; index: number } => {
        // Check root level
        const rootIndex = items.findIndex((item) => item.id === id)
        if (rootIndex !== -1) {
            return { parentItems: items, index: rootIndex }
        }

        // Check nested levels
        for (const item of items) {
            const childIndex = item.children.findIndex((child) => child.id === id)
            if (childIndex !== -1) {
                return { parentItems: item.children, index: childIndex }
            }
            const result = findItemAndParent(item.children, id)
            if (result.parentItems) {
                return result
            }
        }

        return { parentItems: null, index: -1 }
    }

    const findTreeItemById = (items: TreeItemWidget[], id: string): TreeItemWidget | null => {
        for (const item of items) {
            if (item.id === id) return item
            if (item.children.length > 0) {
                const found = findTreeItemById(item.children, id)
                if (found) return found
            }
        }
        return null
    }

    function TreeNode({ node, style, dragHandle }: { node: NodeApi<TreeItemWidget>; style: any; dragHandle: any }) {
        const indent = node.level * 20
        const { duplicateWidget, deleteWidget, findWidgetById, rootWidget, selectWidget } = useEditor()
        const [isExportOpen, setIsExportOpen] = useState(false)
        const [exportData, setExportData] = useState('')

        const widget = findWidgetById(rootWidget, node?.data?.id)
        const widgetMetadata = getWidgetMetadata(widget?.componentName)
        const nodeName = widgetMetadata?.displayName

        const handleExport = (widget: Widget) => {
            setExportData(JSON.stringify(widget, null, 2))
            setIsExportOpen(true)
        }

        const exportDialog = (
            <Dialog open={isExportOpen} onOpenChange={setIsExportOpen}>
                <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                        <DialogTitle>Export Widget</DialogTitle>
                    </DialogHeader>
                    <div className="mt-4">
                        <div className="relative">
                            <pre className="p-4 rounded-lg bg-muted overflow-auto max-h-[400px]">
                                <code>{exportData}</code>
                            </pre>
                            <Button
                                variant="outline"
                                size="icon"
                                className="absolute top-2 right-4"
                                onClick={() => {
                                    navigator.clipboard.writeText(exportData)
                                }}
                            >
                                <CopyIcon className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        )

        const selectNode = () => {
            node.toggle()
            console.log('Select widget 1')
            selectWidget(findWidgetById(rootWidget, node.data.id))
        }

        useEffect(() => {
            if (editorSelection?.widgetId && findParentIds(treeItems, editorSelection?.widgetId).includes(node.data.id) && !node.isOpen) {
                node.toggle()
            }
        }, [editorSelection])

        return (
            <>
                <div
                    ref={dragHandle}
                    style={{
                        ...style,
                        paddingLeft: `${indent}px`,
                        display: 'flex',
                        alignItems: 'center',
                        cursor: 'pointer',
                    }}
                    className="py-1 px-2 rounded-sm"
                >
                    <div className={`flex flex-grow rounded-sm items-stretch hover:bg-accent ${editorSelection?.widgetId === node.data.id ? 'bg-accent' : ''}`}>
                        <div className="flex items-center gap-2 flex-1  ps-2 py-2 text-sm " onClick={selectNode}>
                            {node.data.children?.length == 0 ? (
                                <div className="" />
                            ) : (
                                <ChevronRight
                                    className={cn('w-4 h-4 transition-transform', {
                                        'transform rotate-90': node.isOpen,
                                    })}
                                />
                            )}
                            <span>{nodeName}</span>
                        </div>
                        {/* Actions */}
                        <div className="flex gap-2">
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button variant="ghost" size="icon" className="justify-center items-center">
                                        <MoreVerticalIcon className="h-4 w-4" />
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-full px-1 py-1">
                                    <PopoverClose className="flex flex-col">
                                        <Button
                                            variant="ghost"
                                            className="w-full text-left"
                                            onClick={(e) => {
                                                e.preventDefault()
                                                duplicateWidget(node.data.id)
                                            }}
                                        >
                                            Duplicate
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            className="w-full text-left"
                                            onClick={(e) => {
                                                e.preventDefault()
                                                handleExport(findWidgetById(rootWidget, node.data.id))
                                            }}
                                        >
                                            Export
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            className="w-full text-left text-destructive"
                                            onClick={(e) => {
                                                e.preventDefault()
                                                deleteWidget(node.data.id)
                                            }}
                                        >
                                            Delete
                                        </Button>
                                    </PopoverClose>
                                </PopoverContent>
                            </Popover>
                        </div>
                    </div>
                </div>
                {exportDialog}
            </>
        )
    }

    const DragCursor = (props: CursorProps) => {
        return (
            <div
                className="h-[2px] bg-primary/50 w-full"
                style={{
                    position: 'absolute',
                    top: `${props.top}px`,
                    left: `${props.left}px`,
                    width: `100px`,
                }}
            ></div>
        )
    }

    return (
        <div className="space-y-2">
            <div className="widget-tree mt-4">
                <div className="flex flex-grow items-center justify-between border-b px-3 pb-2 mb-2">
                    <div className="font-bold text-sm">Widget tree</div>
                    <AddWidgetPopover parentId={null} />
                </div>
            </div>
            <Tree
                data={treeItems}
                openByDefault={false}
                width={'100'}
                onMove={handleMove}
                rowHeight={42}
                padding={8}
                renderCursor={DragCursor}
                disableDrop={(props) => {
                    if (props.parentNode.isRoot) return false

                    const widget = findWidgetById(rootWidget, '' + props.parentNode.data.id)
                    const metadata = getWidgetMetadata(widget?.componentName)
                    console.log('To widget: ', props.parentNode.data.id)
                    return metadata?.type !== 'container'
                }}
            >
                {/*@ts-ignore*/}
                {TreeNode}
            </Tree>
        </div>
    )
}

const SceneTypeSelector: React.FC<{
    value?: CampaignSceneType
    onChange: (value: CampaignSceneType) => void
}> = ({ value, onChange }) => {
    return (
        <div className="space-y-4">
            <Label>Page Type</Label>
            <Select value={value} onValueChange={onChange}>
                <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select page type..." />
                </SelectTrigger>
                <SelectContent>
                    {Object.values(CampaignSceneType).map((type) => (
                        <SelectItem key={type} value={type}>
                            {type
                                .split('_')
                                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                                .join(' ')}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    )
}

SceneEditor.WidgetSettings = () => {
    const { editorSelection, findWidgetById, rootWidget, selectWidget } = useEditor()
    const { scenes, setScenes } = useCampaignEditor()
    const { currentSceneId } = useSceneEditor()
    const [expandedSections, setExpandedSections] = useState<string[]>([])
    const [currentSubPage, setCurrentSubPage] = useState<{ section: string; page: string } | null>(null)

    const selectedWidget = useMemo(() => findWidgetById(rootWidget, editorSelection?.widgetId), [editorSelection, rootWidget])
    const propertyControlsMetadata = useMemo(() => getPropertyControls(selectedWidget?.componentName), [selectedWidget])

    useEffect(() => {
        console.log('Metadata changed')
        if (propertyControlsMetadata?.controls) {
            setExpandedSections(Object.keys(propertyControlsMetadata.controls))
        }
    }, [propertyControlsMetadata])

    const handleSceneTypeChange = (type: CampaignSceneType) => {
        setScenes((draft) => {
            const currentSceneIndex = draft.findIndex((scene) => scene.id === currentSceneId)
            if (currentSceneIndex > -1) {
                draft[currentSceneIndex].type = type
            }
        })
    }

    if (editorSelection?.type == 'game-asset') {
        return (
            <div>
                <GameAssetSettingsSidebar />
            </div>
        )
    }

    if (!editorSelection) {
        return (
            <div>
                <p className="text-secondary text-sm text-center">Select a widget to edit its properties.</p>
            </div>
        )
    }

    console.log('rerender widget setrtings')
    return (
        <div>
            {editorSelection?.widgetId === rootWidget?.id && (
                <>
                    <SceneTypeSelector value={scenes.find((scene) => scene.id === currentSceneId)?.type} onChange={handleSceneTypeChange} />
                    <div className="h-4" />
                    <VariablesEditor />
                </>
            )}

            {propertyControlsMetadata?.controls && (
                <Accordion type="multiple" value={expandedSections} onValueChange={setExpandedSections} className="">
                    {Object.entries(propertyControlsMetadata.controls).map(([sectionKey, section]) => (
                        <AccordionItem key={sectionKey} value={sectionKey}>
                            <AccordionTrigger className="text-md font-bold">{section.name}</AccordionTrigger>
                            <AccordionContent>
                                <div className="space-y-4 pt-2">{section.controls?.map((Control, index) => <Control key={`${sectionKey}-${index}`} widget={selectedWidget} />)}</div>
                            </AccordionContent>
                        </AccordionItem>
                    ))}
                </Accordion>
            )}
        </div>
    )
}

const GameAssetSettingsSidebar = () => {
    // Added setSelectedGameAsset from useEditor
    const { editorSelection, updateWidgetSettings, findWidgetById, rootWidget, setSelectedGameAsset, selectWidget } = useEditor()
    const widget = editorSelection?.widgetId ? findWidgetById(rootWidget, editorSelection?.widgetId) : null
    const assetSelection = editorSelection as GameAssetSelection

    const { settings } = useWidgetSettings<GameWidgetSettings>(widget)
    const gameModule = useMemo(() => getGameModule(settings?.gameId), [settings?.gameId])
    const SingleConfigEditComponent = useMemo(() => gameModule?.configKeyEditor, [gameModule])

    const updateConfig = (config: any) => {
        updateWidgetSettings(widget.id, 'gameConfig', 'desktop', config)
    }

    if (!settings) {
        return <>No settings found.</>
    }

    return (
        <div className="space-y-4">
            <Button
                variant="ghost"
                className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
                onClick={() => {
                    console.log('Select widget 3')
                    selectWidget(widget)
                }}
            >
                <ArrowLeftIcon className="h-4 w-4" />
                Back to Game Settings
            </Button>
            <div className="space-y-2">
                {SingleConfigEditComponent && (
                    <Suspense>
                       <ConfigTypeProvider configType={gameModule.configType}>
                            <SingleConfigEditComponent configKey={assetSelection.assetKey} config={settings.gameConfig} updateConfig={updateConfig} />
                        </ConfigTypeProvider>
                    </Suspense>
                )}
            </div>
        </div>
    )
}

const SelectedGameWidgetScreenPicker = () => {
    const { rootWidget, editorSelection, findWidgetById, selectWidget } = useEditor()
    const selectedWidget = findWidgetById(rootWidget, editorSelection?.widgetId)
    const [selectedScreen, setSelectedScreen] = useAtom(gamePreviewScreenAtomFamily(editorSelection?.widgetId))

    const { settings } = useWidgetSettings<GameWidgetSettings>(selectedWidget)
    const gameModule = useMemo(() => getGameModule(settings?.gameId), [settings?.gameId])

    const handlePreviewSceneChanged = (screenId: string) => {
        if (!screenId) {
            return
        }
        //@ts-ignore Check why is this an error.
        setSelectedScreen(screenId)

        if (editorSelection?.type == 'game-asset') {
            selectWidget(selectedWidget)
        }
    }

    if (!gameModule?.previewScreens || !Array.isArray(gameModule.previewScreens)) {
        return null
    }

    console.log('Selected: ', selectedScreen)
    console.log('game config now', settings.gameConfig)

    return (
        <Select value={selectedScreen || gameModule.previewScreens[0]?.screenId} onValueChange={handlePreviewSceneChanged}>
            <SelectTrigger className="w-[220px]">
                <SelectValue placeholder="Select preview..." />
            </SelectTrigger>
            <SelectContent>
                {gameModule.previewScreens
                    ?.filter((screen) => settings.gameConfig != null && (screen.visibleCheck == null || screen.visibleCheck(settings.gameConfig)))
                    .map((screen) => (
                        <SelectItem key={screen.screenId} value={screen.screenId}>
                            {screen.displayName}
                        </SelectItem>
                    ))}
            </SelectContent>
        </Select>
    )
}

const Toolbox = () => {
    const { editorSelection, duplicateWidget, deleteWidget } = useEditor()

    const selectedWidgetBoundingBox = useMemo(() => {
        console.log('editorSelection.widgetId: ', editorSelection?.widgetId)
        const selectedElement = document.querySelector(`[data-widget-id="${editorSelection?.widgetId}"]`)
        if (selectedElement) {
            console.log('Rect get')
            //@ts-ignore getBoundingClientRect exists.
            const rect = selectedElement?.childNodes[0]?.getBoundingClientRect()
            console.log('Rect:', rect)
            return rect
        }
        return null
    }, [editorSelection])

    const handleDuplicate = () => {
        duplicateWidget(editorSelection?.widgetId)
    }
    const handleDelete = () => {
        deleteWidget(editorSelection?.widgetId)
    }

    console.log('selectedWidgetBoundingBox: ', selectedWidgetBoundingBox)

    if (!selectedWidgetBoundingBox) {
        return <></>
    }

    return (
        <div
            className={`absolute flex items-center gap-4 bg-black text-sm text-white rounded-lg  h-[50px] px-4`}
            style={{ top: selectedWidgetBoundingBox?.top - 54, left: selectedWidgetBoundingBox?.left }}
        >
            <CopyIcon onClick={handleDuplicate} className="h-4 w-4 cursor-pointer" />
            <TrashIcon onClick={handleDelete} className="h-4 w-4 cursor-pointer" />
            <SelectedGameWidgetScreenPicker />
        </div>
    )
}

const WidgetOverlays = ({ selectedWidgetId, hoveredWidget }: { selectedWidgetId: string; hoveredWidget: Widget }) => {
    const selectedRef = useRef<HTMLDivElement>(null)
    const hoverRef = useRef<HTMLDivElement>(null)
    const gameAssetRef = useRef<HTMLDivElement>(null)
    const { deleteWidget, duplicateWidget, rootWidget, editorSelection, findWidgetById } = useEditor()

    const assetSelection = editorSelection?.type == 'game-asset' ? (editorSelection as GameAssetSelection) : null

    const updateOverlay = useCallback(
        (ref: React.RefObject<HTMLDivElement>, widgetId: string | null) => {
            if (widgetId && ref.current && widgetId !== rootWidget?.id) {
                const element = document.querySelector(`[data-widget-id="${widgetId}"]`)?.childNodes[0] as HTMLElement
                if (element) {
                    const rect = element.getBoundingClientRect()
                    const top = rect.top
                    const left = rect.left

                    ref.current.style.top = `${top}px`
                    ref.current.style.left = `${left}px`
                    ref.current.style.width = `${rect.width}px`
                    ref.current.style.height = `${rect.height}px`
                    ref.current.style.display = 'block'

                    //@ts-ignore setAttribute does exist there.
                    ref.current.childNodes[0]?.setAttribute('style', 'position: absolute;')
                }
            } else if (ref.current) {
                ref.current.style.display = 'none'
            }
        },
        [rootWidget]
    )

    const updateGameAssetOverlay = useCallback(() => {
        if (editorSelection?.type !== 'game-asset') {
            return
        }

        if (assetSelection && assetSelection.bounds && gameAssetRef.current) {
            // const editorContainer = document.querySelector('[data-editor-container]')
            // const editorRect = editorContainer?.getBoundingClientRect() || { top: 0, left: 0 }
            const { x, y, width, height } = assetSelection.bounds

            const gameWidget = document.querySelector(`[data-game-widget-id="${assetSelection.widgetId}"]`) as HTMLElement
            if (gameWidget) {
                if (assetSelection.bounds.isAbsolute) {
                    const gameRect = gameWidget.getBoundingClientRect()
                    // Ensure selection stays within game rect bounds
                    const maxX = Math.min(x + width, gameRect.right)
                    const maxY = Math.min(y + height, gameRect.bottom)
                    const minX = Math.max(x, gameRect.left)
                    const minY = Math.max(y, gameRect.top)

                    gameAssetRef.current.style.left = `${minX}px`
                    gameAssetRef.current.style.top = `${minY}px`
                    gameAssetRef.current.style.width = `${maxX - minX}px`
                    gameAssetRef.current.style.height = `${maxY - minY}px`
                    gameAssetRef.current.style.display = 'block'
                } else {
                    const gameRect = gameWidget.getBoundingClientRect()
                    const clampedX = x < 0 ? 0 : x
                    const cutLeft = clampedX - x
                    const overflowRight = x + width > gameRect.width ? x + width - gameRect.width : 0
                    const newWidth = Math.max(0, width - cutLeft - overflowRight)

                    // Clamp y coordinate and adjust height
                    const clampedY = y < 0 ? 0 : y
                    const cutTop = clampedY - y
                    const overflowBottom = y + height > gameRect.height ? y + height - gameRect.height : 0
                    const newHeight = Math.max(0, height - cutTop - overflowBottom)
                    // Calculate final position relative to editor container
                    const finalLeft = gameRect.left + clampedX
                    const finalTop = gameRect.top + clampedY
                    // Apply the overlay positioning and sizing
                    gameAssetRef.current.style.left = `${finalLeft}px`
                    gameAssetRef.current.style.top = `${finalTop}px`
                    gameAssetRef.current.style.width = `${newWidth}px`
                    gameAssetRef.current.style.height = `${newHeight}px`
                    gameAssetRef.current.style.display = 'block'
                }
            }
        } else if (gameAssetRef.current) {
            gameAssetRef.current.style.display = 'none'
        }
    }, [assetSelection])

    const updateAllOverlays = useCallback(() => {
        console.log('Updating overlays.')
        updateOverlay(selectedRef, selectedWidgetId)
        updateOverlay(hoverRef, hoveredWidget?.id || null)
        updateGameAssetOverlay()
    }, [updateOverlay, selectedWidgetId, hoveredWidget, updateGameAssetOverlay])

    useLayoutEffect(() => {
        updateAllOverlays()
    }, [updateAllOverlays])

    useEffect(() => {
        const handleScroll = () => {
            requestAnimationFrame(updateAllOverlays)
        }

        window.addEventListener('scroll', handleScroll, true)
        window.addEventListener('resize', updateAllOverlays)

        return () => {
            window.removeEventListener('scroll', handleScroll, true)
            window.removeEventListener('resize', updateAllOverlays)
        }
    }, [updateAllOverlays])

    return (
        <>
            <div
                ref={selectedRef}
                style={{
                    position: 'absolute',
                    border: '1px dashed black',
                    zIndex: 3,
                }}
            ></div>

            {hoveredWidget?.id !== selectedWidgetId && (
                <div
                    ref={hoverRef}
                    style={{
                        position: 'absolute',
                        border: '1px solid black',
                        pointerEvents: 'none',
                        zIndex: 2,
                        display: 'none',
                    }}
                >
                    <span className={'bg-black px-3 text-sm rounded-lg mt-[-34px] py-[4px] text-white'}>{hoveredWidget?.componentName}</span>
                </div>
            )}
            <div
                ref={gameAssetRef}
                style={{
                    position: 'absolute',
                    border: '1px solid #3b82f6',
                    zIndex: 3,
                    display: 'none',
                    pointerEvents: 'none',
                }}
            ></div>
        </>
    )
}

// Modify SceneEditor.EditorView to include the type selector
SceneEditor.EditorView = () => {
    const { rootWidget, setRootWidget, editorSelection, selectWidget } = useEditor()
    const { currentOrganization } = useCurrentOrganization()
    const { assetsData, isLoadingAssets, assetsError } = useOrganizationAssets(currentOrganization?.id)

    const { viewMode } = useCampaignEditor()

    const [hoveredWidget, setHoveredWidget] = useState<Widget | null>(null)

    const resolveAssetUrl = useMemo(
        () => (id: AssetUrl) => {
            return id?.assetId ? assetsData?.assets.find((a) => a.id === id?.assetId)?.fileUrl : id?.absoluteUrl
        },
        [assetsData]
    )

    const handleMouseMove = (widget: Widget) => {
        setHoveredWidget(widget)
    }

    const handleMouseLeave = () => {
        setHoveredWidget(null)
    }

    const handleClick = (widget: Widget) => {
        console.log('Select widget 4')
        selectWidget(widget)
    }

    const handlePageClick = (e: React.MouseEvent) => {
        // Only handle clicks directly on the container, not on widgets
        if (e.target === e.currentTarget) {
            if (rootWidget) {
                selectWidget(rootWidget)
            }
        }
    }

    useEffect(() => {
        if (!editorSelection && rootWidget) {
            console.log('Select widget 5')
            selectWidget(rootWidget)
        }
    }, [rootWidget, editorSelection, selectWidget])

    if (!rootWidget) return null

    const renderWidget = (widget: Widget): ReactNode => {
        if (!widget || !widget.children) {
            return null
        }

        const widgetMeta = WidgetMetadataRegistry[widget.componentName]
        if (!widgetMeta) {
            return (
                <span className="text-destructive">
                    Widget template not found: {widget?.componentName} {JSON.stringify(WidgetMetadataRegistry)}
                </span>
            )
        }

        const WidgetComponent = widgetMeta.componentType

        const context: WidgetRenderingContext = {
            widgetId: widget.id,
            sceneId: 'just_preview',
            isEditorMode: true,
            isSelectedInEditor: editorSelection?.widgetId === widget.id,
            useEditorContext: useEditor(),
            breakpoint: viewMode,
        }

        const renderedChildren = widget.children.map((child) => renderWidget(child))
        const unwrappedSettings = widget.settings // unwrapResponsiveValues(widget.settings, viewMode)

        return (
            <div
                key={widget.id}
                data-widget-id={widget.id}
                onMouseLeave={handleMouseLeave}
                onMouseMove={(e) => {
                    e.stopPropagation()
                    handleMouseMove(widget)
                }}
                onClick={(e) => {
                    console.log('click: ' + widget.id)
                    e.stopPropagation() // Prevent event bubbling
                    e.preventDefault()
                    handleClick(widget)
                }}
                style={{ display: 'contents' }}
            >
                <WidgetComponent data-widget-id={widget.id} settings={unwrappedSettings} {...context} resolveAssetUrl={resolveAssetUrl}>
                    {renderedChildren}
                </WidgetComponent>
            </div>
        )
    }

    if (isLoadingAssets) {
        return <>Loading...</>
    }

    return (
        <>
            <div className="flex flex-grow parent-editor">
                <div className="flex flex-grow" onClick={handlePageClick} data-editor-container>
                    {renderWidget(rootWidget)}
                </div>
            </div>

            <div className="fixed overflow-clip top-0 bottom-0 left-0 right-0 pointer-events-none widget-overlays" style={{ margin: 0 }}>
                <WidgetOverlays key={Math.random()} selectedWidgetId={editorSelection?.widgetId || ''} hoveredWidget={hoveredWidget || ({} as Widget)} />
            </div>
            <div style={{ margin: 0 }}>
                <Toolbox />
            </div>
        </>
    )
}

export default SceneEditor
