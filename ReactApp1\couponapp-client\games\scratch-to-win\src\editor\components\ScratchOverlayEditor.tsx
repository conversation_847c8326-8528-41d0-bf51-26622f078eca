import { useConfigKeyDefinition } from '@repo/shared-game-utils/hooks/useConfigType'
import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { Label } from '@repo/shared/components/ui/label'
import { Slider } from '@repo/shared/components/ui/slider'

interface ScratchOverlayEditorProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
}

export function ScratchOverlayEditor({ configKey, config, onChange }: ScratchOverlayEditorProps) {
    const definition = useConfigKeyDefinition(configKey)

    return (
        <div className="space-y-4">
            <div>
                <AssetPicker
                    exactSize={definition?.width && definition?.height ? { width: definition.width, height: definition.height } : undefined}
                    onSelect={(assetId) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                asset: { assetId },
                            },
                        })
                    }
                    assetUrl={(config[configKey] as any)?.asset}
                    extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']}
                />
            </div>

            <div>
                <Label className="mb-2 block">Border Radius</Label>
                <div className="flex justify-between items-center mb-2">
                    <span className="text-sm">{(config[configKey] as any)?.borderRadius || 0}px</span>
                </div>
                <Slider
                    value={[(config[configKey] as any)?.borderRadius || 0]}
                    min={0}
                    max={50}
                    step={1}
                    onValueChange={(values) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                borderRadius: values[0],
                            },
                        })
                    }
                />
            </div>
        </div>
    )
} 