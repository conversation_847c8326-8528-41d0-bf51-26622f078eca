import { useCallback,  createContext, useContext } from 'react'
import { useAtom } from 'jotai'
import { selectedEditorItemAtom } from '@repo/shared/lib/atoms/editor-atoms'
import { mergeConfigWithFallback } from '../utils/configUtils'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { GameConfig } from '@repo/shared/lib/game/gameConfig'

export type GameRuntimeProps = {
    currentScreenId?: any
    setCurrentScreenId: (screenId: any) => void
    
    initialGameScreenChecked: React.MutableRefObject<boolean>

    config: GameConfig;
    defaultConfig: GameConfig;
    
    widgetId: string;
    inputEnabled?: boolean;
    isPreview: boolean;
    resolveAssetUrl: (id: AssetUrl) => string | undefined;
    
}


export const GameContext = createContext<GameRuntimeProps | null>(null)

export function useGame<T extends GameConfig>() {
    const context = useContext(GameContext)

    if (!context) {
        throw new Error('useGame must be used within a GameProvider')
    }

    const { widgetId,  isPreview = false, config: rawConfig, defaultConfig} = context
    const config = mergeConfigWithFallback(rawConfig, defaultConfig) as T

    const [editorSelection, setEditorSelection] = useAtom(selectedEditorItemAtom)

    // Function to handle asset selection in preview mode
    const onGameAssetSelect = useCallback(
        (assetKey: string, bounds: { x: number; y: number; width: number; height: number }) => {
            if (!isPreview || !widgetId) {
                return
            }

            setEditorSelection({
                widgetId,
                assetKey,
                bounds: {
                    x: bounds.x,
                    y: bounds.y,
                    width: bounds.width,
                    height: bounds.height,
                    isAbsolute: true,
                },
                type: 'game-asset',
                time: Date.now(),
            })
        },
        [isPreview, widgetId, setEditorSelection]
    )

    return {
        ...context,
        config,
        onGameAssetSelect,
    }
}
