import { AssetPicker } from '@repo/shared/components/editor/assetsManager'

interface AudioEditorProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
}

export function AudioEditor({ configKey, config, onChange }: AudioEditorProps) {
    return (
        <AssetPicker 
            onSelect={(assetId) => onChange({ [configKey]: { assetId } })} 
            assetUrl={config[configKey] as any} 
            extensions={['mp3', 'wav', 'ogg']} 
        />
    )
}
