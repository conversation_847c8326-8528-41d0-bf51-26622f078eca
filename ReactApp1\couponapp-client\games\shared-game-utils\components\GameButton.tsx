import { useGame } from '../hooks/useGame'
import { ButtonIconStyle, GameButtonStyle } from '../types/uiStyles'
import { getTextAlignmentStyles, getButtonStyle } from '../utils/gameStyleUtils'
import { GameText } from './GameText'
import { Icon } from '@repo/shared/components/ui/icon-picker'

// GameButtonIcon component for handling button icons
interface GameButtonIconProps {
    iconStyle?: ButtonIconStyle
}

export const GameButtonIcon: React.FC<GameButtonIconProps> = ({ iconStyle }) => {
    if (!iconStyle || !iconStyle.iconName_tIcon || iconStyle.isVisible === false) {
        return null
    }

    const iconSize = iconStyle.size || 24
    const iconColor = iconStyle.color || '#000000'
    const offsetX = iconStyle.offsetX || 0
    const offsetY = iconStyle.offsetY || 0
    const fill = iconStyle.fill ? iconColor : '#00000000'
    const strokeWidth = iconStyle.strokeWidth || undefined

    return (
        <div style={{ transform: `translate(${offsetX}px, ${offsetY}px)` }}>
            <Icon name={iconStyle.iconName_tIcon as any} size={iconSize} color={iconColor} fill={fill} strokeWidth={strokeWidth} />
        </div>
    )
}

// GameButton component for consistent button styling
interface GameButtonProps {
    config: GameButtonStyle
    onClick: (e: React.MouseEvent) => void
    dataConfigKey: string
}

export const GameButton: React.FC<GameButtonProps> = ({ config, onClick, dataConfigKey }) => {
    // Extract text style properties
    const { isPreview } = useGame()

    const textStyle = config?.textConfig?.style || {}
    const textAlign = textStyle.textAlign || 'center'
    const alignmentStyles = getTextAlignmentStyles(textAlign)
    const iconStyle = config?.iconStyle

    if (!isPreview && config.isVisible == false) {
        return null
    }

    const getIconPosition = () => {
        if (!iconStyle || !iconStyle.iconName_tIcon || iconStyle.isVisible === false) {
            return { flexDirection: 'row' as const }
        }

        const position = iconStyle?.position || 'left'
        switch (position) {
            case 'left':
                return { flexDirection: 'row' as const }
            case 'right':
                return { flexDirection: 'row-reverse' as const }
            case 'top':
                return { flexDirection: 'column' as const }
            case 'bottom':
                return { flexDirection: 'column-reverse' as const }
            case 'center':
                return { flexDirection: 'row' as const, position: 'relative' as const }
            default:
                return { flexDirection: 'row' as const }
        }
    }

    // Determine if we should display the icon
    const hasIcon = iconStyle && iconStyle.iconName_tIcon && iconStyle.isVisible !== false

    return (
        <button
            data-editor-selectable-key={dataConfigKey}
            style={{
                ...getButtonStyle(config),
                display: 'flex',
                ...alignmentStyles,
                opacity: isPreview && config.isVisible == false ? 0.5 : 1,
                ...getIconPosition(),
                alignItems: 'center',
                justifyContent: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',
                gap: iconStyle?.position === 'center' ? 0 : '8px', // Space between icon and text, except for center position
            }}
            onClick={onClick}
        >
            {isPreview && config.isVisible == false && <div>[hidden]</div>}

            {config.isVisible !== false && (
                <>
                    {hasIcon && iconStyle?.position !== 'center' && <GameButtonIcon iconStyle={iconStyle} />}
                    <div
                        style={{
                            width: !hasIcon || iconStyle?.position === 'center' ? '100%' : 'auto',
                            textAlign: textAlign as 'left' | 'center' | 'right',
                        }}
                    >
                        <GameText config={config?.textConfig} dataConfigKey={null} />
                    </div>
                    {hasIcon && iconStyle?.position === 'center' && (
                        <div
                            style={{
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: `translate(-50%, -50%) translate(${iconStyle?.offsetX || 0}px, ${iconStyle?.offsetY || 0}px)`,
                            }}
                        >
                            <GameButtonIcon iconStyle={iconStyle} />
                        </div>
                    )}
                </>
            )}
        </button>
    )
}
