import { AssetUrl, SoundAssetUrl } from "@repo/shared/lib/types/widgetSettings";
import { ReactGameConfig } from "../../types/config";

export class BaseScene extends Phaser.Scene {

  protected config: ReactGameConfig;
  protected resolveAssetUrl: (id: AssetUrl) => string;

  constructor(
    gameConfig: ReactGameConfig,
    resolveAssetUrl: (id: AssetUrl) => string
  ) {
    super("BaseScene");
    this.config = gameConfig;
    this.resolveAssetUrl = resolveAssetUrl;
  }

  loadAsset(type: "audio" | "image", configKey: keyof ReactGameConfig) {
    if (
      type == "audio" && (this.config[configKey] as SoundAssetUrl)?.enabled == false
    ) {
      return;
    }

    const safeResolve = (configKey: keyof ReactGameConfig): string => {
      try {
        if (  this.config && this.config[configKey]) {
         
          const assetUrl = this.config[configKey] as AssetUrl;
          const resolved = this.resolveAssetUrl(assetUrl);

          if (resolved) {
            console.log("Resolved asset for", configKey, ":", resolved);
            return resolved;
          } else {
            console.error(`Failed to resolve asset for ${String(configKey)}`, assetUrl);
          }
        }
        return "";
      } catch (err) {
        console.error(`Error resolving asset for ${String(configKey)}:`, err);
        return "";
      }
    };

    const url = safeResolve(configKey);
    if (url) {
      try {
        if (type === "audio") {
          console.log("Loading audio", url);
          this.load.audio(String(configKey), url);
        } else {
          this.load.image(String(configKey), url);
        }
      } catch (e) {
        console.error(e);
      }
    }
  }

  loadSpriteSheet(configKey: keyof ReactGameConfig, frameCount: number) {
    const safeResolve = (configKey: keyof ReactGameConfig): string => {
      try {
        if (this.config && this.config[configKey]) {
          const assetUrl = this.config[configKey] as AssetUrl;
          const resolved = this.resolveAssetUrl(assetUrl);

          if (resolved) {
            console.log("Resolved sprite sheet asset for", configKey, ":", resolved);
            return resolved;
          } else {
            console.error(`Failed to resolve sprite sheet asset for ${String(configKey)}`, assetUrl);
          }
        }
        return "";
      } catch (err) {
        console.error(`Error resolving sprite sheet asset for ${String(configKey)}:`, err);
        return "";
      }
    };

    const url = safeResolve(configKey);
    if (url) {
      try {
        console.log("Loading image", url);
        this.load.image('temp_' + String(configKey), url);
        this.load.once('filecomplete-image-temp_' + String(configKey), () => {
          const image = this.textures.get('temp_' + String(configKey)).getSourceImage() as HTMLImageElement;
          const frameWidth = image.width / frameCount;
          const frameHeight = image.height;
          
          this.load.spritesheet(String(configKey), url, {
            frameWidth: frameWidth,
            frameHeight: frameHeight,
          });
        });
      } catch (e) {
        console.error("Error loading sprite sheet:", e);
      }
    }
  }

  protected setupSoundEffect(key: string) {
    if (!this.cache.audio.exists(key)) {
      return null;
    }
    console.log("Setting up sound effect for", key);
    return this.sound.add(key, {
      loop: false,
      volume: 0.4,
    });
  }
}
