import { TryAgainScreen, RewardScreen, OutOfLivesScreen, PickingRewardScreen } from './Scenes'
import { GameRuntimeComponentProps } from '@repo/shared/lib/game/game'
import { useCallback, useEffect, useRef, useState } from 'react'
import { gameEvents } from '@repo/shared/lib/campaign/eventEmitter'
import { useGameAtom } from '@repo/shared/lib/atoms/atom-extensions'
import { defaultGameConfig, ReactGameConfig } from '../types/config'
import { GameScreenId } from '../types/screen'
import { makeElementsInteractive } from '@repo/shared-game-utils/utils/previewInteractive'
import { usePreloadImage } from '@repo/shared-game-utils/hooks/usePreloadImage'
import { useGameRewards } from '@repo/shared/lib/game/useGameRewards'
import { useMusic } from '@repo/shared-game-utils/hooks/useSounds'
import { GameButton } from '@repo/shared-game-utils/components/GameButton'
import { CounterElement } from '@repo/shared-game-utils/components/CounterElement'
import { GameSoundSwitch } from '@repo/shared-game-utils/components/GameSoundSwitch'
import { GameContext, useGame } from '@repo/shared-game-utils/hooks/useGame'
export type GameRuntimeProps = GameRuntimeComponentProps<ReactGameConfig> & {
    currentScreenId?: GameScreenId
    initialGameScreenChecked: React.MutableRefObject<boolean>
}

export default function MainGame(props: GameRuntimeProps) {
    const [currentScreenId, setCurrentScreenId] = useState<GameScreenId>(props.currentScreenId || 'main')
    const initialGameScreenChecked = useRef(false)

    // Create an enhanced context with both props and screen state
    const contextValue = {
        ...props,
        currentScreenId,
        setCurrentScreenId,
        initialGameScreenChecked,
        defaultConfig: defaultGameConfig,
    }

    useEffect(() => {
        if (!props.isPreview) return
        setCurrentScreenId(props.currentScreenId || 'main')
    }, [props.currentScreenId, props.isPreview])

    if(!props.config) {
       console.log("Config not yet there. Waiting...")
        return <>Loading</>
    }

    useEffect(() => {
        console.log("Config updated", props.config)
    }, [props.config])

    return (
        <GameContext.Provider value={contextValue}>
                <GameContent />
        </GameContext.Provider>
    )
}

export const useGameState = () => {
    const { config, widgetId } = useGame<ReactGameConfig>()
    const initialLivesCount = config.gameEndHandler?.useLives ? config.gameEndHandler?.livesCount || 3 : Number.MAX_SAFE_INTEGER

    const [lives, setLivesCount] = useGameAtom(widgetId, 'livesCount', initialLivesCount)
    const [attemptsTaken, setAttemptsTaken] = useGameAtom(widgetId, 'attemptsTaken', 0)


    return {
        lives,
        setLivesCount,
        attemptsTaken,
        setAttemptsTaken,
    }
}

function usePrepareAssets() {
    console.log("Prepare assets")
    const { config } = useGame<ReactGameConfig>()

    const [loadedElementsCount, setLoadedElementsCount] = useState(0)
    const elementsToLoad = 10
    const loadPercentage =  Math.round((loadedElementsCount / elementsToLoad) * 100)

    function appendLoadedElementCount() {
        setLoadedElementsCount((prev) => prev + 1)
    }

    useMusic(config.backgroundMusic, false)
    useMusic(config.gameOverSound, false)
    useMusic(config.winSound, false)
    useMusic(config.pickingRewardSound, false)

    // Preload images
    usePreloadImage(config.openButton?.asset, appendLoadedElementCount)
    usePreloadImage(config.pickingRewardBackground?.asset, appendLoadedElementCount)
    usePreloadImage(config.mainBackground?.asset, appendLoadedElementCount)
    usePreloadImage(config.rewardBackground?.asset, appendLoadedElementCount)
    usePreloadImage(config.tryAgainBackground?.asset, appendLoadedElementCount)
    usePreloadImage(config.gameOverOverlay?.asset, appendLoadedElementCount)
    usePreloadImage(config.rewardOverlay?.asset, appendLoadedElementCount)
    usePreloadImage(config.outOfLivesOverlay?.asset, appendLoadedElementCount)
    usePreloadImage(config.gameSoundSwitch?.onAsset, appendLoadedElementCount)
    usePreloadImage(config.gameSoundSwitch?.offAsset, appendLoadedElementCount)

    return { loadPercentage }
}

function GameContent() {
    const { config, widgetId, resolveAssetUrl, isPreview, currentScreenId, setCurrentScreenId, onGameAssetSelect } = useGame<ReactGameConfig>()

    const { loadPercentage } = usePrepareAssets()

    useMusic(config.backgroundMusic, true, true)

    const containerRef = useRef<HTMLDivElement>(null)

    // // Function to handle try again button click
    const handleTryAgainClick = useCallback(() => {
        if (isPreview) return

        setCurrentScreenId('main')
    }, [isPreview, setCurrentScreenId])

    // Function to handle reward button click
    const handleRewardButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinishedWithReward', {
            score: 0, // Set score to 0 as we're removing score functionality
            widgetId: widgetId,
        })
    }, [isPreview, widgetId])

    // Function to handle out of lives button click
    const handleOutOfLivesButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinishedNoReward', {
            score: 0, // Set score to 0 as we're removing score functionality
            widgetId: widgetId,
        })
    }, [isPreview, widgetId])



    useEffect(() => {
        if (!isPreview) return
        if(loadPercentage < 100) return
        
        makeElementsInteractive(onGameAssetSelect)
    }, [currentScreenId, isPreview, loadPercentage])


    if(loadPercentage < 100) {
        return <div>Loading: {loadPercentage}%</div>
    }
    // style={getBackgroundStyle(config.mainBackground, resolveAssetUrl)}


    let bgImg = resolveAssetUrl(config.mainBackground?.asset)

    if(currentScreenId == 'picking-reward') {
        bgImg = resolveAssetUrl(config.pickingRewardBackground?.asset)
    }

    if(currentScreenId == 'try-again') {
        bgImg = resolveAssetUrl(config.tryAgainBackground?.asset)
    }

    if(currentScreenId == 'out-of-lives') {
        bgImg = resolveAssetUrl(config.outOfLivesBackground?.asset)
    }

    if(currentScreenId == 'claim-reward') {
        bgImg = resolveAssetUrl(config.rewardBackground?.asset)
    }

    // Removed claim-reward-or-try-again background handling as that screen is no longer used

    return (
        <div
            data-game-widget-id={widgetId}
            ref={containerRef}
            className="relative flex flex-col items-center justify-center h-full w-full touch-none select-none overscroll-none"

        >

            <img src={bgImg} className='absolute h-full w-full' style ={{
                objectFit: 'cover',
                objectPosition: 'center',
                width: '100%',
                height: '100%'
            }}/>

            <div className="relative w-full h-full text-black">
                {currentScreenId === 'main' && <MainGameScreen />}
                {currentScreenId === 'try-again' && <TryAgainScreen onButtonClick={handleTryAgainClick} />}
                {/* Removed claim-reward-or-try-again screen as per requirement to only show claim reward */}
                {currentScreenId === 'claim-reward' && <RewardScreen onButtonClick={handleRewardButtonClick} />}
                {currentScreenId === 'out-of-lives' && <OutOfLivesScreen onButtonClick={handleOutOfLivesButtonClick} />}
                {currentScreenId === 'picking-reward' && <PickingRewardScreen  />}
            </div>
        </div>
    )
}

function MainGameScreen() {
    const { initialGameScreenChecked, config, setCurrentScreenId, isPreview, widgetId } = useGame<ReactGameConfig>()
    const { lives, setLivesCount, setAttemptsTaken, attemptsTaken } = useGameState()
    const { hasWonReward } = useGameRewards({gameWidgetId: widgetId, attempt: attemptsTaken })


    const useLives = config.gameEndHandler?.useLives

    const checkInitialGameScreen = useCallback(
        (lives: number) => {
            if (isPreview) return

            // First check if we have a reward to claim
            if (hasWonReward) {
                // Always show claim reward screen if there's a reward, regardless of lives
                setCurrentScreenId('claim-reward')
                return
            }

            // Then check if we have out of lives condition
            if (useLives && lives <= 0) {
                // Show out of lives screen
                setCurrentScreenId('out-of-lives')
                return
            }

            // If we reach here, we don't have a reward and we have lives
            // Just show the main game screen
            setCurrentScreenId('main')
            return
        },
        [useLives, lives, setCurrentScreenId, isPreview, hasWonReward]
    )

    // We've replaced these functions with handleOpenClick

    // Function to handle opening the lootbox
    const handleOpenClick = useCallback(() => {
        if (isPreview) return


        // Check if lives system is enabled and if player has lives
        if (useLives && lives <= 0) {
            setCurrentScreenId('out-of-lives')
            return
        }

        // Decrement lives if lives system is enabled
        if (useLives) {
            setLivesCount(lives - 1)
        }

        setAttemptsTaken(prev => prev ? prev + 1 : 1)

        setCurrentScreenId('picking-reward')
    }, [isPreview, useLives, lives, setLivesCount, setCurrentScreenId])

    useEffect(() => {
        if (isPreview) return
        if (initialGameScreenChecked.current) return
        console.log('checking initial game screen')
        initialGameScreenChecked.current = true
        checkInitialGameScreen(lives)
    }, [lives])

    return (
        <div className="game-content-area w-full h-full flex flex-col p-4" data-editor-is-background={true}  data-editor-selectable-key="mainBackground" >
            {/* Top section with sound switch and lives counter */}
            <div className="w-full flex justify-between items-center">
                {/* Lives counter - only shown when lives system is enabled */}
                <div className="flex items-center p-2">
                    {useLives && (
                        <div className="flex items-center">
                            <CounterElement configKey="livesStyle" style={config.livesStyle}>
                                {isPreview ? 3 : lives} 
                            </CounterElement>
                        </div>
                    )}
                </div>

                {/* Sound switch */}
                <div
                    className={`flex ${config.gameSoundSwitch?.alignment === 'center' ? 'justify-center' : config.gameSoundSwitch?.alignment === 'right' ? 'justify-end' : 'justify-start'} p-2`}
                >
                    <GameSoundSwitch config={config.gameSoundSwitch} dataConfigKey="gameSoundSwitch" />
                </div>
            </div>

            <div className="flex-1 flex flex-col items-center justify-center">
                <GameButton
                    config={config.openButton}
                    dataConfigKey="openButton"
                    onClick={handleOpenClick}
                />
            </div>
        </div>
    )
}
