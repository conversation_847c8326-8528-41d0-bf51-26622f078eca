import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { MemoryCardStyle, MemoryGridStyle } from '../../types/config'
import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { ReactGameConfig } from '../../types/config'
import './memory-card.css'

export interface MemoryCardProps {
    id: number
    cardIndex?: number // Optional, not used in component
    frontConfig: MemoryCardStyle
    backConfig: MemoryCardStyle
    isFlipped: boolean
    isMatched: boolean
    onClick: () => void
    dataConfigKey?: string
}

export const MemoryCard: React.FC<MemoryCardProps> = ({
    frontConfig,
    backConfig,
    isFlipped,
    isMatched,
    onClick,
    dataConfigKey,
}) => {
    const { resolveAssetUrl, isPreview, onGameAssetSelect, config } = useGame<ReactGameConfig>()
    const [isAnimating, setIsAnimating] = useState(false)
    const gridConfig = config.memoryGrid

    // Handle click event
    const handleClick = () => {
        if (!isFlipped && !isMatched && !isAnimating) {
            setIsAnimating(true)
            onClick()
        }
    }

    // Reset animation state after flip completes
    useEffect(() => {
        if (isAnimating) {
            const timer = setTimeout(() => {
                setIsAnimating(false)
            }, 500) // Match this with the animation duration
            return () => clearTimeout(timer)
        }
    }, [isAnimating])

    // Get card style
    const getCardStyle = (config: MemoryCardStyle) => {
        return {
            // Use global card dimensions from grid config
            width: `${gridConfig.cardWidth || 100}px`,
            height: `${gridConfig.cardHeight || 100}px`,
            borderRadius: `${gridConfig.cardBorderRadius || 0}px`,
            backgroundColor: config.useBackgroundColor ? config.fill || 'transparent' : 'transparent',
            backgroundImage: config.asset ? `url(${resolveAssetUrl(config.asset)})` : 'none',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            cursor: isFlipped || isMatched ? 'default' : 'pointer',
        }
    }

    // Get front and back styles
    const frontStyle = getCardStyle(frontConfig)
    const backStyle = getCardStyle(backConfig)

    return (
        <div
            data-editor-selectable-key={dataConfigKey}
            data-editor-selectable-is-background={true}
            className="relative"
            style={{
                width: `${gridConfig.cardWidth || 100}px`,
                height: `${gridConfig.cardHeight || 100}px`,
                margin: '4px'
            }}
            onClick={(e) => {
                if (isPreview) {
                    e.stopPropagation()
                    if (onGameAssetSelect && dataConfigKey) {
                        const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
                        onGameAssetSelect(dataConfigKey, {
                            x: rect.left,
                            y: rect.top,
                            width: rect.width,
                            height: rect.height,
                        })
                    }
                } else {
                    handleClick()
                }
            }}
        >
            <motion.div
                className="absolute w-full h-full"
                initial={false}
                animate={{
                    rotateY: isFlipped || isMatched ? 180 : 0,
                }}
                transition={{
                    duration: 0.5,
                    type: 'spring',
                    stiffness: 300,
                    damping: 20,
                }}
                style={{
                    transformStyle: 'preserve-3d',
                }}
            >
                {/* Back of card (shown when not flipped) */}
                <div
                    className="absolute w-full h-full backface-hidden"
                    style={{
                        ...backStyle,
                    }}
                    data-editor-selectable-key="memoryCardBack"
                    onClick={(e) => {
                        if (isPreview && onGameAssetSelect) {
                            e.stopPropagation()
                            const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
                            onGameAssetSelect("memoryCardBack", {
                                x: rect.left,
                                y: rect.top,
                                width: rect.width,
                                height: rect.height,
                            })
                        }
                    }}
                />

                {/* Front of card (shown when flipped) */}
                <div
                    className="absolute w-full h-full backface-hidden"
                    style={{
                        ...frontStyle,
                        transform: 'rotateY(180deg)',
                    }}
                    data-editor-selectable-key={dataConfigKey}
                    onClick={(e) => {
                        if (isPreview && onGameAssetSelect && dataConfigKey) {
                            e.stopPropagation()
                            const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
                            onGameAssetSelect(dataConfigKey, {
                                x: rect.left,
                                y: rect.top,
                                width: rect.width,
                                height: rect.height,
                            })
                        }
                    }}
                />
            </motion.div>
        </div>
    )
}
