import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { Input } from '@repo/shared/components/ui/input'
import { FontPicker } from '@repo/shared/components/editor/fontPicker'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'

interface ScoreboardNumbersStyleEditorProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
}

export function ScoreboardNumbersStyleEditor({ configKey, config, onChange }: ScoreboardNumbersStyleEditorProps) {
    return (
        <div className="space-y-4">
            <div>
                <Label className="mb-2 block">Background Image</Label>
                <AssetPicker
                    onSelect={(assetId) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                asset: { assetId },
                            },
                        })
                    }
                    assetUrl={(config[configKey] as any)?.asset}
                    extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']}
                />
            </div>

            <div>
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Background Color</Label>
                    <Switch
                        checked={(config[configKey] as any)?.useBackgroundColor !== false}
                        onCheckedChange={(useBackgroundColor) =>
                            onChange({
                                [configKey]: { ...config[configKey], useBackgroundColor },
                            })
                        }
                    />
                </div>
                <div className={`transition-opacity ${(config[configKey] as any)?.useBackgroundColor !== false ? 'opacity-100' : 'opacity-40'}`}>
                    <ColorPicker
                        color={(config[configKey] as any)?.fill || '#ffffff'}
                        onChange={(fill) =>
                            onChange({
                                [configKey]: {
                                    ...config[configKey],
                                    fill,
                                },
                            })
                        }
                    />
                </div>
            </div>

            <div>
                <Label className="mb-2 block">Text Color</Label>
                <ColorPicker
                    color={(config[configKey] as any)?.textColor || '#000000'}
                    onChange={(textColor) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                textColor,
                            },
                        })
                    }
                />
            </div>

            <div>
                <Label className="mb-2 block">Font Family</Label>
                <FontPicker
                    font={(config[configKey] as any)?.fontFamily_tFontFamily || 'Londrina Solid'}
                    onChange={(fontFamily) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                fontFamily_tFontFamily: fontFamily,
                            },
                        })
                    }
                />
            </div>

            <div>
                <Label className="mb-2 block">Font Size</Label>
                <Input
                    type="number"
                    value={(config[configKey] as any)?.fontSize || 16}
                    onChange={(e) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                fontSize: parseInt(e.target.value, 10),
                            },
                        })
                    }
                />
            </div>

            <div>
                <Label className="mb-2 block">Text Alignment</Label>
                <Select
                    value={(config[configKey] as any)?.textAlign || 'center'}
                    onValueChange={(value) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                textAlign: value,
                            },
                        })
                    }
                >
                    <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select alignment" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                        <SelectItem value="top-left">Top Left</SelectItem>
                        <SelectItem value="top-center">Top Center</SelectItem>
                        <SelectItem value="top-right">Top Right</SelectItem>
                        <SelectItem value="bottom-left">Bottom Left</SelectItem>
                        <SelectItem value="bottom-center">Bottom Center</SelectItem>
                        <SelectItem value="bottom-right">Bottom Right</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            <div>
                <Label className="mb-2 block">Padding</Label>
                <div className="grid grid-cols-2 gap-2">
                    <div>
                        <Label className="text-xs">Top</Label>
                        <Input
                            type="number"
                            value={(config[configKey] as any)?.padding?.top || 0}
                            onChange={(e) =>
                                onChange({
                                    [configKey]: {
                                        ...config[configKey],
                                        padding: {
                                            ...(config[configKey] as any)?.padding,
                                            top: parseInt(e.target.value, 10),
                                        },
                                    },
                                })
                            }
                        />
                    </div>
                    <div>
                        <Label className="text-xs">Right</Label>
                        <Input
                            type="number"
                            value={(config[configKey] as any)?.padding?.right || 0}
                            onChange={(e) =>
                                onChange({
                                    [configKey]: {
                                        ...config[configKey],
                                        padding: {
                                            ...(config[configKey] as any)?.padding,
                                            right: parseInt(e.target.value, 10),
                                        },
                                    },
                                })
                            }
                        />
                    </div>
                    <div>
                        <Label className="text-xs">Bottom</Label>
                        <Input
                            type="number"
                            value={(config[configKey] as any)?.padding?.bottom || 0}
                            onChange={(e) =>
                                onChange({
                                    [configKey]: {
                                        ...config[configKey],
                                        padding: {
                                            ...(config[configKey] as any)?.padding,
                                            bottom: parseInt(e.target.value, 10),
                                        },
                                    },
                                })
                            }
                        />
                    </div>
                    <div>
                        <Label className="text-xs">Left</Label>
                        <Input
                            type="number"
                            value={(config[configKey] as any)?.padding?.left || 0}
                            onChange={(e) =>
                                onChange({
                                    [configKey]: {
                                        ...config[configKey],
                                        padding: {
                                            ...(config[configKey] as any)?.padding,
                                            left: parseInt(e.target.value, 10),
                                        },
                                    },
                                })
                            }
                        />
                    </div>
                </div>
            </div>

            <div>
                <Label className="mb-2 block">Dimensions</Label>
                <div className="grid grid-cols-2 gap-2">
                    <div>
                        <Label className="text-xs">Width</Label>
                        <Input
                            type="number"
                            value={(config[configKey] as any)?.width || 80}
                            onChange={(e) =>
                                onChange({
                                    [configKey]: {
                                        ...config[configKey],
                                        width: parseInt(e.target.value, 10),
                                    },
                                })
                            }
                        />
                    </div>
                    <div>
                        <Label className="text-xs">Height</Label>
                        <Input
                            type="number"
                            value={(config[configKey] as any)?.height || 45}
                            onChange={(e) =>
                                onChange({
                                    [configKey]: {
                                        ...config[configKey],
                                        height: parseInt(e.target.value, 10),
                                    },
                                })
                            }
                        />
                    </div>
                </div>
            </div>

            <div>
                <Label className="mb-2 block">Border Radius</Label>
                <Input
                    type="number"
                    value={(config[configKey] as any)?.borderRadius || 0}
                    onChange={(e) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                borderRadius: parseInt(e.target.value, 10),
                            },
                        })
                    }
                />
            </div>

            <div>
                <Label className="mb-2 block">Margin Bottom</Label>
                <Input
                    type="number"
                    value={(config[configKey] as any)?.marginBottom || 0}
                    onChange={(e) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                marginBottom: parseInt(e.target.value, 10),
                            },
                        })
                    }
                />
            </div>
        </div>
    )
}
