import React from 'react'
import { Button } from '@repo/shared/components/ui/button'
import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import { RefreshCw } from 'lucide-react'
import { defaultGameConfig } from '../../quiz-game/src/types/config'

interface RewardsSettingsEditorProps {
    config: any
    onChange: (config: any) => void
}

export const RewardsSettingsEditor: React.FC<RewardsSettingsEditorProps> = ({ config, onChange }) => {
    const handleGameRewardsHandlerChange = (updates: any) => {
        onChange({
            gameRewardsHandler: {
                ...config.gameRewardsHandler,
                ...updates,
            },
        })
    }

    const handleResetToDefault = () => {
        const defaultValue = defaultGameConfig.gameRewardsHandler
        if (defaultValue !== undefined) {
            onChange({ gameRewardsHandler: defaultValue })
        }
    }

    return (
        <div className="space-y-4">

            <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Enable Rewards</Label>
                    <Switch checked={config.gameRewardsHandler?.rewardsEnabled || false} onCheckedChange={(value) => handleGameRewardsHandlerChange({ rewardsEnabled: value })} />
                </div>

                {config.gameRewardsHandler?.rewardsEnabled && (
                    <div className="space-y-2">
                        {/* Settings removed: enableCtaButton, showRewardInGame, and amountOfCoinsForReward are now always true by default */}
                    </div>
                )}
            </div>
        </div>
    )
}
