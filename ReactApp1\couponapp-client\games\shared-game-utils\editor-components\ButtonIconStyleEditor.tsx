import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { Slider } from '@repo/shared/components/ui/slider'
import { Button } from '@repo/shared/components/ui/button'
import { Trash2 } from 'lucide-react'
import { IconPicker } from '@repo/shared/components/ui/icon-picker'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@repo/shared/components/ui/accordion'
import { Separator } from '@repo/shared/components/ui/separator'
import { ButtonIconStyle } from '../types/uiStyles'


interface ButtonIconStyleEditorProps {
    config: ButtonIconStyle
    onChange: (changes: ButtonIconStyle) => void
}

export function ButtonIconStyleEditor({ config, onChange }: ButtonIconStyleEditorProps) {
    return (
        <Accordion type="single" collapsible className="w-full ">
            <AccordionItem value="icon">
                <AccordionTrigger>Icon Settings</AccordionTrigger>
                <AccordionContent>
                    <div className="space-y-4 p-2">
                        <div className="flex items-center gap-2">
                            <div className="flex-1">
                                <IconPicker value={config?.iconName_tIcon as any} onValueChange={(iconName) => onChange({ ...config, iconName_tIcon: iconName })} />
                                {config?.iconName_tIcon && (
                                    <Button className="ms-2" variant="destructive" size="sm" onClick={() => onChange({ ...config, iconName_tIcon: null })}>
                                        <Trash2 size={16} />
                                    </Button>
                                )}
                            </div>
                        </div>
                        {config?.iconName_tIcon && (
                            <>
                                <div>
                                    <Label>Icon Color</Label>
                                    <ColorPicker color={config?.color || '#000000'} onChange={(color) => onChange({ ...config, color })} />
                                </div>
                                <div>
                                    <div className="flex items-center space-x-2 mb-2">
                                        <Switch checked={!!config?.fill} onCheckedChange={(checked) => onChange({ ...config, fill: checked })} />
                                        <Label className="flex-grow">Filled</Label>
                                    </div>
                                </div>
                                <div>
                                    <Label>Stroke Width</Label>
                                    <Slider value={[config?.strokeWidth || 2]} min={1} max={5} step={0.5} onValueChange={(values) => onChange({ ...config, strokeWidth: values[0] })} />
                                    <div className="mt-1 text-xs text-gray-500">{config?.strokeWidth || 2}px</div>
                                </div>
                                <div>
                                    <Label>Icon Size</Label>
                                    <Slider value={[config?.size || 24]} min={12} max={48} step={1} onValueChange={(values) => onChange({ ...config, size: values[0] })} />
                                    <div className="mt-1 text-xs text-gray-500">{config?.size || 24}px</div>
                                </div>
                                <div>
                                    <Label>Icon Position</Label>
                                    <Select value={config?.position || 'left'} onValueChange={(position) => onChange({ ...config, position: position as any })}>
                                        <SelectTrigger className="w-full">
                                            <SelectValue placeholder="Select position" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="left">Left</SelectItem>
                                            <SelectItem value="right">Right</SelectItem>
                                            <SelectItem value="top">Top</SelectItem>
                                            <SelectItem value="bottom">Bottom</SelectItem>
                                            <SelectItem value="center">Center</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <Separator />
                                <div>
                                    <Label>Icon Offset X</Label>
                                    <Slider value={[config?.offsetX || 0]} min={-20} max={20} step={1} onValueChange={(values) => onChange({ ...config, offsetX: values[0] })} />
                                    <div className="mt-1 text-xs text-gray-500">{config?.offsetX || 0}px</div>
                                </div>
                                <div>
                                    <Label>Icon Offset Y</Label>
                                    <Slider value={[config?.offsetY || 0]} min={-20} max={20} step={1} onValueChange={(values) => onChange({ ...config, offsetY: values[0] })} />
                                    <div className="mt-1 text-xs text-gray-500">{config?.offsetY || 0}px</div>
                                </div>
                            </>
                        )}
                    </div>
                </AccordionContent>
            </AccordionItem>
        </Accordion>
    )
}
