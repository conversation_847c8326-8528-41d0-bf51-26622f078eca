import { BackgroundStyle, ButtonIconStyle, ContainerStyle, CounterElementStyle, GameButtonStyle, GameEndHandler, 
    GameRewardsHandler, GameSoundSwitchStyle, GameTextSettings, GameTimerHandler, GameTimerStyle, RewardComponentStyle } from '@repo/shared-game-utils/types/uiStyles'
import { GameConfig, gameConfigKey } from '@repo/shared/lib/game/gameConfig'
import { AssetUrl, SoundAssetUrl } from '@repo/shared/lib/types/widgetSettings'

export type QuestionAreaAnimationType = 'none' | 'card-slide'

export type QuestionAreaContainerStyle = ContainerStyle & {
    animationType?: QuestionAreaAnimationType
}

export type AnswerTileStyle = {
    textConfig: GameTextSettings
    asset?: AssetUrl
    fill?: string
    useBackgroundColor?: boolean
    borderRadius?: number
    isVisible?: boolean
    iconStyle?: ButtonIconStyle
    backgroundScaleMode?: 'cover' | 'contain' | 'pixelated' | 'pixelated-stretch' | 'pixelated-cover'
    pixelated?: boolean
}

export type QuizAnswer = {
    text: string
    isCorrect: boolean
}

export type QuizQuestion = {
    text: string
    answers: QuizAnswer[]
    image?: AssetUrl
}

export type QuestionAnswerSettings = {
    questions: QuizQuestion[]
}


export type GameStartHandler = {
    enableStartScreen?: boolean
}

export class ReactGameConfig extends GameConfig {
    gameStartHandler?: GameStartHandler

    @gameConfigKey({ name: 'Lives Handler', configEditorType: 'lives-handler' })
    gameEndHandler?: GameEndHandler

    @gameConfigKey({ name: 'Timer Handler', configEditorType: 'timer-handler' })
    gameTimerHandler?: GameTimerHandler

    @gameConfigKey({ name: 'Timer Style', configEditorType: 'game-timer' })
    gameTimerStyle?: GameTimerStyle

    @gameConfigKey({ name: 'Rewards Handler', configEditorType: 'rewards-handler' })
    gameRewardsHandler?: GameRewardsHandler

    @gameConfigKey({ name: 'Background Music', configEditorType: 'audio' })
    backgroundMusic?: SoundAssetUrl

    @gameConfigKey({ name: 'Win Sound', configEditorType: 'audio' })
    winSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Picking Reward Sound', configEditorType: 'audio' })
    pickingRewardSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Game Over Sound', configEditorType: 'audio' })
    gameOverSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Correct Answer Sound', configEditorType: 'audio' })
    correctAnswerSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Incorrect Answer Sound', configEditorType: 'audio' })
    incorrectAnswerSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Continue to Next Question Sound', configEditorType: 'audio' })
    continueToNextQuestionSound?: SoundAssetUrl

    @gameConfigKey({ name: 'Question Container', configEditorType: 'container' })
    questionContainer?: ContainerStyle

    @gameConfigKey({ name: 'Question Area Container', configEditorType: 'question-area-container' })
    questionAreaContainer?: QuestionAreaContainerStyle

    @gameConfigKey({ name: 'Question Text', configEditorType: 'text' })
    questionText?: GameTextSettings

    @gameConfigKey({ name: 'Answer Button Default', configEditorType: 'answer-tile' })
    answerButtonDefault?: AnswerTileStyle

    @gameConfigKey({ name: 'Answer Button Correct', configEditorType: 'answer-tile' })
    answerButtonCorrect?: AnswerTileStyle

    @gameConfigKey({ name: 'Answer Button Incorrect', configEditorType: 'answer-tile' })
    answerButtonIncorrect?: AnswerTileStyle

    @gameConfigKey({ name: 'Questions & Answers', configEditorType: 'question-answer-settings' })
    questionAnswerSettings?: QuestionAnswerSettings

    @gameConfigKey({
        name: 'Main Background',
        configEditorType: 'background',
        width: 800,
        height: 600,
    })
    mainBackground: BackgroundStyle

    @gameConfigKey({
        name: 'Score Style',
        configEditorType: 'counter',
    })
    scoreStyle: CounterElementStyle

    @gameConfigKey({
        name: 'Lives Style',
        configEditorType: 'counter',
    })
    livesStyle: CounterElementStyle

    @gameConfigKey({
        name: 'Game Over Text',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    gameOverText: GameTextSettings

    @gameConfigKey({
        name: 'Out of Lives Try Again Button',
        configEditorType: 'game-button',
        editorSettings: { toggleableVisibility: true },
    })
    outOfLivesContinueButton: GameButtonStyle

    @gameConfigKey({ name: 'Lose Life Overlay', configEditorType: 'container' })
    loseLifeOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Lose Life Title', configEditorType: 'text' })
    loseLifeTitle?: GameTextSettings

    @gameConfigKey({ name: 'Continue Button', configEditorType: 'game-button' })
    continueButton?: GameButtonStyle

    @gameConfigKey({ name: 'Try Again Button', configEditorType: 'game-button' })
    tryAgainButton?: GameButtonStyle

    @gameConfigKey({ name: 'Game Over Overlay', configEditorType: 'container' })
    gameOverOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Game Over Title', configEditorType: 'text' })
    gameOverTitle?: GameTextSettings

    @gameConfigKey({ name: 'Game Over Message', configEditorType: 'text' })
    gameOverMessage?: GameTextSettings

    @gameConfigKey({ name: 'Game Over Continue Button', configEditorType: 'game-button' })
    gameOverContinueButton?: GameButtonStyle

    @gameConfigKey({ name: 'Reward Overlay', configEditorType: 'container' })
    rewardOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Reward Title', configEditorType: 'text' })
    rewardTitle?: GameTextSettings

    @gameConfigKey({ name: 'Reward Claim Button', configEditorType: 'game-button' })
    rewardClaimButton?: GameButtonStyle

    @gameConfigKey({ name: 'Out of Lives Overlay', configEditorType: 'container' })
    outOfLivesOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Out of Lives Title', configEditorType: 'text' })
    outOfLivesTitle?: GameTextSettings

    @gameConfigKey({ name: 'Claim Reward Overlay', configEditorType: 'container' })
    claimRewardOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Claim Reward Title', configEditorType: 'text' })
    claimRewardTitle?: GameTextSettings

    @gameConfigKey({ name: 'Claim Reward Button', configEditorType: 'game-button' })
    claimRewardButton?: GameButtonStyle


    @gameConfigKey({ name: 'Reward Component', configEditorType: 'reward-component-style' })
    rewardComponent?: RewardComponentStyle

    @gameConfigKey({ name: 'Start Screen Overlay', configEditorType: 'container' })
    startScreenOverlay?: ContainerStyle

    @gameConfigKey({ name: 'Start Screen Title', configEditorType: 'text' })
    startScreenTitle?: GameTextSettings

    @gameConfigKey({ name: 'Start Screen Start Button', configEditorType: 'game-button' })
    startScreenStartButton?: GameButtonStyle

    @gameConfigKey({ name: 'Sound Switch', configEditorType: 'sound-switch', editorSettings: { toggleableVisibility: true } })
    gameSoundSwitch?: GameSoundSwitchStyle
}

export const defaultGameConfig: ReactGameConfig = {
    gameStartHandler: {
        enableStartScreen: false,
    },

    gameEndHandler: {
        useLives: false,
        livesCount: 3,
    },

    gameTimerHandler: {
        useTimer: true,
        timerDuration: 20,
        loseLifeOnTimeout: true,
    },

    gameTimerStyle: {
        progressColor: '#22c55e',
        backgroundColor: '#4338ca',
    },

    gameRewardsHandler: {
        rewardsEnabled: false,
    },

    backgroundMusic: {
        enabled: true,
        absoluteUrl: 'https://example.com/background-music.mp3',
    },

    winSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/win-sound.mp3',
    },

    pickingRewardSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/picking-reward-sound.mp3',
    },

    gameOverSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/game-over-sound.mp3',
    },

    correctAnswerSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/correct-answer-sound.mp3',
    },

    incorrectAnswerSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/incorrect-answer-sound.mp3',
    },

    continueToNextQuestionSound: {
        enabled: true,
        absoluteUrl: 'https://example.com/continue-to-next-question-sound.mp3',
    },

    answerButtonDefault: {
        textConfig: {
            text: null, // Will be set from code
            style: {
                fontFamily_tFontFamily: 'Poppins',
                fontSize: 16,
                fill: '#4338ca', // indigo-800
                isVisible: true,
                textAlign: 'center',
            },
        },
        fill: '#f8fafc', // slate-50
        useBackgroundColor: true,
        borderRadius: 12,
    },

    answerButtonCorrect: {
        textConfig: {
            text: null, // Will be set from code
            style: {
                fontFamily_tFontFamily: 'Poppins',
                fontSize: 16,
                fill: '#ffffff', // white
                isVisible: true,
                textAlign: 'center',
            },
        },
        fill: '#22c55e', // green-500
        useBackgroundColor: true,
        borderRadius: 12,
    },

    answerButtonIncorrect: {
        textConfig: {
            text: null, // Will be set from code
            style: {
                fontFamily_tFontFamily: 'Poppins',
                fontSize: 16,
                fill: '#ffffff', // white
                isVisible: true,
                textAlign: 'center',
            },
        },
        fill: '#ef4444', // red-500
        useBackgroundColor: true,
        borderRadius: 12,
    },

    questionAnswerSettings: {
        questions: [
            {
                text: "Z czym podawany jest groch, według tradycyjnej polskiej potrawy?",
                answers: [
                    { text: "Ziemniakami", isCorrect: false },
                    { text: "Rodzynkami", isCorrect: false },
                    { text: "Kapustą", isCorrect: true },
                    { text: "Grzybami", isCorrect: false }
                ]
            },
            {
                text: "Która planeta jest najbliższa Słońcu?",
                answers: [
                    { text: "Wenus", isCorrect: false },
                    { text: "Merkury", isCorrect: true },
                    { text: "Mars", isCorrect: false },
                    { text: "Ziemia", isCorrect: false }
                ]
            },
            {
                text: "Jaki jest największy ocean na Ziemi?",
                answers: [
                    { text: "Atlantycki", isCorrect: false },
                    { text: "Spokojny", isCorrect: true },
                    { text: "Indyjski", isCorrect: false },
                    { text: "Arktyczny", isCorrect: false }
                ]
            },
            {
                text: "Który pierwiastek chemiczny ma symbol 'O'?",
                answers: [
                    { text: "Złoto", isCorrect: false },
                    { text: "Srebro", isCorrect: false },
                    { text: "Tlen", isCorrect: true },
                    { text: "Żelazo", isCorrect: false }
                ]
            },
            {
                text: "W którym roku wybuchła II wojna światowa?",
                answers: [
                    { text: "1939", isCorrect: true },
                    { text: "1941", isCorrect: false },
                    { text: "1945", isCorrect: false },
                    { text: "1937", isCorrect: false }
                ]
            }
        ]
    },

    mainBackground: {
        fill: '#faf8ef',
        useBackgroundColor: true,
    },

    questionContainer: {
        fill: '#4338ca', // indigo-800
        useBackgroundColor: true,
        borderRadius: 16,
        padding: {
            top: 24,
            right: 24,
            bottom: 24,
            left: 24,
        },
        maxWidth: 450,
    },

    questionText: {
        text: null, //must be null so we set text from child component
        style: {
            fontFamily_tFontFamily: 'Poppins',
            fontSize: 18,
            fill: '#ffffff',
            textAlign: 'center',
            isVisible: true,
        },
    },

    scoreStyle: {
        textConfig: {
            text: 'Score: ',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#776e65',
                isVisible: true,
            },
        },
        fill: '#eee4da',
        width: 100,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
        isVisible: true,
        position: 'center',
    },

    livesStyle: {
        textConfig: {
            text: 'Lives: ',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#776e65',
                isVisible: true,
            },
        },
        fill: '#eee4da',
        width: 100,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
        isVisible: true,
        position: 'left',
        iconStyle: {
            iconName_tIcon: 'Heart',
            color: '#e74c3c',
            size: 20,
            position: 'left',
            isVisible: true,
            fill: true,
        },
    },

    gameOverText: {
        text: 'Game Over!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    continueButton: {
        textConfig: {
            text: 'Ok',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    outOfLivesContinueButton: {
        textConfig: {
            text: 'Ok',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    loseLifeOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    loseLifeTitle: {
        text: 'You Lost a Life',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
       borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    gameOverTitle: {
        text: 'Game Over',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverMessage: {
        text: 'Better luck next time!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 22,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverContinueButton: {
        textConfig: {
            text: 'Continue',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    rewardOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
       borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    rewardTitle: {
        text: 'You Earned a Reward!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    rewardClaimButton: {
        textConfig: {
            text: 'Claim Reward',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    outOfLivesOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
       borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    outOfLivesTitle: {
        text: 'Out of Lives',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    claimRewardTitle: {
        text: 'Claim Your Reward or Try Again',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    claimRewardOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    claimRewardButton: {
        textConfig: {
            text: 'Claim Reward',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    tryAgainButton: {
        textConfig: {
            text: 'Try Again',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 160,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    rewardComponent: {
        imageWidth: 150,
        imageHeight: 150,
        titleFontFamily: 'Londrina Solid',
        titleFontSize: 22,
        titleColor: '#776e65',
        titleTextAlign: 'left',
        descriptionFontFamily: 'Poppins',
        descriptionFontSize: 16,
        descriptionColor: '#776e65',
        descriptionTextAlign: 'left',
        padding: 16,
        maxWidth: 400,
        layout: 'horizontal-left',
        imageBorderRadius: 8,
        imageMargin: 16,
        containerBackgroundColor: 'transparent',
        containerBorderRadius: 12,
        containerShadow: false,
        spacing: 16,
        showImage: true,
        showTitle: true,
        showDescription: true,
    },

    startScreenOverlay: {
        fill: 'rgba(238, 228, 218, 0.73)',
        useBackgroundColor: true,
        borderRadius: 12,
        padding: {
            top: 40,
            right: 40,
            bottom: 40,
            left: 40,
        },
        maxWidth: 500,
    },

    startScreenTitle: {
        text: 'Welcome to Quiz Game!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 32,
            fill: '#776e65',
            isVisible: true,
            textAlign: 'center',
        },
    },

    startScreenStartButton: {
        textConfig: {
            text: 'Start Game',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 20,
                fill: '#ffffff',
                isVisible: true,
                textAlign: 'center',
            },
        },
        fill: '#4338ca',
        width: 180,
        height: 50,
        useBackgroundColor: true,
        borderRadius: 8,
    },

    gameSoundSwitch: {
        onAsset: {
            absoluteUrl: 'https://placehold.co/48x48/f5cb5c/000000?text=ON',
        },
        offAsset: {
            absoluteUrl: 'https://placehold.co/48x48/f5cb5c/000000?text=OFF',
        },
        width: 48,
        height: 48,
        offsetX: 0,
        offsetY: 0,
        alignment: 'right',
        position: 'right',
        isVisible: true,
    },
}
