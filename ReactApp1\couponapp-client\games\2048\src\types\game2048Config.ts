import { <PERSON><PERSON>uttonSty<PERSON>, GameEndHandler, RewardComponentStyle, GameTextSettings, GameRewardsHandler } from '@repo/shared-game-utils/types/uiStyles'
import { GameConfig, gameConfigKey } from '@repo/shared/lib/game/gameConfig'
import { AssetBackground, AssetUrl, SoundAssetUrl } from '@repo/shared/lib/types/widgetSettings'


export type MainBackground = {
    asset?: AssetUrl
    fill?: string
    useBackgroundColor?: boolean
}

export type GridBackground = {
    asset?: AssetUrl
    fill?: string
    useBackgroundColor?: boolean
}

export type ScoreboardNumbersStyle = {
    asset?: AssetUrl
    fill?: string
    fontFamily_tFontFamily?: string
    fontSize?: number
    textColor?: string
    textAlign?: 'left' | 'center' | 'right' | 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
    padding?: {
        top?: number
        right?: number
        bottom?: number
        left?: number
    }
    marginBottom?: number
    width?: number
    height?: number
    useBackgroundColor?: boolean
}

export type Tile2048 = {
    asset?: AssetUrl
    fill?: string
    textColor?: string
    showText?: boolean
    useBackgroundColor?: boolean
    fontFamily_tFontFamily?: string
    fontSize?: number
}

export type EmptyTile2048 = {
    asset?: AssetUrl
    fill?: string
    useBackgroundColor?: boolean
}



export class Game2048Config extends GameConfig {
    @gameConfigKey({ name: 'Lives Handler', configEditorType: 'lives-handler' })
    gameEndHandler?: GameEndHandler

    @gameConfigKey({ name: 'Rewards Handler', configEditorType: 'rewards-handler' })
    rewardsHandler?: GameRewardsHandler

    @gameConfigKey({
        name: 'Main Background',
        configEditorType: 'image-background',
        width: 800,
        height: 600,
    })
    mainBackground: MainBackground

    @gameConfigKey({ name: 'Score Style', configEditorType: '2048-scoreboard-numbers-style' })
    scoreStyle: ScoreboardNumbersStyle

    @gameConfigKey({
        name: 'Best Score Style',
        configEditorType: '2048-scoreboard-numbers-style',
    })
    bestScoreStyle: ScoreboardNumbersStyle

    @gameConfigKey({ name: 'Lives Style', configEditorType: '2048-scoreboard-numbers-style' })
    livesStyle: ScoreboardNumbersStyle

    @gameConfigKey({
        name: 'Grid Background',
        configEditorType: '2048-grid-background',
    })
    gridBackground: GridBackground

    @gameConfigKey({
        name: 'Lose life overlay background',
        configEditorType: '2048-grid-background',
    })
    loseLifeOverlay: GridBackground

    @gameConfigKey({
        name: 'Tile Spacing',
        configEditorType: 'number',
    })
    tileSpacing: number

    @gameConfigKey({
        name: 'Grid Margin',
        configEditorType: 'number',
    })
    gridMargin: number

    @gameConfigKey({
        name: 'Empty Tile Style',
        configEditorType: 'empty-2048-tile',
    })
    emptyTile: EmptyTile2048

    @gameConfigKey({
        name: 'Tile 2 Style',
        configEditorType: '2048-tile',
        width: 80,
        height: 80,
    })
    tile2: Tile2048

    @gameConfigKey({
        name: 'Tile 4 Style',
        configEditorType: '2048-tile',
        width: 80,
        height: 80,
    })
    tile4: Tile2048

    @gameConfigKey({
        name: 'Tile 8 Style',
        configEditorType: '2048-tile',
        width: 80,
        height: 80,
    })
    tile8: Tile2048

    @gameConfigKey({
        name: 'Tile 16 Style',
        configEditorType: '2048-tile',
        width: 80,
        height: 80,
    })
    tile16: Tile2048

    @gameConfigKey({
        name: 'Tile 32 Style',
        configEditorType: '2048-tile',
        width: 80,
        height: 80,
    })
    tile32: Tile2048

    @gameConfigKey({
        name: 'Tile 64 Style',
        configEditorType: '2048-tile',
        width: 80,
        height: 80,
    })
    tile64: Tile2048

    @gameConfigKey({
        name: 'Tile 128 Style',
        configEditorType: '2048-tile',
        width: 80,
        height: 80,
    })
    tile128: Tile2048

    @gameConfigKey({
        name: 'Tile 256 Style',
        configEditorType: '2048-tile',
        width: 80,
        height: 80,
    })
    tile256: Tile2048

    @gameConfigKey({
        name: 'Tile 512 Style',
        configEditorType: '2048-tile',
        width: 80,
        height: 80,
    })
    tile512: Tile2048

    @gameConfigKey({
        name: 'Tile 1024 Style',
        configEditorType: '2048-tile',
        width: 80,
        height: 80,
    })
    tile1024: Tile2048

    @gameConfigKey({
        name: 'Tile 2048 Style',
        configEditorType: '2048-tile',
        width: 80,
        height: 80,
    })
    tile2048: Tile2048

    @gameConfigKey({
        name: 'Tile 4096 Style',
        configEditorType: '2048-tile',
        width: 80,
        height: 80,
    })
    tile4096: Tile2048

    @gameConfigKey({
        name: 'Game Over Text',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    gameOverText: GameTextSettings

    @gameConfigKey({
        name: 'Try Again Text',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    tryAgainText: GameTextSettings

    @gameConfigKey({
        name: 'New Game Text',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    newGameText: GameTextSettings

    @gameConfigKey({
        name: 'Game End Final Text',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    gameEndFinalText: GameTextSettings

    @gameConfigKey({
        name: 'Lose Life Title',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    loseLifeTitle: GameTextSettings

    @gameConfigKey({
        name: 'Continue Button',
        configEditorType: 'game-button',
    })
    continueButtonText: GameButtonStyle  
    
    
    @gameConfigKey({
        name: 'Continue Button',
        configEditorType: 'game-button',
    })
    loseLifeContinueButton: GameButtonStyle

    @gameConfigKey({
        name: 'Out of Lives Title',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    outOfLivesTitle: GameTextSettings

    @gameConfigKey({
        name: 'Out of Lives Try Again Button',
        configEditorType: 'game-button',
    })
    outOfLivesContinueButton: GameButtonStyle

    @gameConfigKey({
        name: 'Out of Lives Overlay',
        configEditorType: '2048-grid-background',
    })
    outOfLivesOverlay: GridBackground

    @gameConfigKey({
        name: 'Game Over Title',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    gameOverTitle: GameTextSettings

    @gameConfigKey({
        name: 'Game Over Message',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    gameOverMessage: GameTextSettings

    @gameConfigKey({
        name: 'Game Over Continue Button',
        configEditorType: 'game-button',
    })
    gameOverContinueButton: GameButtonStyle

    @gameConfigKey({
        name: 'Game Over Overlay',
        configEditorType: '2048-grid-background',
    })
    gameOverOverlay: GridBackground

    @gameConfigKey({
        name: 'Reward Title',
        configEditorType: 'text',
        editorSettings: { toggleableVisibility: true },
    })
    rewardTitle: GameTextSettings

    @gameConfigKey({
        name: 'Reward Claim Button',
        configEditorType: 'game-button',
    })
    rewardClaimButton: GameButtonStyle

    @gameConfigKey({
        name: 'Reward Overlay',
        configEditorType: '2048-grid-background',
    })
    rewardOverlay: GridBackground

    @gameConfigKey({ name: 'Reward Component', configEditorType: 'reward-component-style' })
    rewardComponent?: RewardComponentStyle
}

export const game2048DefaultConfig: Game2048Config = {
    gameEndHandler: {
        useLives: false,
        livesCount: 3,
    },

    mainBackground: {
        fill: '#faf8ef',
        useBackgroundColor: true,
    },

    scoreStyle: {
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 16,
        fill: '#776e65',
        textColor: '#f9f6f2',
        textAlign: 'center',
        width: 80,
        height: 45,
        marginBottom: 0,
        padding: {
            top: 5,
            right: 5,
            bottom: 5,
            left: 5,
        },
    },

    bestScoreStyle: {
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 16,
        fill: '#776e65',
        textColor: '#f9f6f2',
        textAlign: 'center',
        width: 80,
        height: 45,
        marginBottom: 0,
        padding: {
            top: 5,
            right: 5,
            bottom: 5,
            left: 5,
        },
    },

    livesStyle: {
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 16,
        fill: '#776e65',
        textColor: '#f9f6f2',
        textAlign: 'center',
        width: 80,
        height: 45,
        marginBottom: 0,
        padding: {
            top: 5,
            right: 5,
            bottom: 5,
            left: 5,
        },
    },

    gridBackground: {
        fill: '#bbada0',
        useBackgroundColor: true,
    },

    loseLifeOverlay: {
        fill: '#1e20ac',
        useBackgroundColor: true,
    },

    tileSpacing: 15,

    gridMargin: 20,

    emptyTile: {
        fill: '#cdc1b4',
        useBackgroundColor: true,
    },

    tile2: {
        fill: '#eee4da',
        textColor: '#776e65',
        showText: true,
        useBackgroundColor: true,
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 22,
    },

    tile4: {
        fill: '#ede0c8',
        textColor: '#776e65',
        showText: true,
        useBackgroundColor: true,
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 22,
    },

    tile8: {
        fill: '#f2b179',
        textColor: '#f9f6f2',
        showText: true,
        useBackgroundColor: true,
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 22,
    },

    tile16: {
        fill: '#f59563',
        textColor: '#f9f6f2',
        showText: true,
        useBackgroundColor: true,
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 22,
    },

    tile32: {
        fill: '#f67c5f',
        textColor: '#f9f6f2',
        showText: true,
        useBackgroundColor: true,
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 22,
    },

    tile64: {
        fill: '#f65e3b',
        textColor: '#f9f6f2',
        showText: true,
        useBackgroundColor: true,
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 22,
    },

    tile128: {
        fill: '#edcf72',
        textColor: '#f9f6f2',
        showText: true,
        useBackgroundColor: true,
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 22,
    },

    tile256: {
        fill: '#edcc61',
        textColor: '#f9f6f2',
        showText: true,
        useBackgroundColor: true,
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 22,
    },

    tile512: {
        fill: '#edc850',
        textColor: '#f9f6f2',
        showText: true,
        useBackgroundColor: true,
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 22,
    },

    tile1024: {
        fill: '#edc53f',
        textColor: '#f9f6f2',
        showText: true,
        useBackgroundColor: true,
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 20,
    },

    tile2048: {
        fill: '#edc22e',
        textColor: '#f9f6f2',
        showText: true,
        useBackgroundColor: true,
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 20,
    },

    tile4096: {
        fill: '#3c3a32',
        textColor: '#f9f6f2',
        showText: true,
        useBackgroundColor: true,
        fontFamily_tFontFamily: 'Londrina Solid',
        fontSize: 20,
    },

    gameOverText: {
        text: 'Game Over!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',

            isVisible: true,
        },
    },

    tryAgainText: {
        text: 'Try Again',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 22,
            fill: '#f9f6f2',

            isVisible: true,
        },
    },

    newGameText: {
        text: 'New Game',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 22,
            fill: '#f9f6f2',

            isVisible: true,
        },
    },

    gameEndFinalText: {
        text: 'Congratulations!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 24,
            fill: '#776e65',

            isVisible: true,
        },
    },

    loseLifeTitle: {
        text: 'You lost a life!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',

            isVisible: true,
        },
    },

    continueButtonText: {
        textConfig: {
            text: 'Continue',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    outOfLivesTitle: {
        text: "You've lost all lives!",
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#ffffff',

            isVisible: true,
        },
    },

    outOfLivesContinueButton: {
        textConfig: {
            text: 'Ok',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    outOfLivesOverlay: {
        fill: '#1e1e1e',
        useBackgroundColor: true,
    },

    gameOverTitle: {
        text: 'Game Over!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverMessage: {
        text: 'Better luck next time!',
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 22,
            fill: '#776e65',
            isVisible: true,
        },
    },

    gameOverContinueButton: {
        textConfig: {
            text: 'Continue',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 120,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    gameOverOverlay: {
        fill: '#1e1e1e',
        useBackgroundColor: true,
    },

    rewardTitle: {
        text: "You've earned a reward!",
        style: {
            fontFamily_tFontFamily: 'Londrina Solid',
            fontSize: 28,
            fill: '#f8d943',
            isVisible: true,
        },
    },

    rewardClaimButton: {
        textConfig: {
            text: 'Claim Reward',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        fill: '#8f7a66',
        width: 140,
        height: 40,
        useBackgroundColor: true,
        borderRadius: 4,
    },

    rewardOverlay: {
        fill: '#1e1e1e',
        useBackgroundColor: true,
    },

    rewardComponent: {
        imageWidth: 150,
        imageHeight: 150,
        titleFontFamily: 'Londrina Solid',
        titleFontSize: 22,
        titleColor: '#f8d943',
        titleTextAlign: 'center',
        titleFontWeight: 'bold',
        descriptionFontFamily: 'Poppins',
        descriptionFontSize: 16,
        descriptionColor: '#f9f6f2',
        descriptionTextAlign: 'center',
        padding: 16,
        maxWidth: 400,
        layout: 'vertical',
        imageBorderRadius: 8,
        imageMargin: 16,
        containerBackgroundColor: 'transparent',
        containerBorderRadius: 12,
        containerShadow: false,
        spacing: 16,
        showImage: true,
        showTitle: true,
        showDescription: true,
    },

    loseLifeContinueButton: {
        textConfig: {
            text: 'Continue',
            style: {
                fontFamily_tFontFamily: 'Londrina Solid',
                fontSize: 18,
                fill: '#ffffff',
                isVisible: true,
            },
        },
        isVisible: true
    },
}
