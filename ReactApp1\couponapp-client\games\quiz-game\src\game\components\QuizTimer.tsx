import React, { useEffect, useState, useRef, memo } from 'react'
import { motion } from 'framer-motion'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { ReactGameConfig } from '../../types/config'

interface QuizTimerProps {
    onTimeExpired: () => void
    questionKey: number // To reset timer when question changes
    isPaused?: boolean // To pause the timer when an answer is selected
}

export const QuizTimer: React.FC<QuizTimerProps> = memo(({ onTimeExpired, questionKey, isPaused = false }) => {
    const { config, isPreview } = useGame<ReactGameConfig>()
    const timerDuration = config.gameTimerHandler?.timerDuration || 30
    const useTimer = config.gameTimerHandler?.useTimer || false
    const progressColor = config.gameTimerStyle?.progressColor || '#22c55e'
    const backgroundColor = config.gameTimerStyle?.backgroundColor || '#4338ca'

    const [timeRemaining, setTimeRemaining] = useState(timerDuration)
    const intervalRef = useRef<NodeJS.Timeout | null>(null)

    // Clear interval helper
    const clearTimer = () => {
        if (intervalRef.current) {
            clearInterval(intervalRef.current)
            intervalRef.current = null
        }
    }

    // Reset timer when question changes
    useEffect(() => {
        if (isPreview) {
            // In preview mode, show timer at half time
            setTimeRemaining(timerDuration / 2)
        } else {
            setTimeRemaining(timerDuration)
        }
        clearTimer()
    }, [questionKey, timerDuration, isPreview])

    // Handle timer logic
    useEffect(() => {
        if (!useTimer || isPaused || timeRemaining <= 0 || isPreview) {
            clearTimer()
            return
        }

        intervalRef.current = setInterval(() => {
            setTimeRemaining((prev) => {
                const newTime = prev - 0.1
                if (newTime <= 0) {
                    clearTimer()
                    onTimeExpired()
                    return 0
                }
                return newTime
            })
        }, 100)

        return clearTimer
    }, [isPreview, useTimer, isPaused, timeRemaining, onTimeExpired])

    // Don't render if timer is not enabled
    if (!useTimer) {
        return null
    }

    const progressPercentage = Math.max(0, (timeRemaining / timerDuration) * 100)

    return (
        <div
            className="w-2/4 h-2 rounded-full overflow-hidden"
            style={{ backgroundColor }}
            data-editor-selectable-key="gameTimerStyle"
        >
            <motion.div
                className="h-full rounded-full transition-all duration-100 ease-linear"
                style={{
                    width: `${progressPercentage}%`,
                    backgroundColor: progressColor
                }}
            />
        </div>
    )
})
