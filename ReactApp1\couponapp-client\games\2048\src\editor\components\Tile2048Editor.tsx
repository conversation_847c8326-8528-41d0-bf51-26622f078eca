import React from 'react'
import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { Input } from '@repo/shared/components/ui/input'
import { FontPicker } from '@repo/shared/components/editor/fontPicker'
import { useConfigKeyDefinition } from '@repo/shared-game-utils/hooks/useConfigType'

interface Tile2048EditorProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
}

export function Tile2048Editor({ configKey, config, onChange }: Tile2048EditorProps) {
    const definition = useConfigKeyDefinition(configKey)

    return (
        <div className="space-y-4">
            <Label>
                {definition?.name ?? configKey.toString()}{' '}
                {definition?.width && definition?.height && (
                    <span className="ms-[2px] text-sm text-muted-foreground mb-2">
                        ({definition.width}x{definition.height}px)
                    </span>
                )}
            </Label>

            <div>
                <Label className="mb-2 block">Background Image</Label>
                <AssetPicker
                    exactSize={definition?.width && definition?.height ? { width: definition.width, height: definition.height } : undefined}
                    onSelect={(assetId) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                asset: { assetId },
                            },
                        })
                    }
                    assetUrl={config[configKey] as any}
                    extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']}
                />
            </div>

            <div>
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Background Color</Label>
                    <Switch
                        checked={(config[configKey] as any)?.useBackgroundColor !== false}
                        onCheckedChange={(useBackgroundColor) =>
                            onChange({
                                [configKey]: { ...config[configKey], useBackgroundColor },
                            })
                        }
                    />
                </div>
                <div className={`transition-opacity ${(config[configKey] as any)?.useBackgroundColor !== false ? 'opacity-100' : 'opacity-40'}`}>
                    <ColorPicker
                        color={(config[configKey] as any)?.fill || '#ffffff'}
                        onChange={(backgroundColor) =>
                            onChange({
                                [configKey]: {
                                    ...config[configKey],
                                    fill: backgroundColor,
                                },
                            })
                        }
                    />
                </div>
            </div>

            <div>
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Text Color</Label>
                    <Switch
                        checked={(config[configKey] as any)?.showText !== false}
                        onCheckedChange={(showText) =>
                            onChange({
                                [configKey]: { ...config[configKey], showText },
                            })
                        }
                    />
                </div>
                <div className={`transition-opacity ${(config[configKey] as any)?.showText !== false ? 'opacity-100' : 'opacity-40'}`}>
                    <ColorPicker
                        color={(config[configKey] as any)?.textColor || '#000000'}
                        onChange={(textColor) =>
                            onChange({
                                [configKey]: {
                                    ...config[configKey],
                                    textColor: textColor,
                                },
                            })
                        }
                    />
                </div>
            </div>

            <div className={`transition-opacity ${(config[configKey] as any)?.showText !== false ? 'opacity-100' : 'opacity-40'}`}>
                <Label className="mb-2 block">Font Family</Label>
                <FontPicker
                    font={(config[configKey] as any)?.fontFamily_tFontFamily || 'Londrina Solid'}
                    onChange={(fontFamily) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                fontFamily_tFontFamily: fontFamily,
                            },
                        })
                    }
                />
            </div>

            <div className={`transition-opacity ${(config[configKey] as any)?.showText !== false ? 'opacity-100' : 'opacity-40'}`}>
                <Label className="mb-2 block">Font Size</Label>
                <Input
                    type="number"
                    value={(config[configKey] as any)?.fontSize || 22}
                    onChange={(e) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                fontSize: parseInt(e.target.value, 10),
                            },
                        })
                    }
                />
            </div>
        </div>
    )
} 