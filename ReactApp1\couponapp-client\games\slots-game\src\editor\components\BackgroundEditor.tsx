import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import { Input } from '@repo/shared/components/ui/input'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { useConfigKeyDefinition } from '@repo/shared-game-utils/hooks/useConfigType'

interface BackgroundEditorProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
}

export function BackgroundEditor({ configKey, config, onChange }: BackgroundEditorProps) {
    const definition = useConfigKeyDefinition(configKey)
    
    return (
        <div className="space-y-4">
            <div>
                <AssetPicker
                    exactSize={definition?.width && definition?.height ? { width: definition.width, height: definition.height } : undefined}
                    onSelect={(assetId) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                asset: { assetId },
                            },
                        })
                    }
                    assetUrl={config[configKey] as any}
                    extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']}
                />
            </div>

            <div>
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Background Color</Label>
                    <Switch
                        checked={(config[configKey] as any)?.useBackgroundColor !== false}
                        onCheckedChange={(useBackgroundColor) =>
                            onChange({
                                [configKey]: { ...config[configKey], useBackgroundColor },
                            })
                        }
                    />
                </div>
                <div className={`transition-opacity ${(config[configKey] as any)?.useBackgroundColor !== false ? 'opacity-100' : 'opacity-40'}`}>
                    <ColorPicker
                        color={(config[configKey] as any)?.fill || '#ffffff'}
                        onChange={(backgroundColor) =>
                            onChange({
                                [configKey]: {
                                    ...config[configKey],
                                    fill: backgroundColor,
                                },
                            })
                        }
                    />
                </div>
            </div>

            <div>
                <Label className="mb-2 block">Dimensions</Label>
                <div className="grid grid-cols-2 gap-2">
                    <div>
                        <Label className="text-xs">Width</Label>
                        <Input
                            type="number"
                            value={(config[configKey] as any)?.width || 400}
                            onChange={(e) =>
                                onChange({
                                    [configKey]: {
                                        ...config[configKey],
                                        width: parseInt(e.target.value, 10),
                                    },
                                })
                            }
                        />
                    </div>
                    <div>
                        <Label className="text-xs">Height</Label>
                        <Input
                            type="number"
                            value={(config[configKey] as any)?.height || 500}
                            onChange={(e) =>
                                onChange({
                                    [configKey]: {
                                        ...config[configKey],
                                        height: parseInt(e.target.value, 10),
                                    },
                                })
                            }
                        />
                    </div>
                </div>
            </div>

            {/* Add grid settings for slots background */}
            {configKey === 'slotsBackground' && (
                <div>
                    <Label className="mb-2 block">Grid Settings (3x3)</Label>
                    <div className="grid grid-cols-2 gap-2">
                        <div>
                            <Label className="text-xs">Tile Spacing</Label>
                            <Input
                                type="number"
                                value={(config[configKey] as any)?.gridSettings?.tileSpacing ?? 0}
                                onChange={(e) =>
                                    onChange({
                                        [configKey]: {
                                            ...config[configKey],
                                            gridSettings: {
                                                ...(config[configKey] as any)?.gridSettings,
                                                tileSpacing: parseInt(e.target.value, 10),
                                            },
                                        },
                                    })
                                }
                            />
                        </div>
                        <div>
                            <Label className="text-xs">Container Padding</Label>
                            <Input
                                type="number"
                                value={(config[configKey] as any)?.gridSettings?.containerPadding ?? 20}
                                onChange={(e) =>
                                    onChange({
                                        [configKey]: {
                                            ...config[configKey],
                                            gridSettings: {
                                                ...(config[configKey] as any)?.gridSettings,
                                                containerPadding: parseInt(e.target.value, 10),
                                            },
                                        },
                                    })
                                }
                            />
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
} 