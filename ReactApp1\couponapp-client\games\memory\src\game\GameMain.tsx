import { TryAgainScreen, RewardScreen, OutOfLivesScreen } from './Scenes'
import { GameRuntimeComponentProps } from '@repo/shared/lib/game/game'
import { useCallback, useEffect, useRef, useState } from 'react'
import { gameEvents } from '@repo/shared/lib/campaign/eventEmitter'
import { useGameAtom } from '@repo/shared/lib/atoms/atom-extensions'
import { defaultGameConfig, ReactGameConfig } from '../types/config'
import { GameScreenId } from '../types/screen'
import { MemoryGrid } from './components/MemoryGrid'
import { MemoryCard } from './components/MemoryCard'
import { useGameRewards } from '@repo/shared/lib/game/useGameRewards'
import { GameTimer, useTimerControls } from './components/GameTimer'
import { CounterElement } from '@repo/shared-game-utils/components/CounterElement'
import { GameSoundSwitch } from '@repo/shared-game-utils/components/GameSoundSwitch'
import { GameContext, useGame } from '@repo/shared-game-utils/hooks/useGame'
import { useMusic, useSoundEffect } from '@repo/shared-game-utils/hooks/useSounds'
import { usePreloadImage } from '@repo/shared-game-utils/hooks/usePreloadImage'
import { getBackgroundStyle } from '@repo/shared-game-utils/utils/gameStyleUtils'
import { makeElementsInteractive } from '@repo/shared-game-utils/utils/previewInteractive'

export type GameRuntimeProps = GameRuntimeComponentProps<ReactGameConfig> & {
    currentScreenId?: GameScreenId
    initialGameScreenChecked: React.MutableRefObject<boolean>
}

export default function MainGame(props: GameRuntimeProps) {
    const [currentScreenId, setCurrentScreenId] = useState<GameScreenId>(props.currentScreenId || 'main')
    const initialGameScreenChecked = useRef(false)

    // Create an enhanced context with both props and screen state
    const contextValue = {
        ...props,
        currentScreenId,
        setCurrentScreenId,
        initialGameScreenChecked,
        defaultConfig: defaultGameConfig,
    }

    useEffect(() => {
        if (!props.isPreview) return
        setCurrentScreenId(props.currentScreenId || 'main')
    }, [props.currentScreenId, props.isPreview])

    return (
        <GameContext.Provider value={contextValue}>
                <GameContent />
        </GameContext.Provider>
    )
}

export const useGameState = () => {
    const { config, widgetId } = useGame<ReactGameConfig>()
    const initialLivesCount = config.gameEndHandler?.useLives ? config.gameEndHandler?.livesCount || 3 : Number.MAX_SAFE_INTEGER

    const [lives, setLivesCount] = useGameAtom(widgetId, 'livesCount', initialLivesCount)
    const [attemptsTaken, setAttemptsaken] = useGameAtom(widgetId, 'attemptsTaken', 0)

    return {
        lives,
        setLivesCount,
        attemptsTaken,
        setAttemptsaken,
    }
}

function usePrepareAssets() {
    const { config } = useGame<ReactGameConfig>()

    // Preload sounds with appropriate layers
    useSoundEffect(config.backgroundMusic)
    useSoundEffect(config.gameOverSound)
    useSoundEffect(config.winSound)
    useSoundEffect(config.cardFlipSound)
    useSoundEffect(config.cardMatchSound)
    useSoundEffect(config.cardMismatchSound)

    // Preload images
    usePreloadImage(config.gameOverOverlay?.asset)
    usePreloadImage(config.rewardOverlay?.asset)
    usePreloadImage(config.outOfLivesOverlay?.asset)
    usePreloadImage(config.gameSoundSwitch?.onAsset)
    usePreloadImage(config.gameSoundSwitch?.offAsset)
    usePreloadImage(config.livesStyle?.asset)

    // Preload memory card assets
    usePreloadImage(config.memoryCardBack?.asset)
    usePreloadImage(config.memoryCard1?.asset)
    usePreloadImage(config.memoryCard2?.asset)
    usePreloadImage(config.memoryCard3?.asset)
    usePreloadImage(config.memoryCard4?.asset)
    usePreloadImage(config.memoryCard5?.asset)
    usePreloadImage(config.memoryCard6?.asset)
    usePreloadImage(config.memoryCard7?.asset)
    usePreloadImage(config.memoryCard8?.asset)
    usePreloadImage(config.memoryCard9?.asset)
    usePreloadImage(config.memoryCard10?.asset)
    usePreloadImage(config.memoryCard11?.asset)
    usePreloadImage(config.memoryCard12?.asset)
    usePreloadImage(config.memoryCard13?.asset)
}

function GameContent() {
    const { config, widgetId, resolveAssetUrl, isPreview, currentScreenId, setCurrentScreenId, onGameAssetSelect } = useGame<ReactGameConfig>()

    // Preload assets
    usePrepareAssets()

    useMusic(config.backgroundMusic, true, true)

    // Create a ref for the container
    const containerRef = useRef<HTMLDivElement>(null)

    // Function to handle try again button click
    const handleTryAgainClick = useCallback(() => {
        if (isPreview) return

        setCurrentScreenId('main')
    }, [isPreview, setCurrentScreenId])

    // Function to handle reward button click
    const handleRewardButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinishedWithReward', {
            score: 0,
            widgetId: widgetId,
        })
    }, [isPreview, widgetId])

    // // Function to handle out of lives button click
    const handleOutOfLivesButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinishedNoReward', {
            score: 0,
            widgetId: widgetId,
        })
    }, [isPreview, widgetId, setCurrentScreenId])

    useEffect(() => {
        if (!isPreview) return
        makeElementsInteractive(onGameAssetSelect)
    }, [currentScreenId, isPreview])

    return (
        <div
            data-game-widget-id={widgetId}
            ref={containerRef}
            className="flex flex-col items-center justify-center h-full w-full touch-none select-none overscroll-none p-4"
            style={getBackgroundStyle(config.mainBackground, resolveAssetUrl)}
        >
            <div className="relative w-full h-full text-black">
                {/* Main game screens with unrevealed or revealed cards */}
                {currentScreenId === 'main' && <MainGameScreen isPreviewMode={isPreview} />}
                {currentScreenId === 'main-preview' && <MainGameScreen isPreviewMode={true} />}

                {/* Other game screens */}
                {currentScreenId === 'try-again' && <TryAgainScreen onButtonClick={handleTryAgainClick} />}
                {currentScreenId === 'claim-reward' && <RewardScreen onButtonClick={handleRewardButtonClick} />}
                {currentScreenId === 'out-of-lives' && <OutOfLivesScreen onButtonClick={handleOutOfLivesButtonClick} />}
            </div>
        </div>
    )
}

interface MainGameScreenProps {
    isPreviewMode?: boolean
}

function MainGameScreen({ isPreviewMode = false }: MainGameScreenProps) {
    const { initialGameScreenChecked, config, widgetId, setCurrentScreenId, currentScreenId, isPreview } = useGame<ReactGameConfig>()
    const { lives, setLivesCount, attemptsTaken, setAttemptsaken } = useGameState()
    const { pickReward, hasWonReward } = useGameRewards({ gameWidgetId: widgetId, attempt: attemptsTaken })

    // Sound hooks
    const { play: playCardFlip } = useSoundEffect(config.cardFlipSound)
    const { play: playCardMatch } = useSoundEffect(config.cardMatchSound)
    const { play: playCardMismatch } = useSoundEffect(config.cardMismatchSound)

    // Timer controls
    const timerControls = useTimerControls()

    // Game state
    const [cards, setCards] = useState<Array<{ id: number; cardType: number; isFlipped: boolean; isMatched: boolean }>>([])
    const [flippedCards, setFlippedCards] = useState<number[]>([])
    const [matchedPairs, setMatchedPairs] = useState<number>(0)
    const [isChecking, setIsChecking] = useState<boolean>(false)
    const [gameCompleted, setGameCompleted] = useState<boolean>(false)
    const [movesCount, setMovesCount] = useState<number>(0)
    const [gameStarted, setGameStarted] = useState<boolean>(false)

    // Game settings
    const rewardsEnabled = config.gameRewardsHandler?.rewardsEnabled || false
    const useLives = config.gameEndHandler?.useLives

    // Grid configuration
    const columns = Math.min(Math.max(config.memoryGrid?.columns || 3, 2), 5)
    const rows = Math.min(Math.max(config.memoryGrid?.rows || 3, 2), 5)
    const gridSize = columns * rows

    // Ensure we have an even number of cells for pairs
    const adjustedGridSize = gridSize % 2 === 0 ? gridSize : gridSize - 1

    // Calculate total pairs (half of the adjusted grid size)
    const totalPairs = adjustedGridSize / 2

    // Initialize the game
    useEffect(() => {
        if (isPreviewMode) {
            setupPreviewMode()
        } else {
            initializeGame()
        }
    }, [isPreviewMode, currentScreenId, columns, rows])

    // Set up cards for preview mode
    const setupPreviewMode = () => {
        // Get available card types (we support up to 25 cards in a 5x5 grid)
        // We need at most 13 different card types (for 25 cards with pairs + 1 odd card if needed)
        const availableCardTypes = Array.from({ length: 13 }, (_, i) => i + 1)

        // Check if we need an odd card (when grid size is odd)
        const needsOddCard = adjustedGridSize < gridSize

        // Calculate how many pairs we need
        const pairsNeeded = totalPairs

        // Create an array to hold our arranged cards
        let arrangedCards: number[] = []

        // In preview mode, we want to place pairs next to each other
        // We'll take each card type and place two of them consecutively
        for (let i = 0; i < pairsNeeded; i++) {
            const cardType = availableCardTypes[i]
            // Add a pair of the same card type next to each other
            arrangedCards.push(cardType, cardType)
        }

        // Add one extra card if needed for odd grid size
        if (needsOddCard) {
            arrangedCards.push(availableCardTypes[pairsNeeded])
        }

        // Ensure we have exactly the right number of cards for the grid
        const gridCards = arrangedCards.slice(0, gridSize)

        // Check if we're in the "revealed cards" screen (main-preview)
        const isRevealedCardsMode = currentScreenId === 'main-preview'

        // For preview mode, we'll use our arranged cards with pairs next to each other
        const previewCards = gridCards.map((cardType, index) => ({
            id: index,
            cardType,
            // In "revealed cards" mode, all cards are revealed
            // In "unrevealed cards" mode, all cards are face down
            isFlipped: isRevealedCardsMode,
            isMatched: isRevealedCardsMode, // Matched cards stay flipped
        }))

        console.log(`Setting up preview mode with ${columns}x${rows} grid (${gridSize} cells, ${totalPairs} pairs)`)

        setCards(previewCards)
        setFlippedCards([])
        setMatchedPairs(0)
        setMovesCount(0)
        setGameCompleted(false)
    }

    // Initialize the game with shuffled cards
    const initializeGame = () => {
        // Get available card types (we support up to 25 cards in a 5x5 grid)
        // We need at most 13 different card types (for 25 cards with pairs + 1 odd card if needed)
        const availableCardTypes = Array.from({ length: 13 }, (_, i) => i + 1)

        // Check if we need an odd card (when grid size is odd)
        const needsOddCard = adjustedGridSize < gridSize

        // Calculate how many pairs we need
        const pairsNeeded = totalPairs

        // Create pairs for the required number of pairs
        const pairCardTypes = availableCardTypes.slice(0, pairsNeeded)
        const cardPairs = [...pairCardTypes, ...pairCardTypes]

        // Add one extra card if needed for odd grid size
        const allCards = needsOddCard ? [...cardPairs, availableCardTypes[pairsNeeded]] : cardPairs

        // Ensure we have exactly the right number of cards for the grid
        const gridCards = allCards.slice(0, gridSize)

        // Shuffle the cards
        const shuffledCards = shuffleArray(gridCards).map((cardType, index) => ({
            id: index,
            cardType,
            isFlipped: false,
            isMatched: false,
        }))

        setCards(shuffledCards)
        setFlippedCards([])
        setMatchedPairs(0)
        setMovesCount(0)
        setGameCompleted(false)
        setGameStarted(false)

        // Reset the timer if enabled, but don't start it yet
        if (config.gameTimerHandler?.useTimer && !isPreview && !isPreviewMode) {
            timerControls.resetTimer()
        }
    }

    // Shuffle array using Fisher-Yates algorithm
    const shuffleArray = (array: any[]) => {
        const newArray = [...array]
        for (let i = newArray.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1))
                ;[newArray[i], newArray[j]] = [newArray[j], newArray[i]]
        }
        return newArray
    }

    // Handle card click
    const handleCardClick = (id: number) => {
        if (isPreview || isPreviewMode) return
        if (isChecking || flippedCards.length >= 2 || cards[id].isFlipped || cards[id].isMatched) return

        // Start the timer on first card click if it's enabled and game hasn't started yet
        if (config.gameTimerHandler?.useTimer && !isPreview && !isPreviewMode && !gameStarted) {
            timerControls.startTimer()
            setGameStarted(true)
            setAttemptsaken((prev: number) => (prev ?? 0) + 1)
        }

        // Play card flip sound
        playCardFlip()

        // Flip the card
        const updatedCards = [...cards]
        updatedCards[id].isFlipped = true
        setCards(updatedCards)

        // Add to flipped cards
        const newFlippedCards = [...flippedCards, id]
        setFlippedCards(newFlippedCards)

        // Check for match if two cards are flipped
        if (newFlippedCards.length === 2) {
            setIsChecking(true)
            setMovesCount(movesCount + 1)

            // Check if the two flipped cards match
            const [firstCardId, secondCardId] = newFlippedCards
            const firstCard = updatedCards[firstCardId]
            const secondCard = updatedCards[secondCardId]

            if (firstCard.cardType === secondCard.cardType) {
                // Cards match
                setTimeout(() => {
                    const matchedCards = [...updatedCards]
                    matchedCards[firstCardId].isMatched = true
                    matchedCards[secondCardId].isMatched = true
                    setCards(matchedCards)
                    setFlippedCards([])
                    setMatchedPairs(matchedPairs + 1)
                    setIsChecking(false)

                    // Play match sound
                    playCardMatch()

                    // Check if game is completed
                    if (matchedPairs + 1 === totalPairs) {
                        handleGameCompleted()
                    }
                }, 500)
            } else {
                // Cards don't match
                setTimeout(() => {
                    const unmatchedCards = [...updatedCards]
                    unmatchedCards[firstCardId].isFlipped = false
                    unmatchedCards[secondCardId].isFlipped = false
                    setCards(unmatchedCards)
                    setFlippedCards([])
                    setIsChecking(false)

                    // Play mismatch sound
                    playCardMismatch()
                }, 1000)
            }
        }
    }

    // Handle game completion
    const handleGameCompleted = async () => {
        setGameCompleted(true)
        setGameStarted(false)



        // Pause the timer if it's enabled
        if (config.gameTimerHandler?.useTimer && !isPreview && !isPreviewMode) {
            timerControls.pauseTimer()
        }

        if (rewardsEnabled) {
            const pickResult = await pickReward()

            if (useLives && lives <= 0) {
                if (pickResult.hasWon) {
                    setCurrentScreenId('claim-reward')
                } else {
                    setCurrentScreenId('out-of-lives')
                }
                return
            }

            if (pickResult.hasWon) {
                setCurrentScreenId('claim-reward')
                return
            } else {
                setCurrentScreenId('try-again')
                return
            }
        } else {
            if (useLives) {
                if (lives > 0) {
                    setCurrentScreenId('try-again')
                } else {
                    setCurrentScreenId('out-of-lives')
                }
            } else {
                setCurrentScreenId('try-again')
            }
        }
    }

    // Check initial game screen
    const checkInitialGameScreen = useCallback(
        (lives: number) => {
            if (isPreview) return

            // First check if we have a reward to claim
            if (hasWonReward) {
                // Always show claim reward screen if there's a reward, regardless of lives
                setCurrentScreenId('claim-reward')
                return
            }

            // Then check if we have out of lives condition
            if (useLives && lives <= 0) {
                // Show out of lives screen
                setCurrentScreenId('out-of-lives')
                return
            }

            // If we reach here, we don't have a reward and we have lives
            // Just show the main game screen
            setCurrentScreenId('main')
            return
        },
        [useLives, lives, setCurrentScreenId, isPreview, hasWonReward]
    )

    const handleLoseLife = useCallback(async () => {
        if (isPreview) return

        // Decrement lives count
        setLivesCount(lives - 1)

        // Increment attempts taken
        setAttemptsaken((prev: number) => prev + 1)

        // Calculate new lives count after decrement
        const newLivesCount = lives - 1

        // If rewards are enabled, pick a reward
        if (rewardsEnabled) {
            const pickResult = await pickReward()

            // First check if we have out of lives condition
            if (useLives && newLivesCount <= 0) {
                if (pickResult.hasWon) {
                    // If rewards enabled and won, show claim reward
                    setCurrentScreenId('claim-reward')
                } else {
                    // Otherwise show out of lives
                    setCurrentScreenId('out-of-lives')
                }
                return
            }

            // Then check if we have a reward to claim
            if (pickResult.hasWon) {
                // Show claim reward screen if won
                setCurrentScreenId('claim-reward')
                return
            } else {
                // Show try again if didn't win
                setCurrentScreenId('try-again')
                return
            }
        } else {
            // No rewards enabled

            // Check if we have out of lives condition
            if (useLives && newLivesCount <= 0) {
                // Show out of lives screen
                setCurrentScreenId('out-of-lives')
                return
            }

            // If we reach here, we don't have a reward and we have lives
            // Show try again screen
            setCurrentScreenId('try-again')
            return
        }
    }, [rewardsEnabled, useLives, lives, setLivesCount, isPreview, setCurrentScreenId, pickReward])

    useEffect(() => {
        if (isPreview || isPreviewMode) return
        if (initialGameScreenChecked.current) return
        console.log('checking initial game screen')
        initialGameScreenChecked.current = true
        checkInitialGameScreen(lives)
    }, [lives, isPreviewMode])

    // Get card configuration based on card type
    const getCardConfig = (cardType: number) => {
        // Try to get the specific card config
        const cardConfig = config[`memoryCard${cardType}`]

        // If the specific card config exists, return it
        if (cardConfig) {
            return cardConfig
        }

        // Otherwise, use one of the existing card configs based on the card type
        // This ensures we don't crash if we have more cards than configurations
        const fallbackCardType = ((cardType - 1) % 5) + 1

        switch (fallbackCardType) {
            case 1:
                return config.memoryCard1
            case 2:
                return config.memoryCard2
            case 3:
                return config.memoryCard3
            case 4:
                return config.memoryCard4
            case 5:
                return config.memoryCard5
            default:
                return config.memoryCard1
        }
    }

    return (
        <div className="game-content-area w-full h-full flex flex-col">
            {/* Top section with sound switch, timer and lives counter */}
            <div className="w-full flex justify-between items-center p-2">
                <div className="flex items-center space-x-2">
                    {/* Lives counter - only shown when lives system is enabled */}
                    {useLives && (
                        <div className="flex items-center">
                            <CounterElement configKey="livesStyle" style={config.livesStyle}>
                                {lives}
                            </CounterElement>
                        </div>
                    )}

                    {/* Timer - only shown when timer is enabled */}
                    {(config.gameTimerHandler?.useTimer || isPreviewMode) && (
                        <div className="flex items-center ml-2">
                            <GameTimer onTimeExpired={handleLoseLife} />
                        </div>
                    )}


                </div>

                {/* Sound switch */}
                <div className={`flex ${config.gameSoundSwitch?.alignment === 'center' ? 'justify-center' : config.gameSoundSwitch?.alignment === 'right' ? 'justify-end' : 'justify-start'}`}>
                    <GameSoundSwitch config={config.gameSoundSwitch} dataConfigKey="gameSoundSwitch" />
                </div>
            </div>

            {/* Memory Game Grid */}
            <div className="flex-grow flex items-center justify-center">
                <MemoryGrid dataConfigKey="memoryGrid">
                    {cards.map((card) => (
                        <MemoryCard
                            key={card.id}
                            id={card.id}
                            cardIndex={card.id}
                            frontConfig={getCardConfig(card.cardType)}
                            backConfig={config.memoryCardBack}
                            isFlipped={card.isFlipped}
                            isMatched={card.isMatched}
                            onClick={() => handleCardClick(card.id)}
                            dataConfigKey={`memoryCard${card.cardType}`}
                        />
                    ))}
                </MemoryGrid>
            </div>
        </div>
    )
}
