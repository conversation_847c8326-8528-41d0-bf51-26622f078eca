import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { Input } from '@repo/shared/components/ui/input'
import { Slider } from '@repo/shared/components/ui/slider'
import { ButtonIconStyleEditor } from './ButtonIconStyleEditor'
import { useConfigKeyDefinition } from '../hooks/useConfigType'
import { TextEditor } from './TextEditor'
interface GameButtonEditorProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
}

export function GameButtonEditor({ configKey, config, onChange }: GameButtonEditorProps) {
        const definition = useConfigKeyDefinition(configKey)

    return (
        <div className="space-y-4">
            {definition?.editorSettings?.toggleableVisibility && (
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Visibility</Label>
                    <Switch
                        checked={(config[configKey] as any)?.isVisible !== false}
                        onCheckedChange={(isVisible) =>
                            onChange({
                                [configKey]: { ...config[configKey], isVisible },
                            })
                        }
                    />
                </div>
            )}
            <div>
                <TextEditor
                    text={
                        (config[configKey] as any)?.textConfig || {
                            text: '',
                            style: {},
                        }
                    }
                    onChange={(textConfig) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                textConfig,
                            },
                        })
                    }
                    configKey={String(configKey) + '.textConfig'}
                />
            </div>

            <ButtonIconStyleEditor
                config={(config[configKey] as any)?.iconStyle || {}}
                onChange={(iconStyle) =>
                    onChange({
                        [configKey]: {
                            ...config[configKey],
                            iconStyle,
                        },
                    })
                }
            />

            <div>
                <Label className="mb-2 block">Background Image</Label>
                <AssetPicker
                    onSelect={(assetId) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                asset: { assetId },
                            },
                        })
                    }
                    assetUrl={(config[configKey] as any)?.asset}
                    extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']}
                />

                {(config[configKey] as any)?.asset && (
                    <div className="mt-2">
                        <div className="flex items-center space-x-2 mb-2">
                            <Label className="flex-grow">Pixel Art Mode</Label>
                            <Switch
                                checked={(config[configKey] as any)?.backgroundScaleMode === 'pixelated-cover'}
                                onCheckedChange={(isPixelArt) =>
                                    onChange({
                                        [configKey]: {
                                            ...config[configKey],
                                            backgroundScaleMode: isPixelArt ? 'pixelated-cover' : 'cover',
                                        },
                                    })
                                }
                            />
                        </div>
                    </div>
                )}
            </div>
            <div>
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Background Color</Label>
                    <Switch
                        checked={(config[configKey] as any)?.useBackgroundColor !== false}
                        onCheckedChange={(useBackgroundColor) =>
                            onChange({
                                [configKey]: { ...config[configKey], useBackgroundColor },
                            })
                        }
                    />
                </div>
                <div className={`transition-opacity ${(config[configKey] as any)?.useBackgroundColor !== false ? 'opacity-100' : 'opacity-40'}`}>
                    <ColorPicker
                        color={(config[configKey] as any)?.fill || '#8f7a66'}
                        onChange={(fill) =>
                            onChange({
                                [configKey]: {
                                    ...config[configKey],
                                    fill,
                                },
                            })
                        }
                    />
                </div>
            </div>
            <div>
                <Label className="mb-2 block">Dimensions</Label>
                <div className="grid grid-cols-2 gap-2">
                    <div>
                        <Label className="text-xs">Width</Label>
                        <Input
                            type="number"
                            value={(config[configKey] as any)?.width || 120}
                            onChange={(e) =>
                                onChange({
                                    [configKey]: {
                                        ...config[configKey],
                                        width: parseInt(e.target.value, 10),
                                    },
                                })
                            }
                        />
                    </div>
                    <div>
                        <Label className="text-xs">Height</Label>
                        <Input
                            type="number"
                            value={(config[configKey] as any)?.height || 40}
                            onChange={(e) =>
                                onChange({
                                    [configKey]: {
                                        ...config[configKey],
                                        height: parseInt(e.target.value, 10),
                                    },
                                })
                            }
                        />
                    </div>
                </div>
            </div>
            <div>
                <Label className="mb-2 block">Offset X</Label>
                <div className="flex justify-between items-center mb-2">
                    <span className="text-sm">{(config[configKey] as any)?.offsetX || 0}px</span>
                </div>
                <Slider
                    value={[(config[configKey] as any)?.offsetX || 0]}
                    min={-250}
                    max={250}
                    step={1}
                    onValueChange={(values) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                offsetX: values[0],
                            },
                        })
                    }
                />
            </div>
            <div>
                <Label className="mb-2 block">Offset Y</Label>
                <div className="flex justify-between items-center mb-2">
                    <span className="text-sm">{(config[configKey] as any)?.offsetY || 0}px</span>
                </div>
                <Slider
                    value={[(config[configKey] as any)?.offsetY || 0]}
                    min={-250}
                    max={250}
                    step={1}
                    onValueChange={(values) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                offsetY: values[0],
                            },
                        })
                    }
                />
            </div>
            <div>
                <Label className="mb-2 block">Border Radius</Label>
                <div className="flex justify-between items-center mb-2">
                    <span className="text-sm">{(config[configKey] as any)?.borderRadius || 4}px</span>
                </div>
                <Slider
                    value={[(config[configKey] as any)?.borderRadius || 4]}
                    min={0}
                    max={50}
                    step={1}
                    onValueChange={(values) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                borderRadius: values[0],
                            },
                        })
                    }
                />
            </div>
            <div>
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Pixelated</Label>
                    <Switch
                        checked={(config[configKey] as any)?.pixelated === true}
                        onCheckedChange={(pixelated) =>
                            onChange({
                                [configKey]: { ...config[configKey], pixelated },
                            })
                        }
                    />
                </div>
            </div>
        </div>
    )
}
