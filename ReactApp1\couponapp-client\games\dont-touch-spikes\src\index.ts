import MainGame from './game/GameMain'
import { ConfigKeyEditor } from './editor/ConfigKeyEditor'
import MainConfigEditor from './editor/MainConfigEditor'
import { PreviewScene } from './game/Scenes'
import { defaultGameConfig, ReactGameConfig } from './types/config'
import { GameScreenId } from './types/screen'
import { GameModule, PreviewScreenDefinition } from '@repo/shared/lib/game/game'

// Preview screen definitions for the game
const previewScreens: PreviewScreenDefinition[] = [
    {
        screenId: 'main' as GameScreenId,
        displayName: 'Main Game Screen',
    },
    {
        screenId: 'try-again' as GameScreenId,
        displayName: 'Try again',
    },
    {
        screenId: 'claim-reward' as GameScreenId,
        displayName: 'Claim Reward',
        visibleCheck: (config) => config.gameRewardsHandler?.rewardsEnabled === true,
    },
    {
        screenId: 'out-of-lives' as <PERSON>ScreenId,
        displayName: 'Game over (out of lives)',
        visibleCheck: (config) => config.gameEndHandler?.useLives === true,
    },
]

const DontTouchSpikesGame: GameModule = {
    id: 'dont-touch-spikes',
    name: 'Dont Touch Spikes',
    runtimeComponent: MainGame,
    editorComponent: MainConfigEditor,
    configKeyEditor: ConfigKeyEditor,
    defaultConfig: defaultGameConfig,
    previewScene: PreviewScene,
    previewScreens: previewScreens,
    configType: ReactGameConfig,
}

export default DontTouchSpikesGame

export { MainGame, MainConfigEditor }
