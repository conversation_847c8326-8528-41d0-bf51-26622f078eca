import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { Input } from '@repo/shared/components/ui/input'
import { FontPicker } from '@repo/shared/components/editor/fontPicker'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'

interface RewardComponentStyleEditorProps {
    config: any
    onChange: (changes: any) => void
}

export function RewardComponentStyleEditor({ config, onChange }: RewardComponentStyleEditorProps) {
    return (
        <div className="space-y-6">
            {/* Layout Section */}
            <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-3">Layout</h4>
                <div className="space-y-4">
                    <div>
                        <Label className="mb-2 block">Layout Style</Label>
                        <Select value={config?.layout || 'vertical'} onValueChange={(value) => onChange({ layout: value })}>
                            <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select layout" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="vertical">Vertical (image on top)</SelectItem>
                                <SelectItem value="horizontal-left">Horizontal (image on left)</SelectItem>
                                <SelectItem value="horizontal-right">Horizontal (image on right)</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Label className="mb-2 block">Spacing & Dimensions</Label>
                        <div className="grid grid-cols-2 gap-2">
                            <div>
                                <Label className="text-xs">Inner Spacing (px)</Label>
                                <Input type="number" value={config?.spacing || 16} onChange={(e) => onChange({ spacing: parseInt(e.target.value, 10) })} />
                            </div>
                            <div>
                                <Label className="text-xs">Padding (px)</Label>
                                <Input type="number" value={config?.padding || 16} onChange={(e) => onChange({ padding: parseInt(e.target.value, 10) })} />
                            </div>
                        </div>
                        <div className="mt-2">
                            <Label className="text-xs">Max Width (px)</Label>
                            <Input type="number" value={config?.maxWidth || 400} onChange={(e) => onChange({ maxWidth: parseInt(e.target.value, 10) })} />
                        </div>
                    </div>
                </div>
            </div>

            {/* Container Settings */}
            <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-3">Container</h4>
                <div className="space-y-4">
                    <div>
                        <Label className="text-xs">Background Color</Label>
                        <ColorPicker color={config?.containerBackgroundColor || 'rgba(255, 255, 255, 0.9)'} onChange={(containerBackgroundColor) => onChange({ containerBackgroundColor })} />
                    </div>
                    <div>
                        <Label className="text-xs">Border Radius (px)</Label>
                        <Input type="number" value={config?.containerBorderRadius || 12} onChange={(e) => onChange({ containerBorderRadius: parseInt(e.target.value, 10) })} />
                    </div>
                    <div className="flex items-center space-x-2">
                        <Switch id="container-pixelated" checked={config?.containerPixelated === true} onCheckedChange={(containerPixelated) => onChange({ containerPixelated })} />
                        <Label htmlFor="container-pixelated">Pixelated Container Border</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Switch id="container-shadow" checked={config?.containerShadow || false} onCheckedChange={(containerShadow) => onChange({ containerShadow })} />
                        <Label htmlFor="container-shadow">Enable Shadow</Label>
                    </div>
                </div>
            </div>

            {/* Image Settings */}
            <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-3">Image</h4>
                <div className="space-y-4">
                    <div className="flex items-center space-x-2 mb-3">
                        <Switch id="show-image" checked={config?.showImage !== false} onCheckedChange={(showImage) => onChange({ showImage })} />
                        <Label htmlFor="show-image">Show Image</Label>
                    </div>
                    <div className={`transition-opacity ${config?.showImage !== false ? 'opacity-100' : 'opacity-40'}`}>
                        <div className="grid grid-cols-2 gap-2">
                            <div>
                                <Label className="text-xs">Width (px)</Label>
                                <Input type="number" value={config?.imageWidth || 150} onChange={(e) => onChange({ imageWidth: parseInt(e.target.value, 10) })} />
                            </div>
                            <div>
                                <Label className="text-xs">Height (px)</Label>
                                <Input type="number" value={config?.imageHeight || 150} onChange={(e) => onChange({ imageHeight: parseInt(e.target.value, 10) })} />
                            </div>
                        </div>
                        <div>
                            <Label className="text-xs">Border Radius (px)</Label>
                            <Input type="number" value={config?.imageBorderRadius || 8} onChange={(e) => onChange({ imageBorderRadius: parseInt(e.target.value, 10) })} />
                        </div>
                        <div className="flex items-center space-x-2">
                            <Switch id="image-pixelated" checked={config?.imagePixelated === true} onCheckedChange={(imagePixelated) => onChange({ imagePixelated })} />
                            <Label htmlFor="image-pixelated">Pixelated Image Border</Label>
                        </div>
                        <div>
                            <Label className="text-xs">Margin (px)</Label>
                            <Input type="number" value={config?.imageMargin || 16} onChange={(e) => onChange({ imageMargin: parseInt(e.target.value, 10) })} />
                        </div>
                    </div>
                </div>
            </div>

            {/* Typography Section */}
            <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-3">Typography</h4>

                {/* Title Style */}
                <div className="mb-4">
                    <h5 className="text-sm font-medium mb-2">Title</h5>
                    <div className="space-y-3 pl-2">
                        <div className="flex items-center space-x-2 mb-3">
                            <Switch id="show-title" checked={config?.showTitle !== false} onCheckedChange={(showTitle) => onChange({ showTitle })} />
                            <Label htmlFor="show-title">Show Title</Label>
                        </div>
                        <div className={`transition-opacity ${config?.showTitle !== false ? 'opacity-100' : 'opacity-40'}`}>
                            <div>
                                <Label className="text-xs">Font Family</Label>
                                <FontPicker font={config?.titleFontFamily || 'Londrina Solid'} onChange={(titleFontFamily) => onChange({ titleFontFamily })} />
                            </div>
                            <div>
                                <Label className="text-xs">Font Size (px)</Label>
                                <Input type="number" value={config?.titleFontSize || 22} onChange={(e) => onChange({ titleFontSize: parseInt(e.target.value, 10) })} />
                            </div>
                            <div>
                                <Label className="text-xs">Color</Label>
                                <ColorPicker color={config?.titleColor || '#776e65'} onChange={(titleColor) => onChange({ titleColor })} />
                            </div>
                            <div>
                                <Label className="text-xs">Text Alignment</Label>
                                <Select value={config?.titleTextAlign || 'left'} onValueChange={(titleTextAlign) => onChange({ titleTextAlign })}>
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Select alignment" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="left">Left</SelectItem>
                                        <SelectItem value="center">Center</SelectItem>
                                        <SelectItem value="right">Right</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Description Style */}
                <div>
                    <h5 className="text-sm font-medium mb-2">Description</h5>
                    <div className="space-y-3 pl-2">
                        <div className="flex items-center space-x-2 mb-3">
                            <Switch id="show-description" checked={config?.showDescription !== false} onCheckedChange={(showDescription) => onChange({ showDescription })} />
                            <Label htmlFor="show-description">Show Description</Label>
                        </div>
                        <div className={`transition-opacity ${config?.showDescription !== false ? 'opacity-100' : 'opacity-40'}`}>
                            <div>
                                <Label className="text-xs">Font Family</Label>
                                <FontPicker font={config?.descriptionFontFamily || 'Poppins'} onChange={(descriptionFontFamily) => onChange({ descriptionFontFamily })} />
                            </div>
                            <div>
                                <Label className="text-xs">Font Size (px)</Label>
                                <Input type="number" value={config?.descriptionFontSize || 16} onChange={(e) => onChange({ descriptionFontSize: parseInt(e.target.value, 10) })} />
                            </div>
                            <div>
                                <Label className="text-xs">Color</Label>
                                <ColorPicker color={config?.descriptionColor || '#776e65'} onChange={(descriptionColor) => onChange({ descriptionColor })} />
                            </div>
                            <div>
                                <Label className="text-xs">Text Alignment</Label>
                                <Select value={config?.descriptionTextAlign || 'left'} onValueChange={(descriptionTextAlign) => onChange({ descriptionTextAlign })}>
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Select alignment" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="left">Left</SelectItem>
                                        <SelectItem value="center">Center</SelectItem>
                                        <SelectItem value="right">Right</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
