import React from 'react'
import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { Label } from '@repo/shared/components/ui/label'
import { Switch } from '@repo/shared/components/ui/switch'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { useConfigKeyDefinition } from '@repo/shared-game-utils/hooks/useConfigType'

interface EmptyTile2048EditorProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
}

export function EmptyTile2048Editor({ configKey, config, onChange }: EmptyTile2048EditorProps) {
    const definition = useConfigKeyDefinition(configKey)

    return (
        <div className="space-y-4">
            <Label>
                {definition?.name ?? configKey.toString()}{' '}
                {definition?.width && definition?.height && (
                    <span className="ms-[2px] text-sm text-muted-foreground mb-2">
                        ({definition.width}x{definition.height}px)
                    </span>
                )}
            </Label>

            <div>
                <Label className="mb-2 block">Background Image</Label>
                <AssetPicker
                    exactSize={definition?.width && definition?.height ? { width: definition.width, height: definition.height } : undefined}
                    onSelect={(assetId) =>
                        onChange({
                            [configKey]: {
                                ...config[configKey],
                                asset: { assetId },
                            },
                        })
                    }
                    assetUrl={config[configKey] as any}
                    extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg']}
                />
            </div>

            <div>
                <div className="flex items-center space-x-2 mb-2">
                    <Label className="flex-grow">Background Color</Label>
                    <Switch
                        checked={(config[configKey] as any)?.useBackgroundColor !== false}
                        onCheckedChange={(useBackgroundColor) =>
                            onChange({
                                [configKey]: { ...config[configKey], useBackgroundColor },
                            })
                        }
                    />
                </div>
                <div className={`transition-opacity ${(config[configKey] as any)?.useBackgroundColor !== false ? 'opacity-100' : 'opacity-40'}`}>
                    <ColorPicker
                        color={(config[configKey] as any)?.fill || '#ffffff'}
                        onChange={(backgroundColor) =>
                            onChange({
                                [configKey]: {
                                    ...config[configKey],
                                    fill: backgroundColor,
                                },
                            })
                        }
                    />
                </div>
            </div>
        </div>
    )
} 