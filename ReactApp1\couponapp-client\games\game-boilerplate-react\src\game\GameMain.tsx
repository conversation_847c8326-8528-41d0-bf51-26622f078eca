import { TryAgainScreen, RewardScreen, OutOfLivesScreen } from './Scenes'
import { GameRuntimeComponentProps } from '@repo/shared/lib/game/game'
import { useCallback, useEffect, useRef, useState } from 'react'
import { gameEvents } from '@repo/shared/lib/campaign/eventEmitter'
import { useGameAtom } from '@repo/shared/lib/atoms/atom-extensions'
import { defaultGameConfig, ReactGameConfig } from '../types/config'
import { GameScreenId } from '../types/screen'
import { useGameRewards } from '@repo/shared/lib/game/useGameRewards'
import { GameSoundSwitch } from '@repo/shared-game-utils/components/GameSoundSwitch'
import { CounterElement } from '@repo/shared-game-utils/components/CounterElement'
import { GameContext, useGame } from '@repo/shared-game-utils/hooks/useGame'
import { usePreloadImage } from '@repo/shared-game-utils/hooks/usePreloadImage'
import { useMusic } from '@repo/shared-game-utils/hooks/useSounds'
import { getBackgroundStyle } from '@repo/shared-game-utils/utils/gameStyleUtils'
import { makeElementsInteractive } from '@repo/shared-game-utils/utils/previewInteractive'

export type GameRuntimeProps = GameRuntimeComponentProps<ReactGameConfig> & {
    currentScreenId?: GameScreenId
    initialGameScreenChecked: React.MutableRefObject<boolean>


    
}

export default function MainGame(props: GameRuntimeProps) {
    const [currentScreenId, setCurrentScreenId] = useState<GameScreenId>(props.currentScreenId || 'main')
    const initialGameScreenChecked = useRef(false)

    // Create an enhanced context with both props and screen state
    const contextValue = {
        ...props,
        currentScreenId,
        setCurrentScreenId,
        initialGameScreenChecked,
        defaultConfig: defaultGameConfig,
    }

    useEffect(() => {
        if (!props.isPreview) return
        setCurrentScreenId(props.currentScreenId || 'main')
    }, [props.currentScreenId, props.isPreview])

    if(!props.config) {
        console.log("Config not yet there. Waiting...")
        return <>Loading</>
    }

    return (
        <GameContext.Provider value={contextValue}>
            <GameContent />
        </GameContext.Provider>
    )
}

const useGameState = () => {
    const { config, widgetId } = useGame<ReactGameConfig>()
    const initialLivesCount = config.gameEndHandler?.useLives ? config.gameEndHandler?.livesCount || 3 : Number.MAX_SAFE_INTEGER

    const [lives, setLivesCount] = useGameAtom(widgetId, 'livesCount', initialLivesCount)
    const [score, setScore] = useGameAtom(widgetId, 'score', 0)
    const [bestScore, setBestScore] = useGameAtom(widgetId, 'bestScore', 0)
    const [attemptsTaken, setAttemptsTaken] = useGameAtom(widgetId, 'attemptsTaken', 0)

    return {
        lives,
        score,
        bestScore,
        attemptsTaken,
        setLivesCount,
        setScore,
        setBestScore,
        setAttemptsTaken,
    }
}

function usePrepareAssets() {
    console.log("Prepare assets")
    const { config } = useGame<ReactGameConfig>()

    const [loadedElementsCount, setLoadedElementsCount] = useState(0)
    const elementsToLoad = 5
    const loadPercentage = Math.round((loadedElementsCount / elementsToLoad) * 100)

    function appendLoadedElementCount() {
        setLoadedElementsCount((prev) => prev + 1)
    }

    // Preload sounds
    useMusic(config.backgroundMusic, false)
    useMusic(config.gameOverSound, false)
    useMusic(config.winSound, false)

    // Preload images
    usePreloadImage(config.gameOverOverlay?.asset, appendLoadedElementCount)
    usePreloadImage(config.rewardOverlay?.asset, appendLoadedElementCount)
    usePreloadImage(config.outOfLivesOverlay?.asset, appendLoadedElementCount)
    usePreloadImage(config.gameSoundSwitch?.onAsset, appendLoadedElementCount)
    usePreloadImage(config.gameSoundSwitch?.offAsset, appendLoadedElementCount)

    return { loadPercentage }
}

function GameContent() {
    const { config, widgetId, resolveAssetUrl, isPreview, currentScreenId, setCurrentScreenId, onGameAssetSelect } = useGame<ReactGameConfig>()
    const { bestScore } = useGameState()

    // Preload assets
    const { loadPercentage } = usePrepareAssets()

    useMusic(config.backgroundMusic, true, true)

    // Create a ref for the container
    const containerRef = useRef<HTMLDivElement>(null)

    // // Function to handle try again button click
    const handleTryAgainClick = useCallback(() => {
        if (isPreview) return

        setCurrentScreenId('main')
    }, [isPreview, setCurrentScreenId])

    // Function to handle reward button click
    const handleRewardButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinishedWithReward', {
            score: bestScore,
            widgetId: widgetId,
        })
    }, [isPreview, bestScore, widgetId])

    // // Function to handle out of lives button click
    const handleOutOfLivesButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinishedNoReward', {
            score: bestScore,
            widgetId: widgetId,
        })
    }, [isPreview, bestScore, widgetId, setCurrentScreenId])

    useEffect(() => {
        if (!isPreview) return
        if(loadPercentage < 100) return

        makeElementsInteractive(onGameAssetSelect)
    }, [currentScreenId, isPreview, loadPercentage])

    if(loadPercentage < 100) {
        return (
            <div className="flex flex-col items-center justify-center h-full w-full">
                <div className="text-xl mb-4">Loading: {loadPercentage}%</div>
                <div className="w-64 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                        className="h-full bg-blue-500 transition-all duration-200 ease-linear"
                        style={{ width: `${loadPercentage}%` }}
                    ></div>
                </div>
            </div>
        )
    }

    return (
        <div
            data-game-widget-id={widgetId}
            ref={containerRef}
            className="flex flex-col items-center justify-center h-full w-full touch-none select-none overscroll-none p-4"
            style={getBackgroundStyle(config.mainBackground, resolveAssetUrl)}
        >
            <div className="relative w-full h-full text-black">
                {currentScreenId === 'main' && <MainGameScreen />}
                {currentScreenId === 'try-again' && <TryAgainScreen onButtonClick={handleTryAgainClick} />}
                {currentScreenId === 'claim-reward' && <RewardScreen onButtonClick={handleRewardButtonClick} />}
                {currentScreenId === 'out-of-lives' && <OutOfLivesScreen onButtonClick={handleOutOfLivesButtonClick} />}
            </div>
        </div>
    )
}

function MainGameScreen() {
    const { initialGameScreenChecked, config, setCurrentScreenId, isPreview, widgetId } = useGame<ReactGameConfig>()
    const { lives, score, bestScore, setLivesCount, setScore, setBestScore, attemptsTaken, setAttemptsTaken } = useGameState()
    const { pickReward, hasWonReward } = useGameRewards({ gameWidgetId: widgetId, attempt: attemptsTaken })

    const rewardsEnabled = config.gameRewardsHandler?.rewardsEnabled || false
    const useLives = config.gameEndHandler?.useLives

    const checkInitialGameScreen = useCallback(
        (lives: number) => {
            if (isPreview) return

            if (rewardsEnabled && hasWonReward) {
                setCurrentScreenId('claim-reward')
                return
            }

            // Handle lives system
            if (useLives && lives <= 0) {
                setCurrentScreenId('out-of-lives')
                return
            }
        },
        [isPreview, rewardsEnabled, hasWonReward, useLives, lives, setCurrentScreenId]
    )

    const handleLoseLife = useCallback(async () => {
        if (isPreview) return

        // Increment attempts taken
        setAttemptsTaken((prev: number) => prev + 1)

        // Decrement lives if lives system is enabled
        setLivesCount(lives - 1)

        // Handle rewards if enabled
        if (rewardsEnabled) {
            const pickResult = await pickReward()

            // If rewards enabled and won, show claim reward
            if (pickResult.hasWon) {
                setCurrentScreenId('claim-reward')
                return
            }
        }

        // If lives system is disabled, show try again screen
        if (!useLives) {
            setCurrentScreenId('try-again')
            return
        }

        // Handle lives system
        if (useLives && lives <= 1) {
            setCurrentScreenId('out-of-lives')
            return
        }

        setCurrentScreenId('try-again')
    }, [isPreview, lives, setLivesCount, setAttemptsTaken, rewardsEnabled, pickReward, useLives, setCurrentScreenId])

    // Function to add points to the score
    const handleAddScore = useCallback(() => {
        if (isPreview) return
        const points = 100
        const newScore = score + points
        setScore(newScore)

        if (newScore > bestScore) {
            setBestScore(newScore)
        }
    }, [score, bestScore, setScore, setBestScore, config.gameRewardsHandler])

    // Function to simulate losing the game
    const handleLoseGame = useCallback(async () => {
        if (isPreview) return

        // Increment attempts taken
        setAttemptsTaken((prev: number) => prev + 1)

        // Handle rewards if enabled
        if (rewardsEnabled) {
            const pickResult = await pickReward()

            // If rewards enabled and won, show claim reward
            if (pickResult.hasWon) {
                setCurrentScreenId('claim-reward')
                return
            }
        }

        setCurrentScreenId('try-again')
    }, [isPreview, setAttemptsTaken, rewardsEnabled, pickReward, setCurrentScreenId])

    // Function to reset score
    const handleResetScore = useCallback(() => {
        if (isPreview) return
        setScore(0)
        setLivesCount(3)
        setAttemptsTaken(0)
    }, [setScore, setLivesCount, setAttemptsTaken])

    useEffect(() => {
        if (isPreview) return
        if (initialGameScreenChecked.current) return
        console.log('checking initial game screen')
        initialGameScreenChecked.current = true
        checkInitialGameScreen(lives)
    }, [lives])

    return (
        <div className="game-content-area w-full h-full flex flex-col">
            {/* Top section with sound switch */}
            <div className="w-full flex">
                <div className={`w-full flex ${config.gameSoundSwitch?.alignment === 'center' ? 'justify-center' : config.gameSoundSwitch?.alignment === 'right' ? 'justify-end' : 'justify-start'} p-2`}
                >
                    <GameSoundSwitch config={config.gameSoundSwitch} dataConfigKey="gameSoundSwitch" />
                </div>
            </div>

            {/* Score element */}
            <CounterElement configKey="scoreStyle" style={config.scoreStyle}>
              {score}
            </CounterElement>
            {/* Debug Controls */}
            <div className="absolute bottom-4 left-4 right-4 flex flex-wrap gap-2 bg-black/20 p-2 rounded-md">
                <button className="px-2 py-1 bg-blue-500 text-white rounded text-sm" onClick={handleAddScore}>
                    Add Score +100
                </button>
                <button className="px-2 py-1 bg-yellow-500 text-white rounded text-sm" onClick={handleLoseLife}>
                    Lose Life ({lives})
                </button>
                <button className="px-2 py-1 bg-red-500 text-white rounded text-sm" onClick={handleLoseGame}>
                    Lose Game
                </button>
                <button className="px-2 py-1 bg-gray-500 text-white rounded text-sm" onClick={handleResetScore}>
                    Reset Score
                </button>
            </div>
            ...some other game stuff
        </div>
    )
}
