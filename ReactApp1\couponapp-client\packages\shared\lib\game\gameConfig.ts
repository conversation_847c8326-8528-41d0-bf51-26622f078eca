export class GameConfig {}

export type ConfigEditorType =
        | 'image'
        | 'spin-game-button'
        | 'image-background'
        | '2048-tile'
        | '2048-grid-background'
        | 'empty-2048-tile'
        | 'game-button'
        | 'audio'
        | 'font'
        | 'number'
        | 'lives-handler'
        | '2048-scoreboard-numbers-style'
        | 'text'
        | 'text-style'
        | 'spacing'
        | 'background'
        | 'scoreboard-numbers-style'
        | 'rewards-handler'
        | 'container'
        | 'reward-component-style'
        | 'scratch-overlay'
        | 'sound-switch'
        | 'memory-grid'
        | 'counter'
        | 'memory-card'
        | 'spin-wheel'
        | 'answer-tile'
        | 'question-area-container'
        | 'game-timer'
        | 'question-answer-settings'
        | 'timer-handler'

export type GameConfigKey = {
    width?: number
    height?: number
    name?: string
    configEditorType?:ConfigEditorType
    editorSettings?: any
}

// Keep track of metadata for decorated properties
const assetMetadata = new Map<string, GameConfigKey>()

// Cache for initialized class instances
const classInstanceCache = new Map<Function, object>()

export function gameConfigKey<T>(config: GameConfigKey = {}) {
    return function (target: undefined, context: ClassFieldDecoratorContext<T, any>) {
        return function (this: T, value: any) {
            console.log('Registering: ', this.constructor.name + '.' + context.name.toString())
            assetMetadata.set(this.constructor.name + '.' + context.name.toString(), {
                ...config,
            })

            return value
        }
    }
}

// Helper function to get asset dimensions
export function getConfigKeyDefinition<T extends object>(classType: new (...args: any[]) => T, propertyName: keyof any): GameConfigKey | undefined {
    loadClassTypeDecorators(classType)
    const fullPropertyName = classType.name + '.' + String(propertyName)
    return assetMetadata.get(fullPropertyName)
}

function loadClassTypeDecorators(classType: any) {
    let instance = classInstanceCache.get(classType)
    if (!instance) {
        instance = new classType()
        classInstanceCache.set(classType, instance)
    }
}