import React from 'react'
import { motion } from 'framer-motion'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { ReactGameConfig } from '../../types/config'
interface ScratchUnderlayProps {
    isPreview?: boolean
}

export const ScratchUnderlay: React.FC<ScratchUnderlayProps> = ({ isPreview }) => {
    const { config, resolveAssetUrl } = useGame<ReactGameConfig>()

    // Get the underlay image URL from config
    const underlayUrl = resolveAssetUrl(config.scratchUnderlay?.asset)

    return (
        <div
            data-editor-selectable-key="scratchUnderlay"
            className="relative overflow-hidden w-[300px] h-[300px] flex items-center justify-center"
            style={{
                borderRadius: config.scratchOverlay?.borderRadius ? `${config.scratchOverlay.borderRadius}px` : undefined,
                backgroundImage: `url(${underlayUrl})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
            }}
        ></div>
    )
}
