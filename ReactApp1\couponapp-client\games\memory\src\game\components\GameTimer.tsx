import React, { useEffect, useState } from 'react'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { useGameAtom } from '@repo/shared/lib/atoms/atom-extensions'
import { CounterElement } from '@repo/shared-game-utils/components/CounterElement'
import { ReactGameConfig } from '../../types/config'

interface GameTimerProps {
    onTimeExpired: () => void
}

export const GameTimer: React.FC<GameTimerProps> = ({ onTimeExpired }) => {
    const { config, widgetId, isPreview } = useGame<ReactGameConfig>()
    const { useTimer, timerDuration } = config.gameTimerHandler || { useTimer: false, timerDuration: 60 }
    
    // Store the timer state in an atom so it persists across renders
    const [timeRemaining, setTimeRemaining] = useGameAtom(widgetId, 'timeRemaining', timerDuration)
    const [isRunning, setIsRunning] = useGame<PERSON>tom(widgetId, 'timerRunning', false)
    const [isPaused, setIsPaused] = useGameAtom(widgetId, 'timerPaused', false)
    
    // Format the time as MM:SS
    const formatTime = (seconds: number): string => {
        const mins = Math.floor(seconds / 60)
        const secs = seconds % 60
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    
    // Start the timer
    const startTimer = () => {
        if (!isRunning && !isPaused) {
            setTimeRemaining(timerDuration)
        }
        setIsRunning(true)
        setIsPaused(false)
    }
    
    // Pause the timer
    const pauseTimer = () => {
        setIsPaused(true)
        setIsRunning(false)
    }
    
    // Reset the timer
    const resetTimer = () => {
        setTimeRemaining(timerDuration)
        setIsRunning(false)
        setIsPaused(false)
    }
    
    // Timer effect
    useEffect(() => {
        if (!useTimer || isPreview) return
        
        let interval: NodeJS.Timeout | null = null
        
        if (isRunning && !isPaused) {
            interval = setInterval(() => {
                setTimeRemaining((prevTime) => {
                    if (prevTime <= 1) {
                        // Time expired
                        clearInterval(interval as NodeJS.Timeout)
                        setIsRunning(false)
                        onTimeExpired()
                        return 0
                    }
                    return prevTime - 1
                })
            }, 1000)
        }
        
        return () => {
            if (interval) clearInterval(interval)
        }
    }, [isRunning, isPaused, useTimer, isPreview, onTimeExpired])
    
    // Don't render if timer is not enabled
    if (!useTimer && !isPreview) {
        return null
    }
    
    return (
        <CounterElement configKey="timerStyle" style={config.timerStyle}>
            {formatTime(timeRemaining)}
        </CounterElement>
    )
}

// Export timer controls for use in other components
export const useTimerControls = () => {
    const { config, widgetId } = useGame<ReactGameConfig>()
    const [, setTimeRemaining] = useGameAtom(widgetId, 'timeRemaining', config.gameTimerHandler?.timerDuration || 60)
    const [, setIsRunning] = useGameAtom(widgetId, 'timerRunning', false)
    const [, setIsPaused] = useGameAtom(widgetId, 'timerPaused', false)
    
    return {
        startTimer: () => {
            setIsRunning(true)
            setIsPaused(false)
        },
        pauseTimer: () => {
            setIsPaused(true)
            setIsRunning(false)
        },
        resetTimer: () => {
            setTimeRemaining(config.gameTimerHandler?.timerDuration || 60)
            setIsRunning(false)
            setIsPaused(false)
        }
    }
}
